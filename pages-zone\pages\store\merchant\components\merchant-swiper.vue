<template>
	<view class="merchant-swiper">
		<u-swiper :list="listBanner"></u-swiper>
	</view>
</template>

<script>
	export default {
		name: 'merchant-swiper',
		data() {
			return {
				listBanner:[]
			}
		},
		mounted() {
			this.getBannerList()
		},
		methods: {
			handleClickSwpier(index){
				console.log(index)
			},
			getBannerList() {
				this.$u.api.listBanner({
					isVisible: true
				}).then((res) => {
					if (res.code == 200) {
						this.listBanner = res.data;
						this.listBanner = this.listBanner.map((item) => {
							item.image = this.$imgUrl + item.imageInput;
							return item;
						});
						console.log(this.list,'轮播图')
					}
				});
			},
		}
	}
</script>

<style scoped lang="scss">
</style>
