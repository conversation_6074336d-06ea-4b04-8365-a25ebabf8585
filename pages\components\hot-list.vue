<template>
	<view class="service-container">
		<!-- 标题栏 -->
		<view class="header">
			<text class="title">{{hnage}}</text>
			<view class="more" @click="handleMore">
				<text>更多</text>
				<u-icon name="arrow-right" size="24"></u-icon>
			</view>
		</view>

		<!-- 服务列表 -->
		<view class="service-row">
			<view class="service-item" v-for="(item, index) in visibleData" :key="index" @click="handleItemClick(item)">
				<view class="content">
					<view class="pic">
						<u-image width="100%" height="140rpx" mode="aspectFill" :src="https+item.cover"></u-image>
					</view>
					<text class="name">{{ item.title }}</text>
					<text class="price">{{ item.amount }}元</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import UIcon from '@/pages-zone/uview-ui/components/u-icon/u-icon.vue'
	import UImage from '@/pages-zone/uview-ui/components/u-image/u-image.vue'
	import {
		HTTP_URL_PROD
	} from '@/api/config';
	export default {
		components: {
			UIcon,
			UImage
		},
		props: {
			data: {
				type: Array,
				default: () => []
			},
			hid: {
				type: String,
				default: ''
			},
			hnage: {
				type: String,
				default: ''
			},
			hurl: {
				type: String,
				default: ''
			}
		},
		computed: {
			visibleData() {
				return this.data.slice(0, 3); // 只显示前三条
			}
		},
		data() {
			return {
				https: HTTP_URL_PROD,
				btnStyle: {
					width: '160rpx',
					height: '56rpx',
					lineHeight: '56rpx',
					background: '#FFF2F0',
					color: '#FF4444',
					fontSize: '24rpx'
				}
			}
		},
		methods: {
			handleMore() {
				uni.navigateTo({
					url: this.hurl + '?name=' + this.hnage + '&id=' + this.hid
				})
			},
			handleItemClick(item) {
				uni.navigateTo({
					url: '/pages-mall/pages/indexdetails/detail?item=' + encodeURIComponent(JSON.stringify(item))
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
	.service-container {
		padding: 32rpx;
		background: #fff;

		.header {
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin-bottom: 32rpx;

			.title {
				font-size: 34rpx;
				font-weight: 600;
				color: #333;
			}

			.more {
				display: flex;
				align-items: center;
				color: #999;
				font-size: 26rpx;

				.u-icon {
					margin-left: 8rpx;
				}
			}
		}

		.service-row {
			display: flex;
			gap: 20rpx;
			overflow: hidden; // 隐藏超出部分

			.service-item {
				flex: 0 0 calc(33.333% - 14rpx); // 三个等宽项
				// background: #F8F8F8;
				border-radius: 16rpx;
				// padding: 24rpx;
				box-sizing: border-box;

				.content {
					display: flex;
					flex-direction: column;
					justify-content: space-between;
					height: 220rpx;

					.name {
						font-size: 28rpx;
						color: #333;
						line-height: 1.4;
					}

					.price {
						font-size: 28rpx;
						color: #FF4444;
						font-weight: 500;
					}
				}
			}
		}
	}

	.line-clamp-1 {
		display: -webkit-box;
		-webkit-line-clamp: 1;
		-webkit-box-orient: vertical;
		overflow: hidden;
	}
</style>