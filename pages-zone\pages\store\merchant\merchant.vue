<template>
	<view class="shop-merchant">
		<navbar title="本地商家"></navbar>
		<!-- 本地商家首页 -->
		<view class="home" v-show="navbarIndex==0">
			<merchant-swiper />
			<merchant-category />
			<merchant-list title="本地到家" @limit='getShopLimit' :search="true" :shopList="shopList" />
			<view style="	height: 120rpx;">

			</view>
		</view>
		<!-- 本地商家分类 -->
		<view class="category" v-show="navbarIndex==1">
			<view class="category-list">
				<view class="category-item" @click="handleCategory(item.id)" :class="{active:active==item.id}"
					v-for="item in categoryList" :key="item.id">
					<view class="img">
						<img style="width: 100%;height: 100%;" :src="baseUrl + item.imgUrl" alt="" />
					</view>
					<view class="title">
						{{item.name}}
					</view>
				</view>
			</view>

			<view class="store" :style="'height:' + (height - 123) + 'px'">
				<view style="height: 100%;">
					<view class="sidebar">
						<view class="sidebar-item" :class="{active:active==item.id}" v-for="item in categoryList"
							:key='item.id' @click="handleCategory(item.id)">
							{{item.name}}
						</view>
					</view>
					<view class="shop-list" :style="'height:' + (height - 223) + 'px'">
						<view class="search">
							<view style="display: inline-block;" class="text" :class='{active:searchActive===null}'
								@click="()=>{searchActive=null;arrow=!arrow}">
								<text>默认</text>
							</view>
							<text class="text" :class='{active:searchActive===1}' @click='searchActive=1'>销售量</text>
							<text class="text" :class='{active:searchActive===2}' @click="searchActive=2">好评</text>
						</view>
						<view class="title" style="font-weight: 800;font-size: 40rpx;margin: 20rpx;">
							{{categoryList.find(i=>(i.id==active)).name}}
						</view>
						<view class="shop-list-item" v-for="item in shopCategoryList" :key='item.shop.id'
							@click="handleShop(item.shop.id)">
							<view class="shop-header">
								<view class="shop-logo">
									<img class="logo-img" :src="baseUrl + item.shop.logoImg" alt="商户logo" />
									<view class="quality-badge" v-if="item.shop.shopStar >= 4.5">
										<text class="badge-text">优质商家</text>
									</view>
								</view>

								<view class="shop-info">
									<view class="shop-name">{{item.shop.name}}</view>
									<view class="shop-rating">
										<view class="rating-container">
											<text class="rating-icon">⭐</text>
											<text class="rating-score">{{item.shop.shopStar || 0}}分</text>
										</view>
										<view class="sales-container">
											<text class="sales-icon">🔥</text>
											<text class="sales-text">月售{{item.shop.order > 1000 ? '1000+' : (item.shop.order || 0)}}</text>
										</view>
									</view>

									<view class="delivery-info">
										<view class="delivery-item">
											<text class="delivery-icon">💰</text>
											<text class="delivery-text">起送¥{{item.shop.qiSong || 0}}</text>
										</view>
										<view class="delivery-item">
											<text class="delivery-icon">🚚</text>
											<text class="delivery-text">配送约¥{{item.shop.peiSong || 0}}</text>
										</view>
									</view>
								</view>
							</view>

							<view class="products-section" v-if="item.storeList && item.storeList.length > 0">
								<view class="products-title">
									<text class="title-text">热门商品</text>
									<text class="view-more">查看更多 ></text>
								</view>
								<view class="products-grid">
									<view class="product-item" v-for="store in item.storeList.slice(0, 4)" :key='store.id'>
										<view class="product-image-container">
											<img class="product-image" :src="baseUrl + store.img" alt="商品图片" />
											<view class="product-overlay">
												<text class="add-to-cart">+</text>
											</view>
										</view>
										<view class="product-info">
											<view class="product-name">{{store.name}}</view>
											<view class="product-price">¥{{store.price}}</view>
										</view>
									</view>
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>

		<view v-if="navbarIndex==2" class="order">
			<Tabs @change="handleTabsChange" :listData="orderNavBarList"></Tabs>
			<view class="order-item" @click="toDetail(item.orderNo)" v-for="item in orderList" :key="item.id">
				<view class="top">
					<view class="left">
						<view class="shopName">
							{{key(item.orderDetailList)}}
						</view>
						<view class="time" v-if="length(item.orderDetailList)>1">
							下单时间: {{item.createTime}}
						</view>
					</view>
					<view class="right">
						{{formatStatus(item.orderStatus, item.deliveryStatus)}}
					</view>
				</view>
				<view class="main">
					<view class="store-item" v-if="length(item.orderDetailList)==1"
						v-for="store of value(item.orderDetailList)" :key="store.id">
						<view class="img">
							<img :src="baseUrl + store.img" alt="" />
						</view>
						<view class="detail">
							<view class="name">
								{{store.name}}
							</view>
							<view class="time">
								下单时间：{{item.createTime}}
							</view>
							<view class="total">
								总价：<text style="color: red;">￥{{item.totalAmount}}</text>
							</view>
						</view>
					</view>
					<view class="store-item_" v-if="length(item.orderDetailList)>1">
						<view class="img" v-for="store in value(item.orderDetailList)" :key="store.id">
							<img :src="baseUrl + store.img" alt="" />
							<text>{{store.name}}</text>
						</view>
						<view class="detail">
							<view class="total">
								￥{{item.totalAmount}}
							</view>
							<view class="count">
								共{{length(item.orderDetailList)}}件
							</view>
						</view>
						<view class="icon">
							<u-icon name=""></u-icon>
						</view>
					</view>
				</view>
				<view v-if="item.orderStatus == 0" class="btn">
					<view class="zf">支付</view>
					<view @click.stop="qxOrder(item.orderNo)" class="qx">取消</view>
				</view>
				<view class="btn" v-if="item.deliveryStatus !== 3 && item.orderStatus == 1">
					<view @click.stop="shOrder(item.storeShopId, item.orderNo, item.deliveryFlag)" class="sh">确认收货</view>
				</view>
			</view>
			<view style="	height: 120rpx;">

			</view>
		</view>
		<merchant-cart />
		<merchant-navbar :navbarIndex="navbarIndex" @change='handleNavbarChange' />
	</view>
</template>

<script>
	import MerchantCategory from './components/merchant-category.vue';
	import MerchantList from './components/merchant-list.vue';
	import MerchantSwiper from './components/merchant-swiper.vue';
	import NavbarCity from '@/components/navbar/navbar-city.vue';
	import MerchantCart from './components/merchant-cart.vue';
	import MerchantNavbar from './components/merchant-navbar.vue';
	import Tabs from '@/components/tabs.vue';
	import {
		baseUrl
	} from '@/api/config';
	// tab数据
	import {
		orderNavBar
	} from '@/static/order/tabs';
	export default {
		data() {
			return {
				shopList: [],
				navbarIndex: 0,
				baseUrl,
				orderNavBarList: orderNavBar,
				orderList: [],
				categoryList: [],
				shopCategoryList: [],
				active: 0,
				height: 0,
				arrow: true,
				searchActive: null,
				tabsIndex: 0,
			}
		},
		components: {
			NavbarCity,
			MerchantCategory,
			MerchantList,
			MerchantSwiper,
			MerchantCart,
			MerchantNavbar,
			Tabs,
		},
		onLoad(opt) {
			if (opt.navbarIndex) {
				this.navbarIndex = opt.navbarIndex
			}
		},
		watch:{
			searchActive:{
				handler(){
					this.getShopLimit(this.searchActive)
				}
			}
		},
		computed: {
			roles() {
				// 逐层判断是否存在，不存在则返回空数组
				if (!this.$store.state.user || !this.$store.state.user.userInfo || !this.$store.state.user.userInfo
					.roles) {
					return [];
				}
				// 存在则正常映射
				return this.$store.state.user.userInfo.roles.map(item => item.roleKey);
			},
			isCommon() {
				// 基于处理后的roles判断，即使未登录也会是安全的空数组
				return this.roles.includes('common');
			}
		},
		mounted() {
			this.getShop()
			this.getCategory()
			uni.getSystemInfo().then(res => {
				this.height = res[1].windowHeight
			})
		},
		onShow() {
			this.getOrder()
		},
		methods: {
			length(obj) {
				let keys = Object.keys(obj)
				return obj[keys[0]]?.length
			},
			key(obj) {
				let keys = Object.keys(obj)
				return keys[0]
			},
			value(obj) {
				let values = Object.values(obj)
				return values[0]
			},
			formatStatus(orderStatus, deliveryStatus) {
				if (orderStatus == 0) {
					return '待支付'
				} else if (orderStatus == 1) {
					if (deliveryStatus == 3) return '已收货'
					else return '已支付'
				} else if (status == 2) {
					return '已取消'
				} else if (status == 3) {
					return '已退款'
				}
			},
			getShop() {
				this.$u.api.getShopAndStore(0).then(res => {
					this.shopList = res.data.list
				})
			},
			getShopLimit(orderBys){
				this.$u.api.getShopAndStore(orderBys).then(res => {
					this.shopList = res.data.list
				})
			},
			getStore() {
				this.$u.api.getShopAndStoreByCategory(this.active,null).then(res => {
					this.shopCategoryList = res.data.list
				})
			},
			getCategory() {
				this.$u.api.getShopCategory().then(res => {
					this.categoryList = res.data.list
					this.active = this.categoryList[0].id
					this.getStore()
				})
			},
			getOrder(status, type) {
				if (this.$store.state.user.token) {
					if (this.isCommon) {
						this.$u.api.getMyShopList().then(res => {
							let shopId = res.data.list[0].id
							this.$u.api.getOrderList(status, type, shopId, true).then(res => {
								this.orderList = res.data.list
							})
						})
					} else {
						this.$u.api.getOrderList(status, type).then(res => {
							this.orderList = res.data.list
						})
					}
				}

			},
			handleNavbarChange(navbarIndex) {
				this.navbarIndex = navbarIndex
			},
			handleShop(id) {
				console.log(id)
				uni.navigateTo({
					url: '/pages-zone/pages/store/shop/shop?id=' + id,
				})
			},
			toDetail(orderNo) {
				uni.navigateTo({
					url: '/pages-zone/pages/store/merchant/order-detail?orderNo=' + orderNo
				})
			},
			handleTabsChange(tabsIndex) {
				this.tabsIndex = tabsIndex
				if ([1, 6, 7, 8].includes(tabsIndex)) {
					let status
					switch (tabsIndex) {
						case 1: {
							status = 0
							break
						}
						case 6: {
							status = 1
							break
						}
						case 7: {
							status = 2
							break
						}
						case 8: {
							status = 3
							break
						}
					}
					this.getOrder(status)
				} else if ([2, 3, 4, 5].includes(tabsIndex)) {
					let status
					switch (tabsIndex) {
						case 2: {
							status = 0
							break
						}
						case 3: {
							status = 1
							break
						}
						case 4: {
							status = 2
							break
						}
						case 5: {
							status = 3
							break
						}
					}
					this.getOrder(status, 0)
				} else {
					this.getOrder()
				}
			},
			handleCategory(id) {
				this.active = id
				this.getStore()
			},
			qxOrder(orderNo) {
				this.$u.api.cancelOrder(orderNo).then(() => {
					this.handleTabsChange(this.tabsIndex)
				})
			},
			shOrder(storeShopId, orderNo, deliveryFlag) {
				this.$u.api.updateOrder(orderNo, null, 3).then(() => {
					uni.navigateTo({
						url: '/pages-zone/pages/store/comment/comment?storeShopId=' + storeShopId + '&deliveryFlag=' + deliveryFlag
					})
				})
			}
		}
	}
</script>

<style scoped lang="scss">
	.home {
		padding: 30rpx;
	}

	.category {
		.category-list {
			position: fixed;
			top: 84px;
			width: 100%;
			z-index: 1001;
			height: 180rpx;
			background: #fff;
			display: flex;
			overflow: auto;
			padding: 20rpx;

			.category-item {
				margin: 0 30rpx;
				display: flex;
				flex-direction: column;
				align-items: center;

				.img {
					border-radius: 20rpx;
					width: 100rpx;
					height: 100rpx;

					img {
						border-radius: 20rpx;
					}
				}

				.title {
					font-weight: 700;
					width: 125rpx;
					text-align: center;
				}

				&.active {
					.img {
						border: #f37b0c solid 4rpx;
					}

					.title {
						color: #fff;
						background: #f47500;
						border-radius: 10rpx;
						padding: 4rpx;
					}
				}
			}
		}

		.store {
			padding-top: 180rpx;

			.sidebar {
				position: fixed;
				width: 180rpx;
				background-color: #f5f5f5;
				display: flex;
				flex-direction: column;
				align-items: center;

				.sidebar-item {
					width: 100%;
					text-align: center;
					height: 100rpx;
					line-height: 100rpx;
					font-size: 36rpx;

					&.active {
						background-color: #fff;
						color: #f39800;
					}
				}
			}

			.store-list {
				background-color: #fff;

				.store-item {
					display: flex;
					padding: 20rpx;


					img {
						width: 36%;
						height: 180rpx;
					}

					.detail {
						width: 44%;
						padding: 10rpx;

						.name {
							font-size: 40rpx;
						}

						.sell {
							color: #959595;
						}
					}

					.cart {
						width: 20%;
						display: flex;
						align-items: flex-end;
						justify-content: center;
					}
				}
			}



			.shop-list {
				margin-left: 180rpx;
				background: #fff;

				.search {
					height: 80rpx;
					align-items: center;
					display: flex;
					font-size: 34rpx;
					margin-top: 20rpx;

					.text {
						margin: 0 30rpx;

						&.active {
							color: #f39800;
						}
					}
				}

				.shop-list-item {
					background: #fff;
					margin: 24rpx 0;
					border-radius: 32rpx;
					padding: 0;
					box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
					border: 1rpx solid #F0F0F0;
					overflow: hidden;
					transition: all 0.3s ease;

					&:hover {
						transform: translateY(-4rpx);
						box-shadow: 0 16rpx 48rpx rgba(0, 0, 0, 0.12);
					}

					.shop-header {
						padding: 32rpx;
						background: linear-gradient(135deg, #f18e6e 0%, #ffd8cb 100%);
						position: relative;
						overflow: hidden;

						&::before {
							content: '';
							position: absolute;
							top: -50%;
							right: -50%;
							width: 200%;
							height: 200%;
							background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
							animation: shimmer 3s ease-in-out infinite;
						}

						.shop-logo {
							position: relative;
							display: flex;
							align-items: center;
							margin-bottom: 24rpx;

							.logo-img {
								width: 120rpx;
								height: 120rpx;
								border-radius: 24rpx;
								border: 4rpx solid rgba(255, 255, 255, 0.3);
								box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.2);
								margin-right: 24rpx;
							}

							.quality-badge {
								background: linear-gradient(135deg, #FF6B6B, #FF8E53);
								padding: 8rpx 16rpx;
								border-radius: 20rpx;
								box-shadow: 0 4rpx 12rpx rgba(255, 107, 107, 0.3);

								.badge-text {
									color: #fff;
									font-size: 24rpx;
									font-weight: 600;
								}
							}
						}

						.shop-info {
							.shop-name {
								font-size: 44rpx;
								font-weight: 700;
								color: #fff;
								margin-bottom: 16rpx;
								text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
							}

							.shop-rating {
								display: flex;
								align-items: center;
								gap: 24rpx;
								margin-bottom: 20rpx;

								.rating-container,
								.sales-container {
									display: flex;
									align-items: center;
									background: rgba(255, 255, 255, 0.2);
									padding: 12rpx 20rpx;
									border-radius: 20rpx;
									backdrop-filter: blur(10rpx);

									.rating-icon,
									.sales-icon {
										font-size: 24rpx;
										margin-right: 8rpx;
									}

									.rating-score,
									.sales-text {
										color: #fff;
										font-size: 28rpx;
										font-weight: 600;
									}
								}
							}

							.delivery-info {
								display: flex;
								gap: 20rpx;

								.delivery-item {
									display: flex;
									align-items: center;
									background: rgba(255, 255, 255, 0.15);
									padding: 12rpx 16rpx;
									border-radius: 16rpx;
									backdrop-filter: blur(10rpx);

									.delivery-icon {
										font-size: 24rpx;
										margin-right: 8rpx;
									}

									.delivery-text {
										color: rgba(255, 255, 255, 0.9);
										font-size: 26rpx;
										font-weight: 500;
									}
								}
							}
						}
					}

					.products-section {
						padding: 32rpx;

						.products-title {
							display: flex;
							justify-content: space-between;
							align-items: center;
							margin-bottom: 24rpx;

							.title-text {
								font-size: 32rpx;
								font-weight: 600;
								color: #2C3E50;
							}

							.view-more {
								font-size: 26rpx;
								color: #FF6B6B;
								font-weight: 500;
							}
						}

						.products-grid {
							display: grid;
							grid-template-columns: repeat(2, 1fr);
							gap: 20rpx;

							.product-item {
								background: #F8F9FA;
								border-radius: 20rpx;
								padding: 16rpx;
								transition: all 0.3s ease;

								&:hover {
									background: #E9ECEF;
									transform: translateY(-2rpx);
								}

								.product-image-container {
									position: relative;
									margin-bottom: 12rpx;

									.product-image {
										width: 100%;
										height: 140rpx;
										border-radius: 16rpx;
										object-fit: cover;
									}

									.product-overlay {
										position: absolute;
										top: 8rpx;
										right: 8rpx;
										width: 48rpx;
										height: 48rpx;
										background: linear-gradient(135deg, #FF6B6B, #FF8E53);
										border-radius: 50%;
										display: flex;
										align-items: center;
										justify-content: center;
										opacity: 0;
										transition: opacity 0.3s ease;

										.add-to-cart {
											color: #fff;
											font-size: 32rpx;
											font-weight: 600;
										}
									}

									&:hover .product-overlay {
										opacity: 1;
									}
								}

								.product-info {
									.product-name {
										font-size: 26rpx;
										color: #2C3E50;
										font-weight: 500;
										margin-bottom: 8rpx;
										overflow: hidden;
										text-overflow: ellipsis;
										white-space: nowrap;
									}

									.product-price {
										font-size: 28rpx;
										color: #FF6B6B;
										font-weight: 700;
									}
								}
							}
						}
					}
				}
			}
		}
	}

	.order {
		height: 100vh;
		background: #fff;

		.order-item {
			background: #fff;
			border-radius: 20rpx;
			box-shadow: #eae7ee 0 0 10rpx 10rpx;
			width: 90%;
			margin: 0 auto;
			margin-bottom: 30rpx;
			padding: 30rpx 30rpx 0rpx;

			.top {
				display: flex;
				justify-content: space-between;
				border-bottom: 1px solid #e7e7e7;
				padding-bottom: 20rpx;
				align-items: center;

				.shopName {
					font-size: 48rpx;
					font-weight: 800;
					margin-bottom: 20rpx;
				}

				.right {
					font-size: 36rpx;
					color: #f47500;
					font-weight: 700;
				}
			}

			.main {
				border-bottom: 1rpx solid #e2e2e2;
				padding: 20rpx 0;

				.store-item {
					display: grid;
					grid-template-columns: 25% 75%;

					.img {
						img {
							width: 100%;
							height: 150rpx;
						}
					}

					.detail {
						padding: 20rpx;

						.name {
							font-weight: 800;
						}
					}
				}

				.store-item_ {
					display: flex;
					align-items: center;

					.img {
						width: 200rpx;

						img {
							width: 100%;
							height: 200rpx;
						}
					}

					.total {
						color: red;
						font-size: 40rpx;
					}
				}
			}

			.btn {
				display: flex;
				height: 150rpx;
				flex-direction: row-reverse;
				padding: 20rpx 20rpx;

				view {
					border: 1rpx solid #f7a151;
					padding: 0 20rpx;
					display: inline-block;
					height: 80rpx;
					line-height: 80rpx;
					margin-left: 20rpx;
					border-radius: 10rpx;
					font-size: 38rpx;
					font-weight: 800;
					color: #fca115;
				}

				.zf {
					background: #fca115;
					color: #fff;
				}
			}
		}
	}
</style>