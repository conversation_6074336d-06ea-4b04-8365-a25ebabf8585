<template>
	<view class="page">
		<!-- 一般导航栏 -->
		<Navbar title="订单详情"></Navbar>
		<!-- 订单状态 -->
		<OrderStatus :type="orderInfo.status" :data="orderInfo"></OrderStatus>
		<!-- 地址 -->
		<view class="address" @click="addressEvent">
			<view style="font-size: 18px;font-weight: bold;">
				{{objMainAddress.consignee||''}}
			</view>
			<view style="color: #999;">
				{{objMainAddress.region||''}}
			</view>
		</view>
		<!-- 商品列表 -->
		<view class="goods">
			<TitleOperate :title="'共' + goodsListw.length + '件商品'" titleSize="30rpx"></TitleOperate>
			<view class="list">
				<SubmitGoodsCard v-for="(goods, index) in goodsListw" :key="index" :data="goods"
					:showBorderBottom="index != goodsListw.length - 1"></SubmitGoodsCard>
			</view>
		</view>
		<!-- 评价展示区域 -->
		<view class="evaluation" v-if="Object.keys(evaluate).length > 0">
			<TitleOperate title="用户评价" titleSize="30rpx"></TitleOperate>
			<view class="evaluation-inner">
				<!-- 评分星星 -->
				<view class="star-container">
					<u-rate v-model="evaluate.serviceSatisfactionLevel" disabled active-color="#FFB800"
						inactive-color="#EEEEEE" size="40rpx" allow-half></u-rate>
					<view class="score-text">{{ evaluate.serviceSatisfactionLevel }}分</view>
				</view>

				<!-- 评价内容 -->
				<view class="evaluation-content">
					{{ evaluate.evaluate }}
				</view>

				<!-- 评价图片 -->
				<view class="evaluation-images" v-if="evaluate.picture && evaluate.picture.trim()">
					<view class="image-item" v-for="(img, index) in formatImages(evaluate.picture)" :key="index"
						@click="previewImage(img, formatImages(evaluate.picture))">
						<image :src="img" mode="aspectFill"></image>
					</view>
				</view>
			</view>
		</view>
		<!-- 订单信息 -->
		<view class="order-info">
			<TitleOperate title="订单信息" titleSize="30rpx"></TitleOperate>
			<view class="inner">
				<LineInfoOperate :ops="orderInfoOps"></LineInfoOperate>
			</view>
		</view>
		<!-- 操作按钮 -->
		<OrderOperate :data="orderInfo" @change="changeqxx"></OrderOperate>
	</view>
</template>

<script>
	import {
		HTTP_URL_PROD
	} from '@/api/config';
	import OrderStatus from '@/pages-mall/components/order/order-status.vue';
	import AddressCard from '@/pages/mine/components/address-card.vue';
	import TitleOperate from '@/components/title-operate.vue';
	import SubmitGoodsCard from '@/pages-mall/components/order/submit-goods-card.vue';
	import LineInfoOperate from '@/pages-mall/components/line-info-operate.vue';
	import OrderOperate from '@/pages-mall/components/order/order-operate.vue';

	export default {
		components: {
			OrderStatus,
			AddressCard,
			SubmitGoodsCard,
			TitleOperate,
			LineInfoOperate,
			OrderOperate
		},
		data() {
			return {
				// 订单信息
				orderInfo: {},
				// 评价信息
				evaluate: {},
				// 默认地址
				objMainAddress: {},
				// 商品列表
				goodsList: [],
				goodsListw: [],
				dizhi: [],
				// 订单信息配置项
				orderInfoOps: [{
						label: '下单时间',
						value: null,
						operate: '',
					},
					{
						label: '备注',
						value: null,
						operate: '',
					}
				]
			};
		},
		onLoad(ops) {
			let userInfo = JSON.parse(decodeURIComponent(ops.item));
			console.log(userInfo, '订单详情')
			this.goodsList = userInfo.communityService.communityServiceSpecifications;

			// 处理评价信息
			if (userInfo.serviceOrderEvaluates && userInfo.serviceOrderEvaluates.length > 0) {
				this.evaluate = userInfo.serviceOrderEvaluates[0];
			}

			// 处理商品列表
			const matchedItem = userInfo.communityService.communityServiceSpecifications.find(
				item => item.id === userInfo.communityServiceSpecificationsId
			);
			let goodsList = [];
			if (matchedItem) {
				goodsList = [{
					name: matchedItem.title,
					num: parseInt(userInfo.num, 10) || 0,
					sku: matchedItem.title,
					originalPrice: userInfo.currentPrice,
					picture: matchedItem.picture,
					checked: false
				}];
			}
			this.goodsListw = goodsList;

			// 处理订单信息
			this.orderInfo.price = userInfo.currentPrice;
			this.orderInfo.status = userInfo.status;
			this.orderInfo.id = userInfo.id;
			this.orderInfoOps[0].value = userInfo.createTime;
			this.orderInfoOps[1].value = userInfo.content;

			// 获取地址信息
			this.AddressList(userInfo.addressId);
		},
		methods: {
			changeqxx(e) {
				this.$eventBus.$emit('order-status-changed', {
					orderId: this.orderInfo.id,
					newStatus: e
				});
				uni.navigateBack();
			},
			AddressList(addressId) {
				let data = {
					pageSize: 99999,
					pageNum: 1
				};
				this.$u.api.getAddressList(data).then(res => {
					this.dizhi = res.rows;
					const mainAddress = res.rows.find(item => item.id === addressId);
					this.objMainAddress = mainAddress || {};
				});
			},
			// 格式化图片地址（处理逗号分隔和拼接基础路径）
			formatImages(pictureStr) {
				if (!pictureStr) return [];
				// 分割字符串并过滤空值
				return pictureStr.split(',').map(img => {
					const trimmedImg = img.trim();
					// 拼接基础路径（如果图片地址不是完整URL）
					return trimmedImg.startsWith('http') ?
						trimmedImg :
						HTTP_URL_PROD + trimmedImg;
				}).filter(img => img); // 过滤空字符串
			},
			// 预览图片
			previewImage(currentImg, allImages) {
				uni.previewImage({
					current: currentImg,
					urls: allImages
				});
			},
			// 地址点击事件（预留）
			addressEvent() {
				// 可添加地址点击逻辑
			}
		}
	};
</script>

<style lang="scss" scoped>
	.page {
		padding-bottom: 120rpx;
		background-color: #F5F5F5;
	}

	.address {
		margin-top: 24rpx;
		padding: 24rpx 30rpx;
		background-color: $app-theme-bg-color;
	}

	.goods,
	.order-info,
	.evaluation {
		margin-bottom: 24rpx;
		background-color: $app-theme-bg-color;

		.list,
		.inner,
		.evaluation-inner {
			padding: 24rpx 30rpx;
		}
	}

	/* 评价区域样式 */
	.evaluation {
		.evaluation-inner {
			border-top: 1px solid #F5F5F5;
			padding-top: 24rpx;
		}

		.star-container {
			display: flex;
			align-items: center;
			margin-bottom: 24rpx;

			.score-text {
				margin-left: 16rpx;
				font-size: 28rpx;
				color: #FFB800;
				font-weight: 500;
			}
		}

		.evaluation-content {
			font-size: 30rpx;
			line-height: 1.6;
			color: #333333;
			margin-bottom: 24rpx;
			word-break: break-all;
		}

		.evaluation-images {
			display: flex;
			flex-wrap: wrap;
			gap: 16rpx;

			.image-item {
				width: 160rpx;
				height: 160rpx;
				border-radius: 8rpx;
				overflow: hidden;
				background-color: #F5F5F5;

				image {
					width: 100%;
					height: 100%;
					display: block;
				}
			}
		}
	}
</style>