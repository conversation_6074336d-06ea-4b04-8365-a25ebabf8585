<template>
	<view class="slot" :style="showBorderBottom ? '' : 'border-bottom:none'">
		<view class="info">
			<view class="item" @click="use">
				<text class="name">{{data.consignee}}</text> <text class="gender">({{data.sex ? "先生" : "女士"}})</text>
				<text class="phone">{{data.phone}}</text>
			</view>
			<view class="address" @click="use">{{ data.region }}{{ data.address }}</view>
			<view class="item2">
				<u-checkbox v-model="data.isMian" @change="handleChange(data)">默认地址</u-checkbox>
				<view class="btn">
					<u-icon size="36" name="edit-pen"></u-icon>
					<text style="margin-right: 20rpx;" @click.stop="goPage(data.id)">编辑</text>
					<u-icon size="36" color="red" name="trash"></u-icon>
					<text @click.stop="deleteAddress">删除</text>
				</view>
			</view>
		</view>
		<u-modal v-model="show" :content="content" show-cancel-button @confirm="callback(data.id)"	></u-modal>
	</view>
</template>

<script>
export default {
	name: 'address-card',
	data(){
		return {
			show:false,
			content:"",
			callback:()=>{},
		}
	},
	computed:{
		use(){
			if(this.isBack){
				return () => {
					this.show = true
					this.content = "是否使用该地址"
					this.callback = (id)=>{
						let pages = getCurrentPages() 
						let prevPage = pages[pages.length -2] 
						prevPage.$vm.getAddressId(id) 
						uni.navigateBack({
							delta: 1
						});
					}
				}
			}
			return ()=>{}
		}
	},
	props: {
		// 是否显示编辑按钮
		showEdit: {
			type: Boolean,
			default: false
		},
		// 数据源
		data: {
			type: Object,
			default: () => {
				return {};
			}
		},
		// 是否显示底部边框
		showBorderBottom: {
			type: Boolean,
			default: false
		},
		// 是否选择并返回
		isBack: {
			type: Boolean,
			default: false
		},
		// 是否能点击
		isGoSelect: {
			type: Boolean,
			default: true
		}
	},
	methods: {
		goPage(id) {
			uni.navigateTo({
				url: '/pages-mine/pages/address/add-or-update?type=update&id=' + id
			});
		},
		handleChange({id,userId}){
			this.$u.api.updateAddress({
				id,
				isMian: 1,
				userId
			}).then(res=>{
				uni.$u.toast('设置默认地址成功')
				this.$emit("getList")
			})
		},
		deleteAddress(){
			this.show = true
			this.content = "确认删除该地址"
			this.callback = (id)=>{
				this.$u.api.deleteAddress(id).then(res=>{
					this.$emit("getList")
				})
			}
		}
	}
};
</script>

<style lang="scss" scoped>
.slot {
	margin: 20rpx 0;
	padding: 50rpx 30rpx 10rpx;
	background-color: $app-theme-bg-color;
	display: flex;
	justify-content: space-between;
	align-items: center;
	
	.info{
		width: 100%;
	}
	
	.item{
		margin-bottom: 20rpx;
		.name{
			font-weight: 700;
			font-size: 38rpx;
			margin-right: 10rpx;
		}
		
		.gender{
			color: #ababab;
			font-size: 38rpx;
			margin-right: 100rpx;
		}
		
		.phone{
			color: #ababab;
			font-size: 38rpx;
		}
	}
	
	.address{
		border-bottom: 1rpx solid #f5f5f5;
		font-size: 36rpx;
		color: #ababb3;
		padding-bottom: 20rpx;
	}
	
	.item2{
		display: flex;
		flex-direction: row;
		align-items: center;
		justify-content: space-between;
		padding: 10rpx 0;
		
		.btn{
			font-size: 36rpx;
		}
	}
}
</style>
