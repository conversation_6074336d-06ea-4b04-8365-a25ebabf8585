export const useShopApi = (Vue, vm) => {
	return {
		// 获取店铺分类信息
		getShopCategory: () => vm.$u.get('store/shopCls/list',{pageSize:99999,pageNum:1, type:1}),
		// 通过id获取店铺信息
		getShopByCategory: (storeShopClsId) => vm.$u.get('store/shop/list', {
			storeShopClsId,
			pageSize:99999,
			pageNum:1,
			isShow:0
		}),
		getMyShopList: () => vm.$u.get('store/shop/myStoreList',{pageSize:99999,pageNum:1}),
		// 查询所有店铺信息
		getShopAndStore: (orderBys) => vm.$u.get('store/shop/search', {
			pageSize:99999,
			pageNum:1,
			[orderBys ? 'orderBys' : undefined]: orderBys
		}),
		// 查询所有店铺信息
		getShopAndStoreByCategory: (storeShopClsId,orderBys) => vm.$u.get('store/shop/search', {
			storeShopClsId, 
			pageSize:99999,
			pageNum:1,
			[orderBys ? 'orderBys' : undefined]: orderBys
		}),
		// 商户入驻
		applyShop:(data) => vm.$u.post('store/shop/apply',data),
		// 查询店铺
		getShopDetail: (id) => vm.$u.get('store/shop/detail', {
			id
		}),
		// 查询商品分类
		getStoreCategory: (storeShopId)=>vm.$u.get('store/storeCls/list',{pageSize:99999,pageNum:1,storeShopId}),
		// 通过商户和商品分类查询商品
		getStore:(storeShopId,storeStoreClsIdSon)=>vm.$u.get('store/store/list',{pageSize:99999,pageNum:1,storeShopId,storeStoreClsIdSon,flag:1}),
		// 查询商品信息
		getStoreDetail:(id)=>vm.$u.get('store/store/detail',{id}),
		// 查询购物车
		getCart: ()=>vm.$u.get('store/storeShoppingCart/list',{}),
		// 查询购物车数量
		getCartCount: ()=>vm.$u.get('store/storeShoppingCart/count',{}),
		// 添加购物车
		addCart: (data)=>vm.$u.post('store/storeShoppingCart/add',data),
		// 修改购物车
		updateCart: (data)=>vm.$u.post('store/storeShoppingCart/update',data),
		// 删除购物车
		deletetCart:(id)=>vm.$u.delete('store/storeShoppingCart/delete?id=' + id),
		// 确认订单
		readyOrder: (data)=>vm.$u.post('store/order/add', data),
		// 提交订单
		submitOrder: (data)=>vm.$u.post('store/order/pay', data),
		// 获取订单
		getOrderList: (status,type = 1,storeShopId, flag)=>vm.$u.get('store/order/list',{
			pageSize:99999,
			pageNum:1,
			[flag ? 'flag' : '' ]: 1,
			[status !=null ? (type == 1 ? 'orderStatus' : 'deliveryStatus') : '']:status,
			[storeShopId ? 'storeShopId': '']:storeShopId
		}),
		// 订单详情
		getOrderDetail: (orderNo)=>vm.$u.get('store/order/detail',{orderNo}),
		// 取消订单
		cancelOrder: (orderNo)=>vm.$u.post('store/order/cancel',orderNo),
		// 修改订单状态
		updateOrder: (orderNo, orderStatus, deliveryStatus)=>vm.$u.get('store/order/updateStatus',{
			orderNo,
			[!orderStatus ? '' :　'orderStatus']: orderStatus,
			[!deliveryStatus ? '' :　'deliveryStatus']: deliveryStatus,
		}),
		// 文字审核
		// text: (code, comment)=>vm.$u.post('store/shopComment/textContentSecurityRecognition',{code,comment}),
		text: (content)=>vm.$u.post('wechat/check/text?content=' + content),
		// 评论商家
		submitComment: (data)=>vm.$u.post('store/shopComment/add',data),
		// 评论数量
		commentCount: (storeShopId)=>vm.$u.get('store/shopComment/commentCount',{storeShopId}),
		// 评论列表
		commentList: (storeShopId,shopStar)=>vm.$u.get('store/shopComment/commentList',{
			storeShopId,
			pageSize:99999,
			pageNum:1,
			shopStar
		}),
	}
}