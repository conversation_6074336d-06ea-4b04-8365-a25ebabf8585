<template>
	<view class="page">
		<Navbar title="服务地址管理"></Navbar>
		<view class="list">
			<AddressCard @getList="getAddressList" v-for="(item, index) in list" :isBack="isSelect" :key="index"
				:data="item" :showEdit="true" :showBorderBottom="index != list.length - 1"></AddressCard>
		</view>
		<view class="btn">
			<u-button type="primary" shape="circle" @click="addorupdate">
				<text>{{istoken?'添加新地址':'登陆后添加收获地址'}}</text>
			</u-button>
		</view>
	</view>
</template>

<script>
	import AddressCard from '@/pages/mine/components/address-card.vue';
	export default {
		components: {
			AddressCard
		},
		data() {
			return {
				istoken: this.$store.state.user.token,
				isSelect: false,
				isBack: false,
				list: []
			};
		},
		onLoad(ops) {
			if (ops.isSelect) {
				this.isSelect = ops.isSelect;
				this.isBack = ops.isBack;
			}
		},
		onShow() {
			this.getAddressList()
		},
		methods: {
			addorupdate() {
				if (this.istoken) {
					uni.navigateTo({
						url: '/pages-mine/pages/address/add-or-update?type=add'
					})
				} else {
					uni.reLaunch({
						url: "/pages/login/index",
					});
				}
			},
			getAddressList() {
				if (this.istoken) {
					this.$u.api.getAddressList().then(res => {
						this.list = res.rows
					})
				}

			}
		}
	};
</script>

<style lang="scss" scoped>
	.page {
		background-color: #f5f5f5;
	}

	.list {
		padding-bottom: 160rpx;
	}

	.btn {
		position: fixed;
		background: #fff;
		bottom: 0;
		left: 0;
		padding: 30rpx;
		width: 100%;
		padding-bottom: 40rpx;

		text {
			margin-left: 8rpx;
		}

		& ::v-deep .u-btn {
			background: #ee6c6c;
		}

		& ::v-deep .u-primary-hover {
			background: #ee6c6c !important;
		}
	}
</style>