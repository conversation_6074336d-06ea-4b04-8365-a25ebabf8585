<template>
	<view>
		<view class="zone-grid">
			<view class="grid-item" v-for="(item, index) in list" :key="index" @click="handleClick(item)">
				<image class="grid-img" :src="item.img" mode=""></image>
			</view>
		</view>
	</view>
</template>

<script>
	const token = uni.getStorageSync("App-Token");
	export default {
		name: 'ZoneGrid',
		props: {
			list: {
				type: Array,
				default: () => []
			}
		},
		data() {
			return {
				token: token,
			}
		},
		methods: {
			handleClick(item) {
				// if (token.token) {
				uni.navigateTo({
					url: item.url
				})
				// } else {
				// 	uni.showModal({
				// 		title: "提示",
				// 		content: "您还未登录，请先登录",
				// 		cancelText: "取消",
				// 		confirmText: "确定",
				// 		success: function(res) {
				// 			if (res.confirm) {
				// 				uni.reLaunch({
				// 					url: "/pages/login/index",
				// 				});
				// 			}
				// 		},
				// 	});
				// }

			}
		}
	}
</script>

<style lang="scss" scoped>
	.zone-grid {
		display: flex;
		flex-wrap: wrap;
		justify-content: space-around;
		row-gap: 20rpx;
		padding-bottom: 24px;

		.grid-item {
			width: 334rpx;
			height: 200rpx;

			.grid-img {
				width: 100%;
				height: 100%;
				object-fit: cover;
			}
		}
	}
</style>