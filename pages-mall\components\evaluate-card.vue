<template>
	<view class="slot" :style="showBorderBottom ? '' : 'border-bottom:none;margin-bottom:0'">
		<view class="user">
			<view class="info">
				<!-- 头像：优先使用用户头像，无则用默认 -->
				<u-avatar size="80" :src="data.picture ? HTTP_URL_DEV + data.picture : defaultAvatar"></u-avatar>
				<view class="basic">
					<view class="name">{{ data.createBy || '匿名用户' }}</view>
					<view class="evaluate">
						<u-rate :count="5" v-model="data.serviceSatisfactionLevel" disabled
							:inactive-color="rateInActiveColor" :active-color="rateActiveColor" size="28rpx"></u-rate>
						<text class="score">{{ data.serviceSatisfactionLevel || 0 }}分</text>
					</view>
				</view>
			</view>
			<view class="date">{{ formatDate(data.createTime) }}</view>
		</view>
		<view class="content">{{ data.evaluate || '暂无评价内容' }}</view>

		<!-- 评价图片展示（处理逗号分隔的图片地址） -->
		<view class="pictures" v-if="imageList.length > 0">
			<view class="picture-item" v-for="(img, index) in imageList" :key="index"
				@click="previewImage(img, imageList)">
				<!-- <u-image :src="img" mode="aspectFill" width="164rpx" height="164rpx" loading-mode="lazy"></u-image> -->
				<!-- <u-image width="100%" height="300rpx" :src="img"></u-image> -->
				<image :src="img" mode="" class="picture-img"></image>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		HTTP_URL_DEV
	} from '@/api/config';

	export default {
		name: 'evaluate-card',
		props: {
			// 数据源（评价数据）
			data: {
				type: Object,
				default: () => ({})
			},
			// 是否显示底部边框
			showBorderBottom: {
				type: Boolean,
				default: false
			}
		},
		data() {
			return {
				rateActiveColor: this.$appTheme.appThemeColor,
				rateInActiveColor: this.$appTheme.appThemeCardGrayColor,
				defaultAvatar: '/static/default-avatar.png', // 默认头像（需自行添加图片）
				HTTP_URL_DEV // 从配置导入基础路径
			};
		},
		computed: {
			// 处理图片列表（分割逗号并拼接完整路径）
			imageList() {
				if (!this.data.picture) return [];
				// 分割逗号，过滤空值，拼接基础路径
				return this.data.picture.split(',')
					.map(img => img.trim())
					.filter(img => img) // 过滤空字符串
					.map(img => img.startsWith('http') ? img : HTTP_URL_DEV + img);
			}
		},
		methods: {
			// 格式化日期（例如：2025-08-14 10:23:03 → 2025-08-14）
			formatDate(dateStr) {
				if (!dateStr) return '';
				const date = new Date(dateStr);
				return `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')}`;
			},
			// 预览图片
			previewImage(currentImg, allImages) {
				uni.previewImage({
					current: currentImg,
					urls: allImages
				});
			}
		}
	};
</script>

<style lang="scss" scoped>
	.slot {
		border-bottom: 1rpx solid $app-theme-border-color;
		padding: 24rpx 30rpx;
		margin-bottom: 24rpx;
		background-color: #fff; // 增加背景色，与页面区分

		&:last-child {
			margin-bottom: 0;
		}
	}

	.user {
		display: flex;
		justify-content: space-between;
		align-items: center; // 垂直居中，更美观

		.info {
			display: flex;
			align-items: center; // 头像与文字垂直居中

			.basic {
				margin-left: 16rpx;

				.name {
					font-size: 28rpx;
					color: $app-theme-text-black-color;
					font-weight: 500;
					margin-bottom: 8rpx; // 与评分拉开距离
				}

				.evaluate {
					display: flex;
					align-items: center;

					.score {
						margin-left: 10rpx;
						font-size: 24rpx;
						color: $app-theme-text-gray-color;
					}
				}
			}
		}

		.date {
			font-size: 26rpx;
			color: $app-theme-text-gray-color;
			white-space: nowrap; // 防止日期换行
		}
	}

	.content {
		padding: 20rpx 0; // 上下 padding 更均衡
		font-size: 28rpx;
		color: $app-theme-text-black-color;
		line-height: 1.6; // 行高更易读
		word-break: break-all; // 长文本自动换行
	}

	.pictures {
		display: flex;
		gap: 16rpx; // 图片间距
		flex-wrap: wrap; // 超出换行
		padding-top: 10rpx;
	}

	.picture-item {
		width: 164rpx;
		height: 164rpx;
		border-radius: 8rpx; // 圆角更美观
		overflow: hidden; // 裁剪超出部分
		background-color: #f5f5f5; // 加载前背景色

		&:active {
			opacity: 0.8; // 点击反馈
		}

		.picture-img {
			width: 164rpx;
			height: 164rpx
		}
	}
</style>