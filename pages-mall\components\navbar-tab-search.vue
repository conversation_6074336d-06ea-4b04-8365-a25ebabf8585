<template>
	<view class="navbar" :style="[{ minHeight: navBarHeight + 'px' }]">
		<view class="inner" :style="[
				{ minHeight: menuHeight + 'px' },
				{ lineHeight: menuHeight + 'px' },
				{ paddingLeft: menuRight * 2 + 'px' },
				{ paddingRight: menuRight * 2 + 'px' },
				{ paddingTop: navBarHeight - menuHeight + 'px' }
			]">
			<view class="navbar-slot">
				<view class="navbar-left" @click="$u.route({ type: 'navigateBack', delta: 1 })">
					<u-icon size="32" name="arrow-leftward" :color="$appTheme.appThemeTextBlackColor"></u-icon>
				</view>
				<view class="navbar-title">{{ title }}</view>
				<view class="navbar-right"></view>
			</view>
			<view class="search-slot" :style="{ paddingTop: menuTop + 'px' }">
				<u-search :placeholder="placeholder" :showAction="false" shape="square" borderRadius="0rpx"
					bg-color="#F4F5F8"></u-search>
			</view>
			<!-- 			<view class="tab-slot">
				<view class="tab-container">
										<view class="tab" v-for="(tab, index) in tabOps" :key="index" :class="{ active: tabIndex == index }"
						@click="change(tab, index)">
						{{ tab.label }}
					</view>
				</view>
			</view> -->
			<u-tabs :list="tabOps" :current="tabIndex" @change="change"></u-tabs>
		</view>
	</view>
</template>

<script>
	const app = getApp();
	export default {
		name: 'navbar-tab-search',
		props: {
			// 标题
			title: {
				type: String,
				default: '标题'
			},
			// 占位内容
			placeholder: {
				type: String,
				default: '请输入'
			},
			// tab配置项
			tabOps: {
				type: Array,
				default: () => {
					return [];
				}
			},
			// 1订单 2技师订单
			Tid: {
				type: String,
				default: '1'
			},
		},
		data() {
			return {
				// 导航栏高度
				menuTop: app.globalData.menuTop,
				navBarHeight: app.globalData.navBarHeight,
				menuRight: app.globalData.menuRight,
				menuBotton: app.globalData.menuBotton,
				menuHeight: app.globalData.menuHeight,
				statusBarHeight: app.globalData.statusBarHeight,
				// 当前tab
				tabIndex: 0
			};
		},
		methods: {
			// 切换tab
			change(index) {
				// 定义不同Tid对应的映射关系
				const tidMappings = {
					1: [1, 2, 4, 5, 6, 7, 3], // Tid=1时的映射
					2: [4, 5] // Tid=2时的映射
					// 可扩展其他Tid的映射规则
				};

				// 获取当前Tid对应的映射数组
				const indexMap = tidMappings[this.Tid];

				// 校验Tid有效性
				if (!indexMap) {
					console.error('无效的Tid:', this.Tid);
					return;
				}

				// 校验index有效性
				if (index < 0 || index >= indexMap.length) {
					console.error(`Tid=${this.Tid} 无效的索引:`, index);
					return;
				}

				// 获取映射值
				const mappedValue = indexMap[index];

				// 更新组件状态
				this.tabIndex = index;

				// 触发事件（传递原始索引和映射值）
				this.$emit('change', {
					originalIndex: index,
					mappedIndex: mappedValue,
					tid: this.Tid // 可选：将Tid一起传递出去
				});

				console.log('操作日志:', {
					Tid: this.Tid,
					inputIndex: index,
					outputValue: mappedValue
				});
			}
		}
	};
</script>

<style lang="scss" scoped>
	.navbar {
		width: 100vw;
		position: fixed;
		top: 0;
		left: 0;
		z-index: 899;
		overflow: hidden;
	}

	.inner {
		width: 100%;
		height: 100%;
		background-color: $app-theme-bg-color;
	}

	.navbar-slot {
		display: flex;
		align-items: center;
		justify-content: space-between;

		.navbar-title {
			font-size: 36rpx;
			font-family: PingFang SC;
			font-weight: 400;
			color: $app-theme-text-black-color;
		}
	}

	.tab-slot {
		height: 80rpx;
		padding-top: 20rpx;
		overflow: hidden;

		.tab-container {
			height: 100%;
			display: flex;
			flex-wrap: nowrap;
			overflow-x: auto;
			padding: 0 20rpx;

			/* 隐藏滚动条 */
			&::-webkit-scrollbar {
				display: none;
				width: 0;
				height: 0;
				background-color: transparent;
			}

			.tab {
				flex-shrink: 0;
				font-size: 26rpx;
				padding: 0 30rpx;
				position: relative;
				color: $app-theme-navbar-tab-color;

				&.active {
					color: #001a62;
					font-weight: bold;

					&::before {
						content: '';
						position: absolute;
						bottom: -10rpx;
						left: 50%;
						transform: translateX(-50%);
						width: 40rpx;
						height: 6rpx;
						background: $app-theme-navbar-tab-color-active;
						border-radius: 3rpx;
					}
				}
			}
		}
	}
</style>