<template>
	<view class="slot">
		<view class="slot-inner"
			:style="[showSearch ? { minHeight: navBarHeight + 'px' } : { height: navBarHeight + 'px' }]">
			<view class="inner-showSearch" :style="[
				{ minHeight: menuHeight + 'px' },
				{ lineHeight: menuHeight + 'px' },
				{ paddingLeft: menuRight * 2 + 'px' },
				{ paddingRight: menuRight * 2 + 'px' },
				{ paddingTop: navBarHeight - menuHeight - menuTop + 'px' },
				{ paddingBottom: '20rpx' }
			]">
				<view class="loaction-slot">
					<view class="loaction-title" v-if="locationStatus == 1"><text>正在获取地理位置...</text></view>
					<view class="loaction-title" v-if="locationStatus == 2" @click="goCitySelectPage">
						<text>{{ cityListSelected ? cityListSelected : locationCom }}</text>
						<u-icon style="margin-left: 8rpx;" :size="18" color="#171717" name="arrow-down-fill"></u-icon>
					</view>
					<view class="loaction-title" v-if="locationStatus == 3" @click="getLocation(true)">
						<text>地理位置获取失败，点击重试</text></view>
				</view>
				<view class="search-slot">
					<u-search class="search-components" @click="goSearchPage(url)" disabled :placeholder="placeholder"
						:showAction="false" shape="square" borderRadius="0rpx" :bg-color="searchBgColor"
						:class="showSlot ? 'active' : ''"></u-search>
					<slot showSlot></slot>
				</view>
				<view class="tabList">
					<view class="tab" v-for="(item, index) in tabOps" :key="index" :class="{ active: current == index }"
						@click="change(index)">{{ item }}</view>
				</view>
			</view>
		</view>
		<view class="slot-height" v-if="showSearch" :style="[{ height: navBarHeight + menuHeight + 27 + 'px' }]"></view>
		<view class="slot-height" v-else :style="[{ height: navBarHeight + 'px' }]"></view>
	</view>
</template>

<script>
import { getLocation, getLocationAgain } from '@/utils/location.js';
const app = getApp();
export default {
	name: 'navbar-tab-search',
	props: {
		// 配置项
		tabOps: {
			type: Array,
			default: () => {
				return ['热门话题', '热门活动', '邻居互动'];
			}
		},
		// 占位内容
		placeholder: {
			type: String,
			default: '请输入'
		},
		// 是否显示插槽，用于输入框右侧内容的显示
		showSlot: {
			type: Boolean,
			default: false
		},
		// 是否显示搜索框
		showSearch: {
			type: Boolean,
			default: true
		},
		// 跳转的url
		url: {
			type: String,
			default: ''
		},
		// 是否显示返回按钮
		showBack: {
			type: Boolean,
			default: false
		}
	},
	data() {
		return {
			// 导航栏高度
			navBarHeight: app.globalData.navBarHeight,
			menuRight: app.globalData.menuRight,
			menuBotton: app.globalData.menuBotton,
			menuHeight: app.globalData.menuHeight,
			menuTop: app.globalData.menuTop,
			// 当前tab
			current: 0,
			// 背景色
			searchBgColor: this.$appTheme.appThemeSearchBgColor,
			// 定位数据
			locationData: {},
			// 当前城市
			locationCom: '',
			// 获取地理位置状态，1正在获取，2获取成功，3失败
			locationStatus: 1
		};
	},
	mounted() {
		if (this.cityListSelected) {
			this.locationStatus = 2;
		} else {
			this.getLocation();
		}
	},
	methods: {
		// 切换tab
		change(index) {
			this.current = index;
			this.$emit('change', index);
		},

		// 去搜索页面
		goSearchPage(url) {
			uni.navigateTo({
				url
			});
		},
		async getLocation(isAgain = false) {
			this.locationStatus = 1;
			if (!isAgain) {
				getLocation(res => {
					this.setLocationData(res);
				});
			} else {
				getLocationAgain(res => {
					this.setLocationData(res);
				});
			}
		},

		// 设置定位数据
		setLocationData(res) {
			if (res.status) {
				this.locationStatus = 2;
				this.locationData = res.data;
				this.locationCom = res.data.address_reference.town.title;
				this.$store.commit('user/COMMIT_LOACTION_INFO', res.data);
			} else {
				this.locationStatus = 3;
				uni.showToast({
					title: '获取地理位置失败',
					duration: 3000,
					icon: 'none'
				});
			}
		}
	}
};
</script>

<style lang="scss" scoped>
.slot {
	width: 100vw;
}

.slot-inner {
	width: 100%;
	position: fixed;
	top: 0;
	left: 0;
	z-index: 899;
	overflow: hidden;
}

.tabList {
	width: 100%;
	display: flex;
	align-items: center;
	justify-content: space-between;
	.tab {
		font-size: 36rpx;
		font-weight: 400;
		margin-right: 48rpx;
		color: $app-theme-navbar-tab-color;

		&.active {
			font-weight: 500;
			color: $app-theme-navbar-tab-color-active;
			font-size: 40rpx;
			position: relative;

			&::before {
				content: '';
				width: 16rpx;
				height: 6rpx;
				background: $app-theme-navbar-tab-color-active;
				border-radius: 1px;
				position: absolute;
				bottom: -10rpx;
				left: 50%;
				transform: translate(-50%, 0);
			}
		}
	}
}

.inner {
	width: 100%;
	position: relative;
	height: 100%;
	background-color: $app-theme-bg-color;

	.left {
		position: absolute;
		z-index: 999;
		display: flex;
		align-items: center;
		// 防误触
		width: 30x;
		margin-right: 10px;
	}

	.tabList {
		width: 50%;
		position: absolute;
		z-index: 999;
		display: flex;
		align-items: center;
	}
}

.inner-showSearch {
	width: 100%;
	height: 100%;
	background-color: $app-theme-bg-color;

	.tabList {
		display: flex;
		align-items: center;
	}
}

.search-slot {
	width: 100%;
	display: flex;
	justify-content: space-between;
	align-items: center;

	.search-components {
		width: 100%;

		&.active {
			width: 86%;
		}
	}
}
</style>
