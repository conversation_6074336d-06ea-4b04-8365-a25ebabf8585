page {
	background-color: #f6f6f6;
	color: #333333;
}
.grid.col-3>view{
	width: 33.333%;
}
.index-sort {
	background-color: #fff;
	text-align: center;
	padding: 0px 15upx;
	/* box-shadow: 0px 0px 5px rgba(0,0,0,.1); */
}

.index-sort-main {
	padding: 25upx 0upx;
}

.index-sort-i {
	width: 80upx;
	height: 80upx;
	line-height: 80upx;
	text-align: center;
	margin: 0 auto 10upx auto;
	color: #fff;
	background-color: #0081ff;
	font-size: 36upx;
	border-radius: 50%;
	/* box-shadow: 1px 2px 2px rgba(0,0,0,.15); */
	opacity: 0.8;
}

.tool-sort .index-sort-i {
	width: 90upx;
	height: 90upx;
	line-height: 90upx;
}

.inbox-sort {
	padding: 20upx 0px;
}

.inbox-sort .index-sort-i {
	width: 90upx;
	height: 90upx;
	line-height: 90upx;
	position: relative;
}

.inbox-sort .index-sort-i .unreadNum {
	position: absolute;
	top: -16upx;
	right: -16upx;
	font-size: 24upx;
	line-height: 34upx;
	display: block;
	text-align: center;
	padding: 0 10upx;
	height: 34upx;
	border-radius: 50%;
}

.index-sort-text {
	font-size: 26upx;
}

.index-sort .index-sort-box:nth-child(1) .index-sort-i {
	background-color: #fbbd08;

}

.index-sort .index-sort-box:nth-child(2) .index-sort-i {
	background-color: #39b54a;
}

.index-sort .index-sort-box:nth-child(3) .index-sort-i {
	background-color: #2eabbf;
}

.index-sort .index-sort-box:nth-child(4) .index-sort-i {
	background-color: #e54d42;
}

.index-sort .index-sort-box:nth-child(5) .index-sort-i {
	background-color: #24c597;
}

.index-sort .index-sort-box:nth-child(6) .index-sort-i {
	background-color: #e03997;
}

.index-sort .index-sort-box:nth-child(7) .index-sort-i {
	background-color: #eb550f;
}

.index-sort .index-sort-box:nth-child(8) .index-sort-i {
	background-color: #4343e7;
}

.index-sort .index-sort-box:nth-child(9) .index-sort-i {
	background-color: #ff9f10;
}

.tabbarActive {
	color: #222 !important;
}

.tabbar view {
	overflow: initial;
	display: block;
}

.tabbar {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	z-index: 990;
	overflow: initial;
	display: block;
	width: 100%;
	height: 100upx;
	background-color: #ffffff;
	border-top: solid 1px #f6f6f6;
	box-sizing: content-box;
}

.tabbar-item {
	display: block;
	height: 100upx;
	float: left;
	background: #fff;
	/* width: calc(25% - 32upx); */
		width: 33.3%;
	text-align: center;
	overflow: hidden;
	border: none;
	margin: 0;
	padding: 0;
	box-sizing: border-box;
}

.tabbar-item .item-img {
	width: 46upx;
	height: 46upx;
	display: block;
	margin: 0 auto;
	margin-top: 10upx;
	text-align: center;
}

.tabbar-item .item-name {
	font-size: 24upx;
	margin-top: 4upx;
	color: #999999;
}

.tabbar-item.addPost {
	width: 120upx;
	height: 120upx;
	padding: 0;
	position: relative;
	overflow: initial;
	box-sizing: border-box;
	background-color: #fff;
}

.tabbar-item.addPost .addPost-main {
	position: absolute;
	top: -30upx;
	background-color: #fff;
	width: 100%;
	height: 100%;
	border-radius: 50%;
	border: solid 1px #f9f9f9;
	position: relative;
	padding: 10upx;
	box-sizing: border-box;
}

.addPost-i {
	background-color: #e4393c;
	width: 100%;
	height: 100%;
	;
	border-radius: 50%;
	line-height: 100upx;
	text-align: center;
	font-size: 70upx;
	color: #fff;
	font-weight: bold;
	transition: all 0.3s;
}

.postShow .addPost-i {
	transform: rotate(135deg) translateZ(0);
}



.tabbar-operate {
	position: fixed;
	z-index: -20;
	bottom: -200upx;
	width: 100%;
	left: 0;
	padding: 0upx 30upx;
	box-sizing: border-box;
	text-align: center;
	transition: 0.3s all;
}

.tabbar-operate.show {
	bottom: 140upx;
}

.tabbar-operate-main {
	width: 100%;
	height: 200upx;
	border-radius: 40upx;
	padding-top: 18upx;
	background: #fff;
	box-shadow: 0px 0px 10upx rgba(0, 0, 0, 0.2);
	overflow: hidden;
}

.tabbar-operate-main>view {
	width: 25%;
	float: left;
}

.tabbar-operate-main .index-sort-text {
	font-size: 28upx;
	font-weight: bold;
}

.tabbar-operate-bg {
	position: fixed;
	background-color: #000;
	width: 100%;
	height: calc(100vh - 100upx);
	opacity: 0;
	z-index: -30;
	left: 0px;
	top: 0px;
	transition: 0.2s all;
	visibility: hidden;
}

.tabbar-operate-bg.show {
	opacity: 0.3;
	visibility: visible;
}


