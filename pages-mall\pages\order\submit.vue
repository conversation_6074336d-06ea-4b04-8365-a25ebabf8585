<template>
	<view class="page">
		<Navbar title="提交订单"></Navbar>
		<!-- 收货地址 -->
		<view class="address" @click="addressEvent">
			<view v-if="objMainAddress && objMainAddress.id">
				<view style="font-size: 18px;font-weight: bold;">
					{{objMainAddress.consignee}}
				</view>
				<view style="color: #999;">
					{{objMainAddress.region}}
				</view>
				<view class="flexadd">
					<view class="">
						默认地址
					</view>
					<view class="">
						更换地址
					</view>
				</view>
			</view>
			<view v-else class="no-address">
				<view class="text">请添加收货地址</view>
			</view>
		</view>
		<!-- 商品列表 -->
		<view class="goods">
			<SubmitGoodsCard v-for="(goods, index) in goodsList" :key="index" :data="goods"
				:showBorderBottom="index != goodsList.length - 1"></SubmitGoodsCard>
		</view>
		<!-- 优惠信息 -->
		<SubmitDiscounts v-if="!oscart"></SubmitDiscounts>
		<!-- 优惠券 -->
		<view class="yhq" v-if="oscart">
			<view class="line">
				<view class="labe">优惠券</view>
				<view class="value gray" @click="goDiscountsPage">
					<text>{{couponlength?couponlength:'暂无可用'}}</text>
					<view class="more"><u-icon size="20" name="arrow-right"></u-icon></view>
				</view>
			</view>
		</view>
		<!-- 备注 日历 -->
		<view class="yhq" v-if="oscart">
			<view class="">
				<u-field v-model="orderInfo.content" label="备注" placeholder="如有特殊要求,请填写备注" type='textarea'>
				</u-field>
			</view>
			<view class="time" @click="calendarshow = true">
				<view class="">
					预约时间
				</view>
				<view class="timerigth">
					<view class="">
						{{orderInfo.reservationTime}}
					</view>
					<view class="">
						<u-icon name="arrow-right" color="#999" size="28"></u-icon>
					</view>
				</view>
			</view>
		</view>
		<!-- 底部按钮 -->
		<SubmitOperate :data="orderInfo" :hasAddress="!!objMainAddress.id" @submitOrder="submitOrder"></SubmitOperate>
		<u-popup v-model="showOscart" mode="center" border-radius="12" :closeable="true">
			<view class="listquan">
				<!-- 优惠券列表 -->
				<view class="item" v-for="(item, index) in couponList" :key="index" @click="changeDiscount(item, index)"
					:class="{
						'expired': item.status === 2, 
						'used': item.status === 1,
						'selected': selectIndex === index
					}">
					<view class="left">
						<view class="discount">
							<text>￥</text>
							<text>{{ item.amount }}</text>
						</view>
						<view class="standard">满{{ item.minUseAmount }}可用</view>
						<!-- 有效期显示 -->
						<view class="valid-date">有效期: {{ formatDateRange(item) }}</view>
					</view>
					<view class="right">
						<view class="title">{{ item.name || '优惠券' }}</view>
						<!-- 使用条件说明 -->
						<view class="condition">适用于所有服务</view>
						<!-- 状态文本 -->
						<!-- <view class="status" v-if="item.status === 2">已过期</view> -->
						<!-- <view class="status used" v-if="item.status === 1">已使用</view> -->
						<view class="status valid" v-if="item.status === 0">
							{{ isCouponNotStarted(item) ? '未生效' : `剩余 ${getDaysLeft(item)} 天` }}
						</view>
					</view>
					<!-- 选中图标 -->
					<view class="select" v-if="selectIndex === index && item.status === 0"></view>

					<!-- 状态标签 -->
					<view class="expired-tag" v-if="item.status === 2">已过期</view>
					<view class="used-tag" v-if="item.status === 1">已使用</view>
					<view class="not-start-tag" v-if="item.status === 0 && isCouponNotStarted(item)">未生效</view>
				</view>
				<!-- 空状态 -->
				<view v-if="couponList.length === 0" class="empty-coupon">
					<u-icon name="coupon" size="100" color="#ccc"></u-icon>
					<text>暂无优惠券</text>
				</view>
			</view>
		</u-popup>
		<u-calendar v-model="calendarshow" :mode="mode" :min-date="minDate" :max-date="maxDate"
			@change="changeTime"></u-calendar>
	</view>
</template>

<script>
	import SubmitDiscounts from '@/pages-mall/components/order/submit-discounts.vue';
	import SubmitGoodsCard from '@/pages-mall/components/order/submit-goods-card.vue';
	import SubmitOperate from '@/pages-mall/components/order/submit-operate.vue';
	import AddressCard from '@/pages/mine/components/address-card.vue';
	export default {
		components: {
			AddressCard,
			SubmitDiscounts,
			SubmitGoodsCard,
			SubmitOperate
		},
		data() {
			return {
				useraddress: null,
				objMainAddress: {}, // 默认地址
				dizhi: [], // 地址列表
				calendarshow: false,
				mode: 'date',
				minDate: '',
				maxDate: '',
				oscart: false, // 控制优惠信息展示
				showOscart: false, // 优惠券弹窗
				selectIndex: null, // 选中的优惠券索引

				addressInfo: {},
				goodsList: [],
				orderInfo: {
					currentPrice: '', // 现价
					originalPrice: '', // 原价
					num: '', // 数量
					communityServiceId: '', // 商品id
					communityServiceSpecificationsId: '', // 规格id
					content: '', // 备注
					userId: this.$store.state.user.userInfo.userId, // 用户id
					addressId: '', // 地址id
					couponId: '', // 优惠券id
					reservationTime: '' // 预约时间
				},
				couponList: [], // 优惠券列表
				couponlength: null, // 优惠券数量文本
				originalTotalPrice: 0, // 原始总价
				addressLoaded: false
			};
		},
		onLoad(option) {
			console.log(option)
			this.initDateRange()
			this.getCoupon()
			this.getAddressList();

			// 初始化商品信息
			let userInfo = JSON.parse(decodeURIComponent(option.coreform));
			let userInfoAddress = JSON.parse(decodeURIComponent(option.Address));
			this.useraddress = userInfoAddress

			this.goodsList.push({
				name: userInfo.title,
				num: option.num,
				sku: userInfo.title,
				originalPrice: userInfo.amount, // 原价
				picture: userInfo.picture
			})

			// 初始化订单信息
			this.orderInfo.num = option.num
			this.orderInfo.originalPrice = userInfo.amount
			this.orderInfo.communityServiceId = option.id
			this.orderInfo.communityServiceSpecificationsId = userInfo.id
			this.originalTotalPrice = (userInfo.amount * option.num).toFixed(2);
			this.orderInfo.currentPrice = this.originalTotalPrice;

			// 金刚区订单标记
			if (option.ishideShoppingCart === "hideShoppingCart") {
				this.oscart = true
			}

			this.addressLoaded = true;
		},
		methods: {
			// 判断优惠券是否未生效（仅针对待使用状态）
			isCouponNotStarted(item) {
				if (item.status !== 0) return false
				const startTime = new Date(item.startTime).getTime()
				return startTime > new Date().getTime()
			},

			// 计算剩余天数（仅针对待使用状态）
			getDaysLeft(item) {
				if (item.status !== 0) return 0
				const endTime = new Date(item.endTime).getTime()
				const days = Math.ceil((endTime - new Date().getTime()) / (1000 * 60 * 60 * 24))
				return days > 0 ? days : 0
			},

			// 获取地址详情
			getAddressId(address) {
				let data = {
					pageSize: 99999,
					pageNum: 1
				}
				this.$u.api.getAddressList(data).then(res => {
					const mainAddress = res.rows.find(item => item.id === address);
					this.objMainAddress = mainAddress
					this.orderInfo.addressId = mainAddress.id
				})
			},

			// 获取优惠券列表
			getCoupon() {
				this.$u.api.getCouponList().then(res => {
					console.log(res, 'res')
					this.couponList = res.data || [];
					// 计算可用优惠券数量（状态为0且已生效）
					const validCount = this.couponList.filter(item =>
						item.status === 0 && !this.isCouponNotStarted(item)
						// !this.isCouponNotStarted(item)
					).length;
					this.couponlength = validCount > 0 ? `${validCount}张可用` : '暂无可用';
				})
			},

			// 格式化日期范围
			formatDateRange(item) {
				const start = item.startTime.split(' ')[0];
				const end = item.endTime.split(' ')[0];
				return `${start} 至 ${end}`;
			},

			// 获取地址列表
			getAddressList() {
				let data = {
					pageSize: 99999,
					pageNum: 1
				}
				this.$u.api.getAddressList(data).then(res => {
					this.dizhi = res.rows || [];
					let addressobj = this.dizhi.find(item => item.isMian === 1) || {};
					if (this.useraddress && this.useraddress.id) {
						addressobj = this.dizhi.find(item => item.id == this.useraddress.id) || addressobj;
					}
					this.objMainAddress = addressobj
					this.orderInfo.addressId = this.objMainAddress.id;
				})
			},

			// 地址点击事件
			addressEvent() {
				if (this.dizhi.length === 0) {
					uni.navigateTo({
						url: '/pages-mine/pages/address/add-or-update?type=add&returnUrl=order'
					});
				} else {
					uni.navigateTo({
						url: '/pages-mine/pages/address/list?isSelect=' + true
					});
				}
			},

			// 打开优惠券弹窗
			goDiscountsPage() {
				if (this.couponlength !== '暂无可用') {
					this.showOscart = true
				} else {
					uni.showToast({
						title: `暂无可用优惠券`,
						icon: "none",
						duration: 2000
					});
				}
			},

			// 选择优惠券
			changeDiscount(item, index) {
				// 已过期或已使用的优惠券不可点击
				if (item.status === 2) {
					uni.showToast({
						title: "该优惠券已过期",
						icon: "none"
					});
					return;
				}
				if (item.status === 1) {
					uni.showToast({
						title: "该优惠券已使用",
						icon: "none"
					});
					return;
				}
				// 未生效的优惠券不可点击
				if (this.isCouponNotStarted(item)) {
					uni.showToast({
						title: `该优惠券 ${item.startTime.split(' ')[0]} 起可用`,
						icon: "none"
					});
					return;
				}

				// 取消选中当前优惠券
				if (this.selectIndex === index) {
					this.selectIndex = null;
					this.orderInfo.couponId = "";
					// 恢复可用数量文本
					const validCount = this.couponList.filter(item =>
						item.status === 0 && !this.isCouponNotStarted(item)
					).length;
					this.couponlength = validCount > 0 ? `${validCount}张可用` : '暂无可用';
					// 恢复原价
					this.orderInfo.currentPrice = this.originalTotalPrice;
					this.showOscart = false;
					return;
				}

				// 验证使用条件（满减门槛）
				const currentPrice = parseFloat(this.originalTotalPrice);
				const minAmount = parseFloat(item.minUseAmount);
				const couponAmount = parseFloat(item.amount);

				if (currentPrice >= minAmount) {
					// 更新选中状态
					this.selectIndex = index;
					this.showOscart = false;
					// 更新优惠信息
					this.couponlength = `减￥${couponAmount}`;
					this.orderInfo.couponId = item.instanceId;
					// 计算优惠后价格（不低于0）
					this.orderInfo.currentPrice = Math.max(
						currentPrice - couponAmount,
						0
					).toFixed(2);
				} else {
					uni.showToast({
						title: `该优惠券需满${minAmount}元使用`,
						icon: "none"
					});
				}
			},

			// 选择预约时间
			changeTime(val) {
				this.orderInfo.reservationTime = val.result;
				this.calendarshow = false;
			},

			// 初始化日期范围
			initDateRange() {
				const today = new Date()
				this.minDate = this.formatDate(today)
				// 最大可选1年后的日期
				const oneYearLater = new Date(today)
				oneYearLater.setFullYear(today.getFullYear() + 1)
				this.maxDate = this.formatDate(oneYearLater)
				// 默认预约时间为今天
				this.orderInfo.reservationTime = this.minDate
			},

			// 格式化日期
			formatDate(date) {
				const year = date.getFullYear()
				const month = String(date.getMonth() + 1).padStart(2, '0')
				const day = String(date.getDate()).padStart(2, '0')
				return `${year}-${month}-${day}`
			},

			// 提交订单
			submitOrder() {
				if (!this.objMainAddress.id) {
					uni.showToast({
						title: '请先选择收货地址',
						icon: 'none'
					});
					return;
				}
				// 订单提交逻辑...
			}
		}
	};
</script>
<style lang="scss" scoped>
	.page {
		padding-bottom: 140rpx;
	}

	.time {
		margin-top: 28rpx;
		padding: 24rpx 0;
		display: flex;
		align-items: center;

		.timerigth {
			flex: 1;
			text-align: right;
			display: flex;
			align-items: center;
			justify-content: space-between;
			margin-left: 48rpx;
			color: #999;
		}
	}

	.address {
		margin-top: 24rpx;
		padding: 24rpx 30rpx;
		background-color: $app-theme-bg-color;

		.flexadd {
			display: flex;
			justify-content: space-between;
			margin-top: 24rpx;
			padding-top: 8rpx;
			border-top: 1px solid #cbcbcb;

			view:nth-child(1) {
				color: #999;
			}

			view:nth-child(2) {
				color: #000;
			}
		}
	}

	.goods {
		margin-top: 24rpx;
		background-color: $app-theme-bg-color;
		padding: 0 30rpx;
	}

	.yhq {
		background: #fff;
		margin: 24rpx;
		padding: 24rpx;
		border-radius: 24rpx;
	}

	.line {
		display: flex;
		justify-content: space-between;
		align-items: center;

		.label {
			font-size: 28rpx;
			color: $app-theme-text-black-color;
		}

		.value {
			padding-right: 24rpx;
			font-size: 28rpx;
			color: $app-theme-text-black-color;

			&.gray {
				color: $app-theme-card-gray-color;
				position: relative;

				.more {
					position: absolute;
					right: -16rpx;
					top: 50%;
					transform: translate(-50%, -50%);
				}
			}
		}
	}

	.listquan {
		margin: 60rpx;
		max-height: 650rpx;
		.item {
			width: 100%;
			height: 174rpx;
			background-image: url('@/pages-mine/static/discounts-bg.png');
			background-size: cover;
			margin-bottom: 30rpx;
			display: flex;
			justify-content: flex-start;
			align-items: flex-end;
			padding-bottom: 34rpx;
			position: relative;
			cursor: pointer;
			transition: all 0.2s;

			// 已过期样式
			&.expired {
				opacity: 0.6;
				background-image: url('@/pages-mine/static/discounts-bg.png'); // 可替换为过期背景图
			}

			// 已使用样式
			&.used {
				opacity: 0.6;
				background-image: url('@/pages-mine/static/discounts-bg.png'); // 可替换为已使用背景图
			}

			// 选中样式
			&.selected {
				box-shadow: 0 0 10rpx 2rpx rgba(96, 150, 225, 0.5);
			}

			.left {
				margin-right: 60rpx;
				margin-left: 50rpx;

				.discount {
					text:nth-child(1) {
						font-size: 22rpx;
						color: $app-theme-text-money-color;
					}

					text:nth-child(2) {
						font-size: 64rpx;
						color: $app-theme-text-money-color;
					}
				}

				.standard {
					font-size: 20rpx;
					color: $app-theme-card-gray-color;
				}
			}

			.right {
				.title {
					font-size: 32rpx;
					color: $app-theme-text-black-color;
					margin-bottom: 32rpx;
				}

				.date {
					font-size: 20rpx;
					color: $app-theme-card-gray-color;
				}

				.status {
					position: absolute;
					top: 20rpx;
					right: 20rpx;
					font-size: 24rpx;

					&.used {
						color: #999;
					}

					&.valid {
						color: #67c23a;
					}

					&:not(.used):not(.valid) {
						color: #f56c6c;
					}
				}
			}

			.select {
				height: 60rpx;
				width: 60rpx;
				background-image: url('@/pages-mine/static/discounts-select.png');
				background-size: cover;
				position: absolute;
				top: 4rpx;
				right: 4rpx;
				z-index: $app-zIndex-absolute;
			}
		}
	}

	.valid-date {
		font-size: 20rpx;
		color: #666;
		margin-top: 8rpx;
	}

	.condition {
		font-size: 22rpx;
		color: #888;
		margin-top: 8rpx;
	}

	// 状态标签样式
	.expired-tag,
	.used-tag,
	.not-start-tag {
		position: absolute;
		top: 0;
		right: 0;
		padding: 4rpx 16rpx;
		border-radius: 0 0 0 16rpx;
		font-size: 22rpx;
		color: white;
	}

	.expired-tag {
		background: rgba(245, 108, 108, 0.7); // 红色系-过期
	}

	.used-tag {
		background: rgba(153, 153, 153, 0.7); // 灰色系-已使用
	}

	.not-start-tag {
		background: rgba(0, 100, 255, 0.7); // 蓝色系-未生效
	}

	// 空状态样式
	.empty-coupon {
		text-align: center;
		padding: 60rpx 0;
		color: #999;

		.u-icon {
			margin-bottom: 20rpx;
		}
	}
</style>