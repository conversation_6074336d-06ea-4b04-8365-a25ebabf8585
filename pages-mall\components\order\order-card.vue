<template>
	<view class="slot" @click.stop="goOrderInfo1">
		<!-- 商品卡片 -->
		<OrderGoodsCard :data="data" showBorderBottom>
			<view class="status-slot">
				<text v-if="data.status == 1">待支付</text>
				<text v-if="data.status == 2">待服务</text>
				<text v-if="data.status == 3">已取消</text>
				<text v-if="data.status == 4">服务中</text>
				<text v-if="data.status == 5">已服务</text>
				<text v-if="data.status == 6">服务完成</text>
				<text v-if="data.status == 7">用户评价</text>
			</view>
		</OrderGoodsCard>

		<!-- 操作按钮区域 -->
		<!-- 待支付 -->
		<view class="operate" v-if="data.status == 1">
			<u-button type="default" style="margin-right: 24rpx;" size="small" shape="circle"
				@click.stop="cancelOrder1()">取消订单</u-button>
			<u-button type="primary" size="small" shape="circle" @click.stop="goPay1()">去付款</u-button>
		</view>

		<!-- 待服务 -->
		<view class="operate" v-if="data.status == 2">
			<u-button type="primary" size="small" shape="circle" @click.stop="pushDelivery1()">提醒服务</u-button>
		</view>

		<!-- 已取消 -->
		<view class="operate" v-if="data.status == 3">
			<u-button type="default" style="margin-right: 24rpx;" size="small" shape="circle"
				@click.stop="deleteOrder1()">删除记录</u-button>
			<u-button type="primary" size="small" shape="circle" @click.stop="goOrderInfo1()">查看详情</u-button>
		</view>

		<!-- 服务中/已服务（用户视角） -->
		<view class="operate" v-if="(data.status == 4 || data.status == 5) && Tid==1">
			<u-button type="primary" size="small" shape="circle" @click.stop="pcconfirm()">用户确认</u-button>
		</view>

		<!-- 技师视角接单 -->
		<view class="operate" v-if="Tid==2">
			<u-button type="primary" size="small" shape="circle" @click.stop="confirmation()">接单</u-button>
		</view>

		<!-- 服务完成（去评价） -->
		<view class="operate" v-if="data.status == 6">
			<u-button type="primary" size="small" shape="circle" @click.stop="goEvaluate1()">去评价</u-button>
		</view>

		<!-- 支付加载遮罩（uView 1.0适配） -->
		<view class="pay-mask" v-if="isPayLoading">
			<view class="loading-container">
				<!-- uView 1.0使用u-loading组件 -->
				<u-loading mode="circle" size="36"></u-loading>
				<text class="loading-text">支付处理中...</text>
			</view>
		</view>

		<!-- 取消订单确认弹窗 -->
		<u-modal v-model="orshow" :show-cancel-button="true" :content="content" @confirm="confirm"></u-modal>

		<!-- 提示组件 -->
		<u-toast ref="uToast" />
	</view>
</template>

<script>
	import {
		orderOperate
	} from '@/pages-mall/mixins/order-operate.js';
	import OrderGoodsCard from '@/pages-mall/components/order/order-goods-card.vue';
	import {
		HTTP_URL_PROD
	} from '@/api/config';

	export default {
		name: 'order-card',
		mixins: [orderOperate],
		components: {
			OrderGoodsCard
		},
		props: {
			// 订单数据
			data: {
				type: Object,
				default: () => ({})
			},
			// 角色标识（1-用户订单 2-技师订单）
			Tid: {
				type: String,
				default: '1'
			},
		},
		data() {
			return {
				orshow: false, // 取消订单弹窗开关
				content: '确认取消当前订单?', // 弹窗内容
				isPayLoading: false // 支付加载状态
			};
		},
		methods: {
			/** 用户确认服务 */
			pcconfirm() {
				this.$u.api.postconfirm(this.data.id).then(res => {
					if (res.code === 200) {
						this.$refs.uToast.show({
							title: '已确认',
							type: 'success',
						});
						this.$emit('change', this.data.status);
					}
				});
			},

			/** 技师接单 */
			confirmation() {
				this.$u.api.postconfirmation(this.data.id).then(res => {
					if (res.code === 200) {
						this.$refs.uToast.show({
							title: '接单成功',
							type: 'success',
						});
						this.$emit('change', this.data.status);
					}
				});
			},

			/** 跳转订单详情 */
			goOrderInfo1() {
				uni.navigateTo({
					url: '/pages-mall/pages/order/detail?item=' + encodeURIComponent(JSON.stringify(this.data))
				});
			},

			/** 提醒服务 */
			pushDelivery1() {
				this.$refs.uToast.show({
					title: '已提醒',
					type: 'success',
				});
			},

			/** 跳转评价页面 */
			goEvaluate1() {
				uni.setStorageSync('orderInfo', this.data);
				uni.navigateTo({
					url: '/pages-mall/pages/evaluate/add'
				});
			},

			/** 显示取消订单弹窗 */
			cancelOrder1() {
				this.orshow = true;
			},

			/** 确认取消订单 */
			confirm() {
				this.$u.api.postcancellationoforder(this.data.id).then(res => {
					if (res.code === 200) {
						this.orshow = false;
						this.$refs.uToast.show({
							title: '已取消',
							type: 'success',
						});
						this.$emit('change', this.data.status);
					}
				});
			},

			/** 删除订单 */
			deleteOrder1() {
				this.$u.api.deleteOrder(this.data.id).then(res => {
					if (res.code === 200) {
						this.$refs.uToast.show({
							title: '删除成功',
							type: 'success',
						});
						this.$emit('change', this.data.status);
					}
				});
			},

			/** 去支付（完整支付流程） */
			async goPay1() {
				try {
					this.isPayLoading = true;

					// 1. 获取微信登录code
					const code = await this.getWxCode();
					if (!code) throw new Error('获取支付凭证失败');

					// 2. 获取支付参数
					const payParams = await this.getPayParams(code, this.data.id);
					// 3. 发起微信支付
					const payResult = await this.requestPayment(payParams);

					// 4. 支付成功处理
					if (payResult.errMsg === 'requestPayment:ok') {
						this.$refs.uToast.show({
							title: '支付成功',
							type: 'success'
						});
						this.$emit('change', 1); // 通知父组件更新列表
						setTimeout(() => this.goOrderInfo1(), 1500);
					}

				} catch (err) {
					// 忽略用户主动取消的情况
					if (!err.message.includes('取消')) {
						this.$refs.uToast.show({
							title: err.message || '支付失败',
							type: 'error'
						});
					}
				} finally {
					this.isPayLoading = false;
				}
			},

			/** 获取微信登录code */
			getWxCode() {
				return new Promise((resolve, reject) => {
					wx.login({
						success: (res) => {
							res.code ? resolve(res.code) : reject(new Error('获取登录凭证失败'));
						},
						fail: () => reject(new Error('微信登录接口调用失败'))
					});
				});
			},

			/** 获取支付参数 */
			getPayParams(code, orderId) {
				return new Promise((resolve, reject) => {
					uni.request({
						url: HTTP_URL_PROD + '/system/wechat/pay/createPrepay',
						method: 'POST',
						header: {
							'Content-Type': 'application/x-www-form-urlencoded',
							'Authorization': this.$store.state.user.token
						},
						data: {
							code,
							orderId
						},
						success: (res) => {
							if (res.data?.code === 200 && res.data?.data) {
								resolve(res.data.data);
							} else {
								reject(new Error(res.data?.msg || '获取支付参数失败'));
							}
						},
						fail: () => reject(new Error('支付接口调用失败'))
					});
				});
			},

			/** 调用微信支付 */
			requestPayment(payParams) {
				return new Promise((resolve, reject) => {
					wx.requestPayment({
						timeStamp: payParams.timeStamp.toString(),
						nonceStr: payParams.nonceStr,
						package: payParams.package,
						signType: payParams.signType || 'MD5',
						paySign: payParams.paySign,
						success: (res) => resolve(res),
						fail: (err) => reject(new Error(`支付失败: ${err.errMsg}`))
					});
				});
			}
		}
	};
</script>

<style lang="scss" scoped>
	.slot {
		background-color: $app-theme-bg-color;
		border-radius: 16rpx;
		box-shadow: $app-theme-shadow;
		padding: 30rpx;
		margin-bottom: 30rpx;
		position: relative; // 用于支付遮罩定位
	}

	.status-slot {
		text-align: right;
		color: $app-theme-color;
		font-size: 28rpx;
		font-weight: 500;
	}

	.operate {
		display: flex;
		justify-content: flex-end;
		margin-top: 20rpx;
	}

	/* 支付加载遮罩（uView 1.0适配） */
	.pay-mask {
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background-color: rgba(255, 255, 255, 0.85);
		display: flex;
		justify-content: center;
		align-items: center;
		z-index: 10;
		border-radius: 16rpx;
	}

	.loading-container {
		display: flex;
		flex-direction: column;
		align-items: center;

		.loading-text {
			font-size: 28rpx;
			color: #666;
			margin-top: 20rpx;
		}
	}

	/* 解决按钮点击事件冒泡问题 */
	::v-deep .u-button {
		pointer-events: auto !important;
	}
</style>