<template>
	<view class="shop-cart">
		<Navbar title='购物车'></Navbar>
		<view class="shop-cart-list">
			<view class="shop-cart-item" v-for="cart in cartList">
				<view class="shop">
					<u-icon color='#f2cb8a' name="home-fill"></u-icon>
					{{cart.shopName}}
				</view>
				<view class="cart">
					<uni-swipe-action>
						<view class="cart-item" v-for="(item,index) in cart.cartList" :key="item.id">
							<view class="check">
								<u-checkbox checked shape="circle" size='30' v-model='item.check' activeColor='#ffba4d' inactiveColor='#ffba4d'/>
							</view>
							<view class="img">
								<img style="width: 100%;height: 180rpx;" :src="baseUrl + item.img" alt="" />
							</view>
							<view class="detail">
								<view class="name">
									{{item.name}}
								</view>
								<view class="price">
									{{item.price}}元
								</view>
							</view>
							<view class="input">
								<view class='jian'  type="warning" shape="circle" @click="handleJian(item)">-</view>
								<u-input :clearable="false" class="number" type='number' v-model="item.count" placeholder=" "></u-input>
								<view class='add' type="warning" shape="circle" @click="handleAdd(item)">+</view>
							</view>
						</view>	
					</uni-swipe-action>
				</view>
				<view class="total">
					<view class="text">
						合计: <text>{{ getTotal(cart.cartList) }}元</text> (起送价:{{cart.cartList[0].qiSong}}元)
					</view>
<!-- 					<view class="button" @click="submit(cart)">
						去下单
					</view> -->
					<view class="button" @click="submit(cart)">
						去预约
					</view>
				</view>
			</view>
			<u-empty :show="cartList.length <= 0" mode="list" text='暂无商品'>
			</u-empty>
		</view>
	</view>
</template>

<script>
	import { baseUrl } from '@/api/config';
	export default {
		data() {
			return {
				cartList:[],
				baseUrl,
				options: [
					{
						text: "收藏",
					},
					{
						text: "删除",
					},
				],
				shopId:0
			}
		},
		onShow() {
			this.$u.api.getCart().then(res=>{
				this.cartList = res.data
				this.cartList.forEach(i=>{
					i.cartList = i.cartList.map(item=>{
						return {...item,check:true}
					})
				})
			})
		},
		computed:{
			shop(){
				return this.$store.state.shop.shop
			},
		},
		methods: {
			handleAdd(item){
				item.count ++
			},
			handleJian(item){
				if(item.count>1) item.count--
				else {
					uni.showModal({
						title: '提示',
						content: '确认删除该商品吗',
						success: (res)=> {
							if (res.confirm) {
								this.$u.api.deletetCart(item.id).then(()=>{
									this.$u.api.getCart().then(res=>{
										this.cartList = res.data
										this.cartList.forEach(i=>{
											i.cartList = i.cartList.map(item=>{
												return {...item,check:true}
											})
										})
									})
								})
							}
						}
					});
				}
			},
			open(item,index){
				console.log(item,index)
			},
			submit(cart){
				console.log(cart)
				let orderList = cart.cartList.filter(i=>i.check)
				if(orderList.some(i=>i.count < 0 || !i.count)){
					uni.$u.toast('购买数量需大于0')
					return
				}
				let shopId = cart.cartList[0].storeStoreClsId.split(',')[0]
				if(orderList.length==0){
					uni.$u.toast('请选择要购买的商品')
					return
				}
				uni.navigateTo({
					url:'/pages-zone/pages/store/cart/cart-order?list=' + encodeURIComponent(JSON.stringify(orderList)) + '&shopId=' + shopId,
				})
			},
			getTotal(list){
				let sum =0 
				list.forEach(item=>{
					sum+=item.check ? item.count * item.price : 0
				})
				return sum
			}
		}
	}
</script>

<style scoped lang="scss">
.shop{
	height: 80rpx;
	display: flex;
  align-items: center;
	color: #f2cb8a;
	font-size: 36rpx;
	padding-left: 20rpx;
	background-color: #fff;
}
.cart{

	.cart-item{
		width: 100%;
		display: grid;
		background: #f5f5f5;
		height: 200rpx;
		grid-template-columns: 10% 20% 30% 40%;
		
		.check{
			display: flex;
			margin-left: 20rpx;
		}
		
		.img{
			display: flex;
			align-items: center;
		}
		
		.detail{
			display: flex;
			flex-direction: column;
			justify-content: space-evenly;
		}
		
		.input{
			display: flex;
			align-items: flex-start;
			align-items: center;
			
			.number{
				width: 80rpx;
				background: #ebecee;
				margin: 0 10rpx;
				height: 50rpx;
				display: flex;
				align-items: center;
				
				& ::v-deep input{
					text-align: center;
					font-size: 40rpx;
				}
			}
			
			.add,.jian{
				background: #ebecee;
				border-radius: 10rpx;
				font-size: 50rpx;
				width: 60rpx;
				height: 50rpx;
				display: flex;
				align-items: center;
				justify-content: center;
			}
		}
	}
}

.total{
	display: flex;
	justify-content: space-between;
	background-color: #fff;
	height: 80rpx;
	padding: 0 20rpx;
	align-items: center;
	
	text{
		color: red;
	}
	
	.button{
		width: 100rpx;
		height: 60rpx;
		background: #f56c6c;
		color: #fff;
		display: flex;
		align-items: center;
		justify-content: center;
		border-radius: 10rpx;
	}
}
</style>
