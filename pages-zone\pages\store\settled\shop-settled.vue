<template>
	<view class="shop-settled">
		<navbar title="商家入驻"></navbar>
		<!-- 表单 -->
		<view class="form">
			<u-form label-position="left" :model="form" :rules="rules" ref="formRef" label-width="180rpx">
				<view class="form-block">
					<u-form-item required prop='contacts' label="联系人">
						<u-input v-model="form.contacts" placeholder="请选择联系人" />
					</u-form-item>
					<u-form-item required prop='phone' label="联系电话">
						<u-input v-model="form.phone" placeholder="请输入联系电话" />
					</u-form-item>
					<u-form-item required prop='name' label="商家名称">
						<u-input v-model="form.name" placeholder="请输入商家名称" />
					</u-form-item>
					<u-form-item required prop='storeShopClsId' label="商家分类">
						<u-input v-model="form.storeShopClsId" type="select" placeholder="请选择商家分类"
							@click="categoryPicker = true" />
					</u-form-item>
					<u-form-item required prop='address' label="商家地址">
						<u-input v-model="form.address" placeholder="请输入商家地址" />
					</u-form-item>
					<u-form-item required prop='qiSong' label="起送费">
						<u-input type='number' v-model="form.qiSong" placeholder="请输入起送费" />
					</u-form-item>
					<u-form-item required prop='peiSong' label="配送费">
						<u-input type="number" v-model="form.peiSong" placeholder="请输入配送费" />
					</u-form-item>
					<u-form-item required prop='businessTime' label="营业时间">
						<u-input v-model="form.businessTime" type="select" placeholder="请选择营业时间"
							@click="businessTimePicker = true" />
					</u-form-item>
					<u-form-item required prop='notice' label="商家简介">
						<u-input v-model="form.notice" placeholder="请输入商家简介" />
					</u-form-item>
				</view>
				<view class="form-block">
					<u-form-item label="证件照片" :border-bottom="false" labelPosition='top' required>
						<!-- 图片上传 -->
						<view class="upload">
							<u-upload width="300" height="300" max-count="1" :max-size="1024 * 1024 * 10"
								:action="uploadUrl" :auto-upload="true" :header="headers" uploadText='品牌logo'
								@on-success="uploadBgPicSuccessLogo" :deleteConfirmBtnColor="themeColor"></u-upload>
							<u-upload width="300" height="300" max-count="1" :max-size="1024 * 1024 * 10"
								:action="uploadUrl" :auto-upload="true" :header="headers" uploadText='商家背景图'
								@on-success="uploadBgPicSuccessBack" :deleteConfirmBtnColor="themeColor"></u-upload>
							<u-upload width="300" height="300" max-count="1" :max-size="1024 * 1024 * 10"
								:action="uploadUrl" :auto-upload="true" :header="headers" uploadText='营业执照/从业资格证'
								@on-success="uploadBgPicSuccessLicense" :deleteConfirmBtnColor="themeColor"></u-upload>
						</view>
					</u-form-item>
				</view>
			</u-form>
		</view>
		<!-- 按钮 -->
		<view class="btn">
			<u-button class='button' type="warning" shape="circle" @click="submit"><text>提交资料</text></u-button>
		</view>
		<view class="agreement">
			<u-checkbox size='30' v-model='agreement' activeColor='#ffba4d' inactiveColor='#ffba4d'
				@click="agreement = !agreement">
				已阅读并同意<text style="color: #ffba4d;">《家事速配商家入驻协议》</text>
			</u-checkbox>
		</view>
		<!-- <CityPicker v-model="cityPicker"></CityPicker> -->
		<!-- picker -->
		<u-picker v-model="categoryPicker" mode="selector" :range="typeOps" range-key="label" @confirm="confirmType"
			:confirm-color="themeColor"></u-picker>
		<u-picker mode="time" :params="{hour: true,minute: true}" v-model="businessTimePicker"
			@confirm="confirmTime"></u-picker>
	</view>
</template>

<script>
	import {
		baseUrl
	} from '@/api/config';
	import {
		getToken
	} from '@/utils/auth';
	import CityPicker from '@/pages-mine/components/city-picker.vue';
	export default {
		data() {
			return {
				themeColor: this.$appTheme.appThemeColor,
				// 类型选择
				categoryPicker: false,
				businessTimePicker: false,
				businessTimeFalg: true,
				cityPickerLabel: "",
				cityPicker: false,
				agreement: false,
				typeOps: [{
						label: '生活',
						value: 4
					},
					{
						label: '音乐',
						value: 5
					},
				],
				headers: {
					Authorization: getToken()
				},
				// 表单
				form: {
					contacts: '',
					phone: '',
					name: '',
					storeShopClsId: '',
					address: '',
					notice: '',
					logoImg: null,
					backImg: null,
					licenseImg: null,
					qiSong: null,
					peiSong: null,
					// region: '',
					businessTime: null,
				},
				rules: {
					contacts: [{
						required: true,
						message: '请输入联系人',
						trigger: ['blur', 'change']
					}],
					phone: [{
							required: true,
							message: '请输入联系电话',
							trigger: ['blur', 'change']
						},
						{
							pattern: /^1[3456789]\d{9}$/,
							message: "手机号格式不正确",
							trigger: "blur"
						}
					],
					name: [{
						required: true,
						message: '请输入商家名称',
						trigger: ['blur', 'change']
					}],
					storeShopClsId: [{
						required: true,
						message: '请选择商家分类',
						trigger: ['blur', 'change']
					}],
					address: [{
						required: true,
						message: '请输入商家地址',
						trigger: ['blur', 'change']
					}],
					notice: [{
						required: true,
						message: '请输入商家简介',
						trigger: ['blur', 'change']
					}],
					qiSong: [{
							required: true,
							message: '请输入起送费',
							trigger: ['blur', 'change']
						},
						{
							pattern: /^-?\d+\.?\d*$/,
							message: '请输入正确数据',
							trigger: 'blur'
						},
					],
					peiSong: [{
							required: true,
							message: '请输入配送费',
							trigger: ['blur', 'change']
						},
						{
							pattern: /^-?\d+\.?\d*$/,
							message: '请输入正确数据',
							trigger: 'blur'
						},
					],
					businessTime: [{
						required: true,
						message: '请选择营业时间',
						trigger: ['blur', 'change']
					}],
				},
				// 上传地址
				uploadUrl: baseUrl + '/common/upload',
				// 省市区
			};
		},
		components: {
			CityPicker
		},
		onReady() {
			this.$refs.formRef.setRules(this.rules)
		},
		onLoad() {
			this.$u.api.getShopCategory().then(res => {
				this.typeOps = res.data.list.map(i => ({
					label: i.name,
					value: i.id
				}))
			})
		},
		methods: {
			// 切换类型
			confirmType(e) {
				this.form.storeShopClsId = this.typeOps[e[0]].label;
			},
			confirmTime(e) {
				if (this.businessTimeFalg) {
					this.form.businessTime = e.hour + ':' + e.minute
					setTimeout(() => {
						this.businessTimePicker = true
						uni.$u.toast('请选择营业结束时间')
						this.businessTimeFalg = !this.businessTimeFalg
					}, 500)
				} else {
					if (this.form.businessTime > (e.hour + ':' + e.minute)) {
						uni.$u.toast('结束时间不能早于开始时间')
						return
					}
					this.form.businessTime = this.form.businessTime + '-' + e.hour + ':' + e.minute
					this.businessTimeFalg = !this.businessTimeFalg
				}
			},
			uploadBgPicSuccessLogo(data) {
				this.form.logoImg = data.fileName
			},
			uploadBgPicSuccessBack(data) {
				this.form.backImg = data.fileName
			},
			uploadBgPicSuccessLicense(data) {
				this.form.licenseImg = data.fileName
			},
			cityChange(e) {
				this.cityPickerLabel = '上虞区-' + e.province.label + '-' + e.city.label;
				this.form.region = this.cityPickerLabel
			},
			submit() {
				if (!this.agreement) {
					uni.$u.toast('请先同意协议')
					return
				}
				console.log(this.form)
				this.$refs.formRef.validate(valid => {
					if (valid) {
						if (!this.form.logoImg || !this.form.backImg || !this.form.licenseImg) {
							uni.$u.toast('请上传相应的图片')
							console.log('请上传相应的图片')
							return
						}
						this.$u.api.applyShop({
							...this.form,
							storeShopClsId: this.typeOps.find(i => i.label === this.form.storeShopClsId)
								.value
						}).then(res => {
							if (res.code == 200) {
								uni.$u.toast('申请入驻成功')
								uni.navigateBack(-1)
							} else {
								uni.$u.toast(res.msg)
							}
						})
					}
				})
			}
		}
	};
</script>

<style lang="scss" scoped>
	.shop-settled {
		background: #f3f5f7;
		padding: 30rpx;

		.form {

			.form-block {
				background: #fff;
				border-radius: 10rpx;
				margin: 0 auto;
				padding: 0 30rpx;
				margin-bottom: 30rpx;

				.upload {
					display: grid;
					grid-template-columns: 1fr 1fr;
				}
			}
		}
	}

	.btn {
		padding: 60rpx 0rpx;

		.button {
			background: #ffba4d;

			text {
				font-weight: 800;
				color: #000;
				font-size: 40rpx
			}

			&:hover {
				background: #ffba4d;
			}
		}
	}

	.agreement {
		text-align: center;

		& ::v-deep .u-checkbox__icon-wrap {
			border: 1px solid #ffba4d;
		}
	}
</style>