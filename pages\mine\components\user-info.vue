<template>
	<view class="slot">
		<view class="left" v-if="isLogin" @click="$u.route('/pages-mine/pages/mine')">
			<u-avatar size="118" :src="img"></u-avatar>
			<view class="info">
				<view class="username">{{ data.nickName || '演示用户' }}</view>
				<view class="desc">{{ data.phonenumber || '这个人很懒,没留下什么这个人很懒' }}</view>
			</view>
		</view>
		<view class="left" v-else @click="$u.route('/pages/login/index')">
			<u-avatar size="118"></u-avatar>
			<view class="info">
				<view class="username">登录</view>
				<view class="desc">点击登录跳转至登录页面</view>
			</view>
		</view>
		<view @click="useringo" class="right"><u-icon size="20" name="arrow-right" color="#000"></u-icon></view>
	</view>
</template>

<script>
	import {
		HTTP_URL_PROD
	} from '@/api/config';
	export default {
		name: 'user-info',
		props: {
			data: {
				type: Object,
				default: () => ({})
			},
		},
		data(){
			return {
				isLogin:null,
			}
		},
		computed: {
			img() {
				return this.data.avatar ? HTTP_URL_PROD + this.data.avatar : '';
			}
		},
		mounted() {
			this.onShowHandler = () => {
				console.log('Received global onShow → 刷新列表');
				this.isLogin = this.$store.state.user.token ? true : false;
			};
			uni.$on('onShow', this.onShowHandler)
		},
		beforeDestroy() {
			// 解绑事件，清理定时器
			uni.$off('onShow', this.onShowHandler);
			this.stopScroll();
		},
		methods: {
			useringo() {
				const token = uni.getStorageSync("App-Token")
				if (token) {
					uni.navigateTo({
						url: `/pages-mine/pages/mine`
					});
				} else {
					uni.navigateTo({
						url: `/pages/login/index`
					});
				}
			},
		},
	};
</script>
<style lang="scss" scoped>
	.slot {
		color: $app-theme-text-white-color;
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 48rpx;
		position: relative;
		z-index: $app-zIndex-normal;

		.left {
			display: flex;
			align-items: center;

			.info {
				margin-left: 24rpx;
				display: flex;
				align-content: space-between;
				flex-wrap: wrap;

				.username {
					font-size: 32rpx;
					width: 100%;
					color: #000;
					margin-bottom: 16rpx;
				}

				.desc {
					font-size: 24rpx;
					width: 100%;
					color: #000;
					display: -webkit-box;
					overflow: hidden;
					-webkit-line-clamp: 1;
					-webkit-box-orient: vertical;
				}
			}
		}

		.right {
			display: flex;
			align-items: center;
		}
	}
</style>