<template>
	<view class="page">
		<Navbar title="优惠卷"></Navbar>
		<!-- 标签切换 -->
		<view class="tabs">
			<view 
				:class="['tab', {active: activeTab === 0}]" 
				@click="activeTab = 0"
			>未使用</view>
			<view 
				:class="['tab', {active: activeTab === 1}]" 
				@click="activeTab = 1"
			>已使用</view>
			<view 
				:class="['tab', {active: activeTab === 2}]" 
				@click="activeTab = 2"
			>无法使用</view>
		</view>
		
		<view class="listquan">
			<view 
				class="item" 
				v-for="(item, index) in filteredCouponList" 
				:key="index"
				:class="[getItemClass(item)]"
			>
				<view class="left">
					<view class="discount">
						<text>￥</text>
						<text>{{ item.amount }}</text>
					</view>
					<view class="standard">满{{ item.minUseAmount }}可用</view>
				</view>
				<view class="right">
					<view class="title">{{ item.title||'满减优惠券' }}</view>
					<view class="date">
						{{ formatDate(item.startTime) }} - {{ formatDate(item.endTime) }}
					</view>
					<!-- 状态说明 -->
					<view class="status" :class="[getStatusTextClass(item)]">
						{{ getCouponStatusText(item) }}
					</view>
				</view>
				<view class="select" :class="[getStatusClass(item)]">
					{{ getStatusTagText(item) }}
				</view>
			</view>
			
			<!-- 空状态提示 -->
			<view v-if="filteredCouponList.length === 0" class="empty">
				<u-empty mode="coupon" text="暂无优惠券"></u-empty>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			couponList: [],
			activeTab: 0, // 0: 未使用, 1: 已使用, 2: 无法使用
			now: new Date().getTime() // 当前时间戳
		};
	},
	computed: {
		// 根据标签和status筛选优惠券
		filteredCouponList() {
			return this.couponList.filter(item => {
				// 确保item和status存在
				if (!item || item.status === undefined) return false;
				
				// 主要根据status筛选
				if (this.activeTab === 0) return item.status === 0;
				if (this.activeTab === 1) return item.status === 1;
				if (this.activeTab === 2) return item.status === 2;
				return true;
			});
		}
	},
	mounted() {
		// 模拟接口请求
		this.$u.api.getCouponList().then(res => {
			// 确保返回数据格式正确
			if (Array.isArray(res.data)) {
				this.couponList = res.data;
			} else {
				this.couponList = [];
			}
		}).catch(() => {
			this.couponList = [];
		});
	},
	methods: {
		// 格式化日期
		formatDate(dateStr) {
			if (!dateStr) return '';
			return dateStr.split(" ")[0];
		},
		
		// 判断优惠券是否过期（辅助判断）
		isCouponExpired(item) {
			if (!item || !item.endTime) return false;
			const endTime = new Date(item.endTime).getTime();
			return endTime < this.now;
		},
		
		// 判断优惠券是否未生效（辅助判断）
		isCouponNotActive(item) {
			if (!item || !item.startTime) return false;
			const startTime = new Date(item.startTime).getTime();
			return startTime > this.now;
		},
		
		// 获取状态显示文本
		getCouponStatusText(item) {
			if (!item || item.status === undefined) return '未知状态';
			
			switch(item.status) {
				case 0:
					if (this.isCouponNotActive(item)) {
						return `未生效 (${this.formatDate(item.startTime)}开始)`;
					}
					if (this.isCouponExpired(item)) {
						return '已过期';
					}
					const daysLeft = Math.ceil((new Date(item.endTime).getTime() - this.now) / (1000 * 60 * 60 * 24));
					return `有效期剩余 ${daysLeft} 天`;
					
				case 1:
					return '已使用';
					
				case 2:
					if (this.isCouponExpired(item)) {
						return '已过期';
					}
					return '无法使用';
					
				default:
					return '未知状态';
			}
		},
		
		// 获取状态标签文本
		getStatusTagText(item) {
			if (!item || item.status === undefined) return '未知';
			
			switch(item.status) {
				case 0:
					return this.isCouponExpired(item) ? '已过期' : '待使用';
				case 1:
					return '已使用';
				case 2:
					return '无法使用';
				default:
					return '未知';
			}
		},
		
		// 获取状态标签样式类（返回类名字符串）
		getStatusClass(item) {
			if (!item || item.status === undefined) return '';
			
			switch(item.status) {
				case 0:
					return this.isCouponExpired(item) ? 'expired-tag' : '';
				case 1:
					return 'used-tag';
				case 2:
					return 'unavailable-tag';
				default:
					return '';
			}
		},
		
		// 获取整个优惠券项的样式类
		getItemClass(item) {
			if (!item || item.status === undefined) return '';
			
			const classes = [];
			if (item.status === 1) classes.push('used');
			if (item.status === 2) classes.push('unavailable');
			if (this.isCouponExpired(item)) classes.push('expired');
			
			return classes;
		},
		
		// 获取状态文本的样式类
		getStatusTextClass(item) {
			if (!item || item.status === undefined) return '';
			
			if (item.status === 1 || item.status === 2 || this.isCouponExpired(item)) {
				return 'inactive-status';
			}
			return 'active-status';
		}
	}
};
</script>

<style lang="scss" scoped>
.page {
	background-color: #f5f5f5;
	padding-bottom: 20rpx;
}

.tabs {
	display: flex;
	justify-content: center;
	margin: 30rpx 0;
	background-color: #fff;
	padding: 20rpx 0;
	
	.tab {
		padding: 10rpx 30rpx;
		margin: 0 10rpx;
		border-radius: 50rpx;
		font-size: 28rpx;
		color: #666;
		border: 1px solid #eee;
		
		&.active {
			background-color: $app-theme-color;
			color: #fff;
			border-color: $app-theme-color;
		}
	}
}

.listquan {
	margin: 20rpx;
	
	.empty {
		margin-top: 100rpx;
	}

	.item {
		width: 100%;
		height: 174rpx;
		background-image: url('@/pages-mine/static/discounts-bg.png');
		background-size: cover;
		margin-bottom: 30rpx;
		display: flex;
		justify-content: flex-start;
		align-items: flex-end;
		padding-bottom: 34rpx;
		position: relative;
		overflow: hidden;
		border-radius: 16rpx;
		
		// 已使用样式
		&.used {
			background: #f0f0f0;
			
			.left .discount text,
			.left .standard,
			.right .title,
			.right .date {
				color: #aaa !important;
			}
		}
		
		// 无法使用样式
		&.unavailable {
			background: #f5f5f5;
			
			.left .discount text,
			.left .standard,
			.right .title,
			.right .date {
				color: #ccc !important;
			}
		}
		
		// 已过期样式
		&.expired {
			background: #e0e0e0;
			opacity: 0.8;
			
			.left .discount text,
			.left .standard,
			.right .title,
			.right .date {
				color: #888 !important;
			}
		}

		.left {
			width: 25%;
			display: flex;
			flex-direction: column;
			align-items: center;
			margin-left: 20rpx;

			.discount {
				text:nth-child(1) {
					font-size: 22rpx;
					color: $app-theme-text-money-color;
				}

				text:nth-child(2) {
					font-size: 58rpx;
					color: $app-theme-text-money-color;
				}
			}

			.standard {
				font-size: 20rpx;
				color: $app-theme-card-gray-color;
			}
		}

		.right {
			flex: 1;
			margin-left: 15rpx;
			padding-right: 30rpx;
			
			.title {
				font-size: 32rpx;
				color: $app-theme-text-black-color;
				margin-bottom: 10rpx;
				font-weight: bold;
			}

			.date {
				font-size: 22rpx;
				color: $app-theme-card-gray-color;
				margin-bottom: 8rpx;
			}
			
			.status {
				font-size: 22rpx;
				font-weight: bold;
				
				&.active-status {
					color: $app-theme-color;
				}
				
				&.inactive-status {
					color: #999;
				}
			}
		}

		.select {
			height: 40rpx;
			width: 150rpx;
			background-color: $app-theme-color;
			color: #fff;
			position: absolute;
			top: 15rpx;
			right: -30rpx;
			z-index: 10;
			display: flex;
			align-items: center;
			justify-content: center;
			font-size: 20rpx;
			font-weight: bold;
			transform: rotate(45deg);
			letter-spacing: 2rpx;
			box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.08);
		}
		
		// 状态标签样式
		.expired-tag {
			background-color: #999;
		}
		.used-tag {
			background-color: #666;
		}
		.unavailable-tag {
			background-color: #ccc;
		}
	}
}
</style>
