<template>
	<view class="page">
		<!-- 全局一般导航栏 -->
		<Navbar title="搜索商品"></Navbar>
		<!-- 全局搜索框 -->
		<Search placeholder="搜索商品、品牌" showCancelBtn @change="searchChange" @search="goSearchResult"></Search>

		<view class="selected-service">
			<view v-for="(item, index) in goodsList" :key="index" class="service-item" @click="chandetail(item)">
				<image :src="https+item.cover" class="service-image"></image>
				<view class="service-info">
					<text class="service-name">{{item.title}}</text>
					<view class="service-price">{{item.amount}}元/{{item.unit||''}}</view>
					<view class="service-meta">
						<text>已售{{item.sold}} 好评率{{item.applauseRate==0?'100%':item.applauseRate+'%'}}</text>
					</view>
				</view>
			</view>
		</view>
		<u-modal v-model="showRemoveHistory" content="是否清空最近搜索记录？" @confirm="removeHistory" show-cancel-button
			:confirm-color="confirmColor"></u-modal>
	</view>
</template>

<script>
	import Search from '@/components/search/search.vue';
	import TitleOperate from '@/components/title-operate.vue';
	// import Tag from '@/components/tag.vue';
	export default {
		components: {
			Search,
			TitleOperate,
			// Tag
		},
		data() {
			return {
				// 直接写在html里不识别，需要在data里转一下
				confirmColor: this.$appTheme.appThemeColor.toString(),
				// 是否显示删除最近搜索的模态框
				showRemoveHistory: false,
				// 是否正在搜索，用于判断是否显示最近搜索和热门搜索
				isInput: false,
				// 最近搜索
				historyList: ['维生素片', '汤臣倍健', '鸡副产品', '鸭副产品', '调理鸡腿'],
				// 热门搜索
				hotList: ['冻品鸭腿', '冻品鸡腿', '鸡副产品', '鸭副产品', '调理鸡腿'],
				goodsList: []
			};
		},
		methods: {
			// 搜索框正在输入
			searchChange(e) {
				if (e) {
					this.isInput = true;
				} else {
					this.isInput = false;
				}
			},
			chandetail(item) {
				console.log(item, '商品的参数')
				uni.navigateTo({
					url: '/pages-mall/pages/indexdetails/detail?item=' + encodeURIComponent(JSON.stringify(item))
				})
			},
			// 清空最近搜索记录
			removeHistory() {
				this.historyList = [];
			},

			// 跳转搜索结果页
			goSearchResult(e) {
				this.$u.api.getlistgoods({
					title: e
				}).then(res => {
					this.goodsList = res.rows
				})
				// uni.setStorageSync('TEMP_SEARCH_KEYWORD', e);
				// uni.navigateTo({
				// 	url: '/pages/search-result'
				// });
			}
		}
	};
</script>

<style lang="scss" scoped>
	.page {
		background-color: #fff;
	}

	.list {
		padding: 0 30rpx;
	}

	.selected-service {
		padding: 0 20rpx;
	}

	.service-item {
		background: #fff;
		border-radius: 15rpx;
		padding: 24rpx;
		margin-bottom: 26rpx;
		display: flex;
		align-items: flex-start;
		box-shadow: 0px 2px 2px 2px rgba(191,191,191,0.2);
	}

	.service-image {
		width: 120rpx;
		height: 120rpx;
		margin-right: 20rpx;
	}

	.service-info {
		flex: 1;
	}

	.service-name {
		font-size: 32rpx;
		margin-bottom: 10rpx;
	}

	.service-price {
		color: #ff6600;
		font-size: 28rpx;
		margin: 12rpx 0;
	}
</style>