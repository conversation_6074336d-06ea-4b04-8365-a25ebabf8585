<template>
	<view class="slot">
		<view class="navbar" :style="[{ minHeight: (navBarHeight) + 'px' }]">
			<view class="inner" :style="[
					{ minHeight: menuHeight + 'px' },
					{ lineHeight: menuHeight + 'px' },
					{ paddingLeft: menuRight * 2 + 'px' },
					{ paddingRight: menuRight * 2 + 'px' },
					{ paddingTop: navBarHeight - menuHeight - menuTop + 'px' },
					{ paddingBottom: '20rpx' }
				]">
				<view class="loaction-slot">
					<view class="loaction-title" @click="goCitySelectPage">
						<text>{{ cityListSelected}}</text>
						<u-icon style="margin-left: 8rpx;" :size="18" color="#171717" name="arrow-down-fill"></u-icon>
					</view>
				</view>
				<view class="search-slot"
					:style="{ paddingTop: menuTop + 'px',  }">
					<u-search v-if="!isFind" @click="goSearchPage" disabled :placeholder="placeholder"
						:showAction="false" bg-color="#F4F5F8"></u-search>
					<view v-else>
						<view style="display: flex;justify-content: space-between;align-items: center;">
							<view style="width: 80%;">
								<u-search :placeholder="placeholder" :showAction="false" bg-color="#F4F5F8"></u-search>
							</view>
							<u-icon name="bell"></u-icon>
						</view>
						<view class="tabs">
							<view class="button-item" v-for="(item, index) in scrollData" :key="index"
								:class="{ 'selected': current === index }" @click="selectItem(index)">
								{{item}}
							</view>
						</view>
						<!-- <u-tabs :list="scrollData" :current="current" @change="change" ></u-tabs> -->
					</view>
				</view>
			</view>
		</view>
		<view class="slot-height" :style="[{ height: navBarHeight + menuHeight + menuTop + (isFind?40:11) + 'px' }]">
		</view>
	</view>
</template>

<script>
	const app = getApp();
	export default {
		name: 'navbar-city-search',
		props: {
			// 占位内容
			placeholder: {
				type: String,
				default: '请输入'
			},
			// 已经选择的社区
			cityListSelected: {
				type: String,
				default: ''
			},
			isFind: {
				type: Boolean,
				default: false
			}
		},
		data() {
			return {
				keyword: "",
				current: 0,
				scrollData: [
					"热门话题", "热门活动", "邻居互动"
				],
				// 导航栏高度
				menuTop: app.globalData.menuTop,
				navBarHeight: app.globalData.navBarHeight,
				menuRight: app.globalData.menuRight,
				menuBotton: app.globalData.menuBotton,
				menuHeight: app.globalData.menuHeight,
				statusBarHeight: app.globalData.statusBarHeight,
			};
		},

		mounted() {




		},
		methods: {
			selectItem(index) {
				this.current = index;
			},
			// 跳转城市选择页面
			goCitySelectPage() {
				uni.navigateTo({
					url: '/pages/home/<USER>'
				});
			},

			// 去搜索页面
			goSearchPage() {
				uni.navigateTo({
					url: '/pages/search'
				});
			},

			// 获取地理位置
			async getLocation(isAgain = false) {

			},
		}
	};
</script>

<style lang="scss" scoped>
	.tabs {
		background: #fff;
		display: flex;
		padding: 20rpx;
		justify-content: space-between;
		color: #999;

		.button-item {}

		.selected {
			font-weight: bold;
			color: #000;
		}
	}

	.slot {
		width: 100vw;
	}

	.navbar {
		width: 100%;
		position: fixed;
		top: 0;
		left: 0;
		z-index: 899;
		overflow: hidden;
	}

	.inner {
		width: 100%;
		height: 100%;
		// background-color: #fff;
		background: linear-gradient(to bottom, #ffd9a4, #ffdcac);
	}

	.loaction-slot {
		display: flex;
		align-items: center;
		padding: 0 2%;

		.loaction-title {
			display: flex;
			align-items: center;

			text {
				font-size: 32rpx;
				font-family: PingFang SC;
				font-weight: 400;
				color: $app-theme-text-black-color;
			}
		}
	}

	.search-slot {}
</style>