import Vue from "vue";
import {
	getToken,
	setToken,
	removeToken
} from "@/utils/auth";
import storage from "@/utils/storage";
import constant from "@/utils/constant";
export default {
	namespaced: true,
	state: {
		token: getToken(),
		userInfo: storage.get(constant.userInfo),
		locationCom: storage.get(constant.locationCom) || "暂无绑定",
	},
	mutations: {
		SET_COM(state, data) {
			state.locationCom = data;
			storage.set(constant.locationCom, data)
		},
		COMMIt_SET_TOKEN(state, data) {
			state.token = data;
		},
		COMMIT_USER_INFO(state, data) {
			storage.set(constant.userInfo, data)
			state.userInfo = data;
		},
	},
	actions: {
		// 退出登录
		LogOut({
			commit,
			state
		}) {
			return new Promise((resolve, reject) => {
				Vue.prototype.$u.api
					.logout()
					.then(() => {
						commit("COMMIt_SET_TOKEN", "");
						commit("COMMIT_USER_INFO", {});
						removeToken();
						storage.clean();
						resolve();
					})
					.catch((error) => {
						reject(error);
					});
			});
		},
		Login({
			commit
		}, code) {
			return new Promise((resolve, reject) => {
				Vue.prototype.$u.api
					.wxLogin({
						code: code
					})
					.then((res) => {
						if (res.code !== 200) {
							uni.showToast({
								title: res.msg,
								icon: "none",
							});
							return false;
						}
						setToken(res.token);
						commit("COMMIt_SET_TOKEN", res.token);
						resolve();
					})
					.catch((error) => {
						reject(error);
					});
			});
		},
		PasswordLogin({
			commit
		}, loginForm) {
			return new Promise((resolve, reject) => {
				Vue.prototype.$u.api.loginByPassword(loginForm)
					.then(res => {
						if (res.code !== 200) {
							uni.showToast({
								title: res.msg,
								icon: "none"
							});
							return reject(new Error(res.msg));
						}

						setToken(res.token); // 存储token
						commit("COMMIt_SET_TOKEN", res.token); // 更新state
						resolve();
					})
					.catch(error => reject(error));
			});
		},
		// 获取用户信息
		GetInfo({
			commit
		}) {
			return new Promise((resolve, reject) => {
				Vue.prototype.$u.api
					.getInfo()
					.then((res) => {
						this.dispatch("user/getLoaction");
						commit("COMMIT_USER_INFO", res.user);
						resolve(res);
					})
					.catch((error) => {
						reject(error);
					});
			});
		},
		getLoaction({
			commit,
			state
		}) {
			return new Promise((resolve, reject) => {
				Vue.prototype.$u.api
					.getBindRegion()
					.then((res) => {
						let obj = res.data.find((item) => item.enabled == true);
						commit("SET_COM", obj.name || "暂无绑定");
						resolve(res);
					})
					.catch((error) => {
						reject(error);
					});
			});
		},
	},
};