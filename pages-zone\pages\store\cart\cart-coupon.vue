<template>
	<view class="cart-coupon" v-show="isShow">
		<view class="back" @click="handleShow"></view>
		<view class="title">
			选择优惠券
		</view>
		<view class="coupon">
			<view class="coupon-item" :class="{
					'expired': item.status === 2, 
					'used': item.status === 1
				}" v-for="item in couponList" :key="item.id" @click="selectCoupon(item)">
				<view class="img" :class="{
					'expired-bg': item.status === 2,
					'used-bg': item.status === 1
				}">
					<view class="amount">
						￥{{item.amount}}
					</view>
					<view class="useAmount">
						满{{item.minUseAmount}}元可用
					</view>
					<!-- 状态标签 -->
					<view v-if="item.status === 2" class="expired-tag">已过期</view>
					<view v-if="item.status === 1" class="used-tag">已使用</view>
				</view>
				<view class="detail">
					<view class="scope">
						{{ item.title || '通用优惠券' }}
					</view>
					<view class="time">
						{{item.startTime.split(" ")[0]}} - {{item.endTime.split(" ")[0]}}
					</view>
					<!-- 状态提示 -->
					<view class="status" v-if="item.status === 2">
						已过期
					</view>
					<view class="status used" v-if="item.status === 1">
						已使用 {{ formatUseTime(item.useTime) }}
					</view>
					<view class="status valid" v-if="item.status === 0">
						{{ isCouponNotStarted(item) ? '未生效' : `剩余 ${getDaysLeft(item)} 天` }}
					</view>
				</view>
			</view>
		</view>
		<view @click="handleShow" class="close_">
			<u-icon name="close"></u-icon>
		</view>
	</view>
</template>

<script>
	export default {
		name: "cart-coupon",
		data() {
			return {
				couponList: [],
				now: new Date().getTime() // 当前时间戳
			}
		},
		props: {
			isShow: {
				type: Boolean,
			},
		},
		mounted() {
			this.getCoupon()
		},
		methods: {
			getCoupon() {
				this.$u.api.getCouponList().then(res => {
					this.couponList = res.data;
				})
			},
			// 格式化使用时间
			formatUseTime(useTime) {
				if (!useTime) return ''
				return useTime.split(" ")[0]
			},

			// 判断优惠券是否未生效（仅针对待使用状态）
			isCouponNotStarted(item) {
				if (item.status !== 0) return false
				const startTime = new Date(item.startTime).getTime()
				return startTime > this.now
			},

			// 计算剩余天数（仅针对待使用状态）
			getDaysLeft(item) {
				if (item.status !== 0) return 0
				const endTime = new Date(item.endTime).getTime()
				const days = Math.ceil((endTime - this.now) / (1000 * 60 * 60 * 24))
				return days > 0 ? days : 0
			},

			// 选择优惠券
			selectCoupon(item) {
				if (item.status === 2) {
					uni.showToast({
						title: '该优惠券已过期',
						icon: 'none'
					})
					return
				}
				if (item.status === 1) {
					uni.showToast({
						title: '该优惠券已使用',
						icon: 'none'
					})
					return
				}
				if (this.isCouponNotStarted(item)) {
					uni.showToast({
						title: '该优惠券尚未生效',
						icon: 'none'
					})
					return
				}
				this.$emit("select", item)
			},

			handleShow() {
				this.$emit('isShow')
			}
		}
	}
</script>

<style scoped lang="scss">
	.cart-coupon {
		position: fixed;
		bottom: 0;
		border-radius: 20rpx 20rpx 0 0;
		z-index: 20001;
		width: 100%;
		height: 80vh;
		background: #fff;

		.back {
			position: fixed;
			top: 0%;
			width: 100%;
			background: rgba(0, 0, 0, .5);
			height: calc(100vh - 80vh);
			z-index: 100;
		}

		.title {
			text-align: center;
			font-size: 40rpx;
			font-weight: 700;
			margin-top: 20rpx;
			padding: 20rpx 0;
			border-bottom: 1px solid #f5f5f5;
		}

		.coupon {
			margin-top: 20rpx;
			height: calc(80vh - 120rpx);
			overflow-y: auto;
			padding-bottom: 40rpx;

			.coupon-item {
				box-shadow: #ededed 0 0 10rpx 10rpx;
				width: 90%;
				margin: 20rpx auto;
				display: grid;
				grid-template-columns: 30% 70%;
				height: 160rpx;
				border-radius: 12rpx;
				overflow: hidden;
				position: relative;
				cursor: pointer;

				// 过期样式
				&.expired {
					opacity: 0.7;
				}

				// 已使用样式
				&.used {
					opacity: 0.7;
				}

				.img {
					background: #6096e1;
					display: flex;
					flex-direction: column;
					align-items: center;
					justify-content: center;
					position: relative;

					// 过期背景
					&.expired-bg {
						background: #a0a0a0;
					}

					// 已使用背景
					&.used-bg {
						background: #c9c9c9;
					}

					.amount {
						font-size: 46rpx;
						color: #fff;
						font-weight: 900;
						line-height: 1.2;
					}

					.useAmount {
						color: #fff;
						font-size: 22rpx;
					}

					// 过期标签
					.expired-tag {
						position: absolute;
						top: 10rpx;
						right: 10rpx;
						background: rgba(0, 0, 0, 0.7);
						color: white;
						padding: 4rpx 16rpx;
						border-radius: 16rpx;
						font-size: 22rpx;
						transform: rotate(-15deg);
					}

					// 已使用标签
					.used-tag {
						position: absolute;
						top: 10rpx;
						right: 10rpx;
						background: rgba(103, 194, 58, 0.7);
						color: white;
						padding: 4rpx 16rpx;
						border-radius: 16rpx;
						font-size: 22rpx;
						transform: rotate(-15deg);
					}
				}

				.detail {
					padding: 20rpx;
					background: #fff;
					display: flex;
					flex-direction: column;
					justify-content: space-between;

					.scope {
						font-size: 28rpx;
						font-weight: bold;
						color: #333;
						overflow: hidden;
						text-overflow: ellipsis;
						white-space: nowrap;
					}

					.time {
						font-size: 24rpx;
						color: #666;
					}

					.status {
						font-size: 24rpx;
						text-align: right;

						&.valid {
							color: #67c23a; // 待使用（有效）
						}

						&.used {
							color: #909399; // 已使用
						}

						&:not(.valid):not(.used) {
							color: #f56c6c; // 已过期
						}
					}
				}
			}
		}

		.close_ {
			position: absolute;
			top: 30rpx;
			right: 30rpx;
			font-size: 40rpx;
			color: #909399;
			width: 50rpx;
			height: 50rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			background: #f5f5f5;
			border-radius: 50%;
		}
	}

	// 空状态样式
	.empty-coupon {
		text-align: center;
		padding: 100rpx 0;
		color: #999;
		font-size: 28rpx;

		.u-icon {
			font-size: 120rpx;
			margin-bottom: 20rpx;
			color: #c0c4cc;
		}
	}
</style>