x<template>
	<view class="merchant-list">
		<view class="title" v-if="title">
			{{title}}
		</view>
		<view class="search" v-if="search">
			<view style="display: inline-block;" class="text" :class='{active:active===null}' @click="()=>{active=null;arrow=!arrow}">
				<text>默认</text>
				<!-- <u-icon size='28' :name="arrow ? 'arrow-down-fill' : 'arrow-up-fill'"></u-icon> -->
			</view>
			<text class="text" :class='{active:active===1}' @click='active=1'>销售量</text>
			<text class="text" :class='{active:active===2}' @click="active=2">好评</text>
		</view>
		<view class="shop-list">
			<view class="shop-list-item" v-for="item in shopList" :key='item.shop.id' @click="handleShop(item.shop.id)">
				<view class="img">
					<img style="width: 100%;height: 100%;" :src="baseUrl + item.shop.logoImg" alt="" />
				</view>
				<view class="detail">
					<view class="name">
						{{item.shop.name}}
					</view>
					<view class="star">
						<text class="star">{{item.shop.shopStar}}分 </text> 
						<text class="sellCount">月售{{item.shop.order>1000 ? '1000+' : item.shop.order}}</text>
					</view>
					<view class="song">
						<text>起送￥{{item.shop.qiSong}}</text>
						<text>配送约￥{{item.shop.peiSong}}</text>
					</view>
					<view class="shop-store">
						<view class="shop-store-item" v-for="store in item.storeList" :key='store.id'>
							<img style="width: 180rpx;height: 180rpx;border-radius: 20rpx;" :src="baseUrl + store.img" alt="" />
							<view class="store-name">
								{{store.name}}
							</view>
							<view class="price">
								￥{{store.price}}
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import { baseUrl } from '@/api/config';
	export default {
		name: "merchant-list",
		data() {
			return {
				swiperList:[],
				active:null,
				arrow:true,
				baseUrl,
			}
		},
		props:{
			title:{
				type: String
			},
			search:{
				type: Boolean
			},
			shopList:{
				type: Array,
			}
		},
		watch:{
			active:{
				handler(){
					this.$emit('limit',this.active)
				}
			}
		},
		methods: {
			handleShop(id){
				uni.navigateTo({
					url:'/pages-zone/pages/store/shop/shop?id=' + id,
				})
			},
		}
	}
</script>

<style scoped lang="scss">
.title{
	font-size: 54rpx;
	font-weight: 700;
	margin-top: 20rpx;
}

.search{
	margin-top: 20rpx;
	font-size: 36rpx;
	
	.text{
		margin-right: 40rpx;
	
		&.active{
			color: #f39800;
		}
	}
}

.shop-list{
	.shop-list-item{
		background: #fff;
		margin: 20rpx 0;
		border-radius: 30rpx;
		padding: 28rpx;
		display: grid;
		grid-template-columns: 30% 70%;
		
		.img{
			width: 100%;
			height: 200rpx;	
		}
		
		.detail{
			padding: 12rpx;
			
			.name{
				font-size: 40rpx;
				font-weight: 700;
				margin-bottom: 20rpx;
			}
			
			.star{
				margin-bottom: 20rpx;
				
				.star{
					margin: 20rpx;
					margin-left: 0;
					color: #f4780d;
					font-weight: 700;
				}
			}
			
			.shop-store{
				display: flex;
				width: 100%;
				margin-top: 20rpx;
				overflow: auto;
				
				.shop-store-item{
					display: flex;
					flex-direction: column;
					margin-right: 30rpx;
					
					.store-name{
						font-weight: 700;
						margin-top: 10rpx;
					}
					
					.price{
						color: red;
						margin-top: 10rpx;
					}
				}
			}
		}
	}
}
</style>
