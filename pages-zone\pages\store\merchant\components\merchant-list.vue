<template>
	<view class="merchant-list">
		<view class="title" v-if="title">
			{{title}}
		</view>
		<view class="search" v-if="search">
			<view style="display: inline-block;" class="text" :class='{active:active===null}' @click="()=>{active=null;arrow=!arrow}">
				<text>默认</text>
				<!-- <u-icon size='28' :name="arrow ? 'arrow-down-fill' : 'arrow-up-fill'"></u-icon> -->
			</view>
			<text class="text" :class='{active:active===1}' @click='active=1'>销售量</text>
			<text class="text" :class='{active:active===2}' @click="active=2">好评</text>
		</view>
		<view class="shop-list">
			<view class="shop-list-item" v-for="item in shopList" :key='item.shop.id' @click="handleShop(item.shop.id)">
				<view class="shop-header">
					<view class="shop-logo">
						<img class="logo-img" :src="baseUrl + item.shop.logoImg" alt="商户logo" />
						<view class="quality-badge" v-if="item.shop.shopStar >= 4.5">
							<text class="badge-text">优质商家</text>
						</view>
					</view>

					<view class="shop-info">
						<view class="shop-name">{{item.shop.name}}</view>
						<view class="shop-rating">
							<view class="rating-container">
								<text class="rating-icon">⭐</text>
								<text class="rating-score">{{item.shop.shopStar || 0}}分</text>
							</view>
							<view class="sales-container">
								<text class="sales-icon">🔥</text>
								<text class="sales-text">月售{{item.shop.order > 1000 ? '1000+' : (item.shop.order || 0)}}</text>
							</view>
						</view>

						<view class="delivery-info">
							<view class="delivery-item">
								<text class="delivery-icon">💰</text>
								<text class="delivery-text">起送¥{{item.shop.qiSong || 0}}</text>
							</view>
							<view class="delivery-item">
								<text class="delivery-icon">🚚</text>
								<text class="delivery-text">配送约¥{{item.shop.peiSong || 0}}</text>
							</view>
						</view>
					</view>
				</view>
				<view class="products-section" v-if="item.storeList && item.storeList.length > 0">
					<view class="products-title">
						<text class="title-text">热门商品</text>
						<text class="view-more">查看更多 ></text>
					</view>
					<view class="products-grid">
						<view class="product-item" v-for="store in item.storeList.slice(0, 4)" :key='store.id'>
							<view class="product-image-container">
								<img class="product-image" :src="baseUrl + store.img" alt="商品图片" />
								<view class="product-overlay">
									<text class="add-to-cart">+</text>
								</view>
							</view>
							<view class="product-info">
								<view class="product-name">{{store.name}}</view>
								<view class="product-price">¥{{store.price}}</view>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import { baseUrl } from '@/api/config';
	export default {
		name: "merchant-list",
		data() {
			return {
				swiperList:[],
				active:null,
				arrow:true,
				baseUrl,
			}
		},
		props:{
			title:{
				type: String
			},
			search:{
				type: Boolean
			},
			shopList:{
				type: Array,
			}
		},
		watch:{
			active:{
				handler(){
					this.$emit('limit',this.active)
				}
			}
		},
		methods: {
			handleShop(id){
				uni.navigateTo({
					url:'/pages-zone/pages/store/shop/shop?id=' + id,
				})
			},
		}
	}
</script>

<style scoped lang="scss">
.merchant-list {
	padding: 20rpx;
	background: #F8F9FA;
	min-height: 100vh;
}

.title {
	font-size: 48rpx;
	font-weight: 700;
	color: #2C3E50;
	margin-bottom: 32rpx;
	text-align: center;
	position: relative;

	&::after {
		content: '';
		position: absolute;
		bottom: -12rpx;
		left: 50%;
		transform: translateX(-50%);
		width: 80rpx;
		height: 6rpx;
		background: linear-gradient(90deg, #FF6B6B, #FF8E53);
		border-radius: 3rpx;
	}
}

.search {
	background: #fff;
	border-radius: 24rpx;
	padding: 24rpx 32rpx;
	margin-bottom: 32rpx;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
	border: 1rpx solid #F0F0F0;
	display: flex;
	align-items: center;
	gap: 32rpx;

	.text {
		position: relative;
		padding: 16rpx 24rpx;
		font-size: 30rpx;
		color: #6C757D;
		font-weight: 500;
		border-radius: 20rpx;
		transition: all 0.3s ease;
		cursor: pointer;

		&.active {
			color: #fff;
			background: linear-gradient(135deg, #FF6B6B, #FF8E53);
			box-shadow: 0 4rpx 16rpx rgba(255, 107, 107, 0.3);
			transform: translateY(-2rpx);
		}

		&:hover:not(.active) {
			background: #F8F9FA;
			color: #495057;
		}
	}
}

.shop-list {
	.shop-list-item {
		background: #fff;
		margin: 24rpx 0;
		border-radius: 32rpx;
		padding: 0;
		box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
		border: 1rpx solid #F0F0F0;
		overflow: hidden;
		transition: all 0.3s ease;

		&:hover {
			transform: translateY(-4rpx);
			box-shadow: 0 16rpx 48rpx rgba(0, 0, 0, 0.12);
		}

		.shop-header {
			padding: 32rpx;
			background: linear-gradient(135deg, #f18e6e 0%, #ffd8cb 100%);
			position: relative;
			overflow: hidden;

			&::before {
				content: '';
				position: absolute;
				top: -50%;
				right: -50%;
				width: 200%;
				height: 200%;
				background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
				animation: shimmer 3s ease-in-out infinite;
			}

			.shop-logo {
				position: relative;
				display: flex;
				align-items: center;
				margin-bottom: 24rpx;

				.logo-img {
					width: 120rpx;
					height: 120rpx;
					border-radius: 24rpx;
					border: 4rpx solid rgba(255, 255, 255, 0.3);
					box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.2);
					margin-right: 24rpx;
				}

				.quality-badge {
					background: linear-gradient(135deg, #FF6B6B, #FF8E53);
					padding: 8rpx 16rpx;
					border-radius: 20rpx;
					box-shadow: 0 4rpx 12rpx rgba(255, 107, 107, 0.3);

					.badge-text {
						color: #fff;
						font-size: 24rpx;
						font-weight: 600;
					}
				}
			}

			.shop-info {
				.shop-name {
					font-size: 44rpx;
					font-weight: 700;
					color: #fff;
					margin-bottom: 16rpx;
					text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
				}

				.shop-rating {
					display: flex;
					align-items: center;
					gap: 24rpx;
					margin-bottom: 20rpx;

					.rating-container,
					.sales-container {
						display: flex;
						align-items: center;
						background: rgba(255, 255, 255, 0.2);
						padding: 12rpx 20rpx;
						border-radius: 20rpx;
						backdrop-filter: blur(10rpx);

						.rating-icon,
						.sales-icon {
							font-size: 24rpx;
							margin-right: 8rpx;
						}

						.rating-score,
						.sales-text {
							color: #fff;
							font-size: 28rpx;
							font-weight: 600;
						}
					}
				}

				.delivery-info {
					display: flex;
					gap: 20rpx;

					.delivery-item {
						display: flex;
						align-items: center;
						background: rgba(255, 255, 255, 0.15);
						padding: 12rpx 16rpx;
						border-radius: 16rpx;
						backdrop-filter: blur(10rpx);

						.delivery-icon {
							font-size: 24rpx;
							margin-right: 8rpx;
						}

						.delivery-text {
							color: rgba(255, 255, 255, 0.9);
							font-size: 26rpx;
							font-weight: 500;
						}
					}
				}
			}
		}

		.products-section {
			padding: 32rpx;

			.products-title {
				display: flex;
				justify-content: space-between;
				align-items: center;
				margin-bottom: 24rpx;

				.title-text {
					font-size: 32rpx;
					font-weight: 600;
					color: #2C3E50;
				}

				.view-more {
					font-size: 26rpx;
					color: #FF6B6B;
					font-weight: 500;
				}
			}

			.products-grid {
				display: grid;
				grid-template-columns: repeat(2, 1fr);
				gap: 20rpx;

				.product-item {
					background: #F8F9FA;
					border-radius: 20rpx;
					padding: 16rpx;
					transition: all 0.3s ease;

					&:hover {
						background: #E9ECEF;
						transform: translateY(-2rpx);
					}

					.product-image-container {
						position: relative;
						margin-bottom: 12rpx;

						.product-image {
							width: 100%;
							height: 140rpx;
							border-radius: 16rpx;
							object-fit: cover;
						}

						.product-overlay {
							position: absolute;
							top: 8rpx;
							right: 8rpx;
							width: 48rpx;
							height: 48rpx;
							background: linear-gradient(135deg, #FF6B6B, #FF8E53);
							border-radius: 50%;
							display: flex;
							align-items: center;
							justify-content: center;
							opacity: 0;
							transition: opacity 0.3s ease;

							.add-to-cart {
								color: #fff;
								font-size: 32rpx;
								font-weight: 600;
							}
						}

						&:hover .product-overlay {
							opacity: 1;
						}
					}

					.product-info {
						.product-name {
							font-size: 26rpx;
							color: #2C3E50;
							font-weight: 500;
							margin-bottom: 8rpx;
							overflow: hidden;
							text-overflow: ellipsis;
							white-space: nowrap;
						}

						.product-price {
							font-size: 28rpx;
							color: #FF6B6B;
							font-weight: 700;
						}
					}
				}
			}
		}
	}
}

@keyframes shimmer {
	0% {
		transform: translateX(-100%) translateY(-100%) rotate(45deg);
	}
	50% {
		transform: translateX(100%) translateY(100%) rotate(45deg);
	}
	100% {
		transform: translateX(-100%) translateY(-100%) rotate(45deg);
	}
}
</style>
