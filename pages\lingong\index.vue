<template>
	<view class="page">
		<!-- 安全区占位，覆盖状态栏+胶囊 -->
		<view class="safe-area" :style="{ height: navHeight + 'px' }" />
		<!-- 通知栏 -->
		<view class="notice-bar">
			<u-notice-bar mode="vertical" color='black' :list="list"></u-notice-bar>
		</view>

		<!-- 功能模块区 -->
		<view class="modules">
			<!-- 代取 -->
			<view class="section">
				<view class="section-title">
					<view class="red-bar" />代取
				</view>
				<view class="module-list two-col">
					<view v-for="(mod, i) in moduleData.pickup" :key="i" class="module-item" @click="onModuleClick(mod)"
						:style="{
              backgroundImage: `url(${mod.bgImage})`,
              backgroundSize: 'cover',
              backgroundPosition: 'center'
            }">
						<text class="module-label">{{ mod.name }}</text>
					</view>
				</view>
			</view>

			<!-- 代买 & 代送 -->
			<view class="section two-cols">
				<view class="sub-section">
					<view class="section-title">
						<view class="red-bar" />代买
					</view>
					<view class="module-list one-col">
						<view v-for="(mod, i) in moduleData.purchase" :key="i" class="module-item"
							@click="onModuleClick(mod)" :style="{
                backgroundImage: `url(${mod.bgImage})`,
                backgroundSize: 'cover',
                backgroundPosition: 'center'
              }">
							<text class="module-label">{{ mod.name }}</text>
						</view>
					</view>
				</view>
				<view class="sub-section">
					<view class="section-title">
						<view class="red-bar" />代送
					</view>
					<view class="module-list one-col">
						<view v-for="(mod, i) in moduleData.delivery" :key="i" class="module-item"
							@click="onModuleClick(mod)" :style="{
                backgroundImage: `url(${mod.bgImage})`,
                backgroundSize: 'cover',
                backgroundPosition: 'center'
              }">
							<text class="module-label">{{ mod.name }}</text>
						</view>
					</view>
				</view>
			</view>

			<!-- 代办 -->
			<view class="section">
				<view class="section-title">
					<view class="red-bar" />代办
				</view>
				<view class="module-list two-col">
					<view v-for="(mod, i) in moduleData.service" :key="i" class="module-item"
						@click="onModuleClick(mod)" :style="{
              backgroundImage: `url(${mod.bgImage})`,
              backgroundSize: 'cover',
              backgroundPosition: 'center'
            }">
						<text class="module-label">{{ mod.name }}</text>
					</view>
				</view>
			</view>
		</view>
		<!-- 接单大厅 -->
		<view class="order-hall-title">
			<text style="padding-left: 15px;">接单大厅</text>
		</view>
		<view class="order-hall">
			<view class="order-list" :style="{ height: orderListHeight + 'rpx', overflow: 'hidden' }" ref="wrapper">
				<view class="scroll-inner" :style="{ transform: `translateY(-${offset}rpx)` }">
					<view v-for="(order, idx) in duplicatedOrders" :key="idx" class="order-item">
						<!-- 保留你的 item 结构和样式 -->
						<view class="order-header">
							<text class="order-status">待接单</text>
							<text class="order-price">¥ {{ order.price }}</text>
						</view>
						<view class="order-header-content">
							<text class="order-tag">{{ order.tag }}</text>
							<text class="order-desc">{{ order.desc }}</text>
						</view>
						<button class="order-btn" @click.stop="onOrderClick(order)">
							立即抢单
						</button>
					</view>
				</view>
			</view>
		</view>
	</view>
	</view>
	</view>
	</view>
</template>

<script>
	// import UNoticEBar from '@/pages-zone/uview-ui/components/u-notice-bar/u-notice-bar.vue'
	import {
		HTTP_URL_PROD
	} from '@/api/config';
	export default {
		name: 'index',
		components: {
			// UNoticEBar
		},
		data() {
			return {
				list: [
					'订单：1234512345 已被快递员接单寒雨连江夜入吴',
					'平明送客楚山孤订单：1234512345 已被快递员接单',
					'洛阳亲订单：1234512345 已被快递员接单友如相问',
					'一片冰心订单：1234512345 已被快递员接单在玉壶'
				],
				// 滚动控制
				scrollOrders: [],
				offset: 0, // 当前偏移(px)
				speed: 1.5, // 平滑滚动速度：每帧移动像素
				frameId: null, // 定时器 ID
				scrollTop: 0,
				scrollTimer: null,
				scrollStep: 1,
				scrollInterval: 30,
				itemHeight: 320,
				orderListHeight: 320,
				scrollSpeed: 1, // 每帧滚动像素数
				frameDuration: 16, // 约 60fps => 1000/60 ≈16ms
				singleHeight: 0, // 原始列表总高度
				lastFetchTime: 0,

				// 模块背景图数据
				moduleData: {
					pickup: [{
							name: '取外卖',
							bgImage: require('@/static/img/waimai.png')
						},
						{
							name: '取快递',
							bgImage: require('@/static/img/kuaidi.png')
						}
					],
					purchase: [{
						name: '代买商品',
						bgImage: require('@/static/img/daimai.png')
					}],
					delivery: [{
						name: '代送',
						bgImage: require('@/static/img/daisong.png')
					}],
					service: [{
							name: '宠物喂养',
							bgImage: HTTP_URL_PROD +
								"/profile/upload/2025/05/22/xAVfLj0rSAoqd2fd1d2e0dfffadd9a96b3987eef9e8c_20250522152911A068.png",
						},
						{
							name: '宠物帮遛',
							bgImage: HTTP_URL_PROD +
								"/profile/upload/2025/05/22/ZLJMmwzrdIWo904b5041b1a8c548d254a40f86fa4575_20250522152733A067.png",
						},
						{
							name: '衣服干洗',
							bgImage: HTTP_URL_PROD +
								"/profile/upload/2025/05/22/B0rIFIpHLJoh4505b875c45b11ea46b0903890effd10_20250522152939A069.png",
						},
						{
							name: '家庭快修',
							bgImage: HTTP_URL_PROD +
								"/profile/upload/2025/05/22/Agku1fNPf25fe6ff5b9b6e5f8a7897efbfc03e630cbf_20250522153007A070.png",
						},
						{
							name: "陪护作业",
							bgImage: HTTP_URL_PROD +
								"/profile/upload/2025/05/22/OC5my6rH5pr6e3528b8989db9b23bfddf3b6126f9b8a_20250522153022A071.png",
						},
						{
							name: "到家家教",
							bgImage: HTTP_URL_PROD +
								"/profile/upload/2025/05/22/HhhikvK20XgI843200c51e973df77c25985f370b8214_20250522153039A072.png",
						},
						{
							name: "带娃煮饭",
							bgImage: HTTP_URL_PROD +
								"/profile/upload/2025/05/22/oYfJzYptWM0cff60656a275ceae48c887677fa12303c_20250522153102A073.png",
						},
						{
							name: "其他家事",
							bgImage: HTTP_URL_PROD +
								"/profile/upload/2025/05/22/gO4dEWigpZWXdbfd55cfaa2daa632829e3d9c82854cf_20250522153121A074.png",
						},
					]
				},
				// 接单大厅数据
				orders: [],
				// 状态栏 & 胶囊数据
				statusBarHeight: 0,
				capsuleTop: 0,
				capsuleHeight: 0,
				pageSize: 10,
				pageNum: 1,
			};
		},
		computed: {
			navHeight() {
				return (
					this.statusBarHeight +
					this.capsuleHeight +
					(this.capsuleTop - this.statusBarHeight) +
					8
				);
			},
			// 双倍列表用于无缝
			duplicatedOrders() {
				return this.scrollOrders.concat(this.scrollOrders);
			}
		},
		mounted() {
			// 1. 首次进来拉数据并启动滚动
			this.getDeliveryOrders();
			this.startScroll();
			// 2. 订阅根组件 onShow 事件（需要在 App.vue 里有 uni.$emit('onShow')）
			this.onShowHandler = () => {
				// console.log('Received global onShow → 刷新列表');
				this.getDeliveryOrders();
				// 注意：滚动不重复启动，只更新数据源即可
			};
			uni.$on('onShow', this.onShowHandler);
			// 3. 计算胶囊和列表高度
			const sysInfo = uni.getSystemInfoSync();
			this.rpx2px = sysInfo.windowWidth / 750;
			this.statusBarHeight = sysInfo.statusBarHeight;
			const btn = uni.getMenuButtonBoundingClientRect();
			this.capsuleTop = btn.top;
			this.capsuleHeight = btn.height;
			this.orderListHeight =
				(sysInfo.windowHeight - (this.navHeight + 920)) * (750 / sysInfo.windowWidth);
		},
		beforeDestroy() {
			// 解绑事件，清理定时器
			uni.$off('onShow', this.onShowHandler);
			this.stopScroll();
		},
		methods: {
			async getDeliveryOrders() {
				console.log('调用 getDeliveryOrders');
				try {
					const res = await this.$u.api.getDeliveryOrders({
						pageSize: this.pageSize,
						pageNum: this.pageNum
					});
					console.log('接口返回：', res);
					if (Array.isArray(res.data)) {
						this.scrollOrders = res.data.map(item => ({
							id: item.orderId,
							tag: `#${item.serviceType}#`,
							desc: item.requirementText,
							price: item.totalFee,
							time: item.createdAt
						}));
						this.$nextTick(this.startScroll);
					}
				} catch (e) {
					console.error('请求大厅订单失败:', e);
				}
			},
			startScroll() {
				// 先清理旧定时器
				if (this.frameId) {
					clearTimeout(this.frameId);
					this.frameId = null;
				}
				const singleHeight = this.scrollOrders.length * this.itemHeight;
				const step = () => {
					this.offset += this.speed;
					if (this.offset >= singleHeight) {
						this.offset -= singleHeight;
					}
					this.frameId = setTimeout(step, 16);
				};
				step();
			},
			// 停止滚动
			stopScroll() {
				if (this.frameId) {
					clearTimeout(this.frameId);
					this.frameId = null;
				}
			},
			onModuleClick(mod) {
				const map = {
					'取外卖': '/pages-points/lingong/quwaimai?type=取外卖',
					'取快递': '/pages-points/lingong/quwaimai?type=取快递',
					'代买商品': '/pages-points/lingong/daimaisp?type=代买商品',
					'代送': '/pages-points/lingong/daisongsp?type=代送'
				};
				if (map[mod.name]) {
					uni.navigateTo({
						url: map[mod.name]
					});
				} else {
					uni.navigateTo({
						url: `/pages-points/lingong/daibantoeight?serviceType=${mod.name}`
					});
				}
			},
			// 点击“立即抢单”
			onOrderClick(order) {
				let token = uni.getStorageSync("App-Token")
				if (!token) {
					uni.showModal({
						title: "提示",
						content: "您还未登录，请先登录",
						cancelText: "取消",
						confirmText: "确定",
						success: function(res) {
							if (res.confirm) {
								uni.reLaunch({
									url: "/pages/login/index",
								});
							}
						},
					});
					return false
				}
				uni.showModal({
					title: '确认接单',
					content: `是否要接收该订单？\n金额：¥${order.price}`,
					confirmText: '确定',
					cancelText: '取消',
					success: async (res) => {
						if (res.confirm) {
							// 调用抢单接口
							try {
								const grabRes = await this.$u.api.grabOrder(order.id)
								console.log('grabOrder 返回：', grabRes)
								if (grabRes.code === 200) {
									// 抢单成功后跳转到详情页
									uni.navigateTo({
										url: `/pages-points/lingong/jiedandetail` +
											`?desc=${encodeURIComponent(order.desc)}` +
											`&price=${order.price}` +
											`&tag=${encodeURIComponent(order.tag)}` +
											`&time=${order.time}`
									})
								} else {
									// 后端返回非 200，弹出提示
									uni.showModal({
										title: '提示',
										content: grabRes.msg || '抢单失败，请稍后重试',
										showCancel: false,
										confirmText: '我知道了'
									})
								}
							} catch (err) {
								console.error('grabOrder 接口异常：', err)
								uni.showModal({
									title: '错误',
									content: '服务器异常，抢单失败',
									showCancel: false,
									confirmText: '我知道了'
								})
							}
						}
					}
				})
			},
			acceptOrder(order) {
				uni.navigateTo({
					url: `/pages-points/lingong/jiedandetail` +
						`?desc=${encodeURIComponent(order.desc)}` +
						`&price=${order.price}` +
						`&tag=${encodeURIComponent(order.tag)}` +
						`&time=${order.time}`
				});
			},
			initAutoScroll() {
				// 先清理旧定时器
				this.stopAutoScroll();
				uni.createSelectorQuery().in(this).select('.order-list').boundingClientRect(rect => {
						this.singleHeight = this.scrollOrders.length * this.itemHeight;
						this.startAutoScroll();
					})
					.exec();
			},
			startAutoScroll() {
				const step = () => {
					this.scrollTop += this.scrollSpeed;
					if (this.scrollTop >= this.singleHeight) {
						// 减去单列表高度，实现无缝循环
						this.scrollTop -= this.singleHeight;
					}
					this.scrollTimer = setTimeout(step, this.frameDuration);
				};
				this.scrollTimer = setTimeout(step, this.frameDuration);
			},
			stopAutoScroll() {
				clearInterval(this.scrollTimer);
				this.scrollTimer = null;
			},
		},
	};
</script>

<style lang="scss" scoped>
	.page {
		position: relative;
		padding-top: 180rpx;
		padding-bottom: 20rpx;
		background: linear-gradient(to bottom,
				#ffe5c0 0%,
				#e0f7ff 40%,
				#ffffff 100%);

	}

	.safe-area {
		width: 100%;
		background-color: #ffe5c0;
		position: fixed;
		top: 0;
		left: 0;
		z-index: 1;
	}

	.notice-bar {
		margin: 10px auto;
		width: 95%;
		height: 70rpx;
		background-color: #fff;
		border-radius: 40px;
	}

	.notice-scroll {
		width: 100%;
		height: 40rpx;
	}

	.notice-item {
		text-align: center;
		height: 40rpx;
		line-height: 40rpx;
		font-size: 26rpx;
		color: #333;
		background-color: #e1ffe5;
	}

	.modules {
		padding: 20rpx;
		background: transparent;
	}

	.section {
		margin-bottom: 30rpx;
	}

	.section-title {
		display: flex;
		align-items: center;
		font-size: 32rpx;
		color: #333;
		margin-bottom: 20rpx;
		font-weight: 600;
	}

	.red-bar {
		width: 8rpx;
		height: 28rpx;
		background-color: #e64340;
		border-radius: 3rpx;
		margin-right: 10rpx;
	}

	.two-cols {
		display: flex;
		justify-content: space-between;
	}

	.sub-section {
		width: 48%;
	}

	.module-list {
		display: grid;
		grid-gap: 50rpx;
	}

	.module-list.two-col {
		grid-template-columns: repeat(2, 1fr);
	}

	.module-list.one-col {
		grid-template-columns: 1fr;
	}

	.module-item {
		height: 150rpx;
		background-color: #fff;
		border-radius: 12rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		/* 根据需要可以在这里加不同的背景渐变或色块 */
	}

	.module-label {
		color: black;
		font-size: 40rpx;
		font-weight: 666;
		// text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.5);
		// z-index: 999;
		padding-left: 55px;
	}

	.module-icon {
		width: 80rpx;
		height: 80rpx;
		margin-bottom: 10rpx;
	}

	// .module-label {
	//   font-size: 24rpx;
	//   color: #333;
	// }

	.order-hall {
		margin-top: 20rpx;
		background: linear-gradient(to bottom, #e0f7ff 0%, #eff9ff 40% #ffffff 100%);
		padding: 20rpx;
		border-radius: 12rpx;
		// border: 1px solid #000
	}

	.order-hall-title {
		font-size: 32rpx;
		color: #333;
		font-weight: 700;
		margin-bottom: 20rpx;
		background-color: #ffffff;
		height: 50px;
		line-height: 50px;
	}

	.order-list {
		height: 500px;
		width: 100%;
		overflow: hidden;
	}

	.order-item {
		background: #fff;
		border-radius: 12rpx;
		margin-bottom: 20rpx;
		padding: 20rpx;
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
	}

	.order-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 10rpx;
	}

	.order-status {
		font-size: 28rpx;
		color: #333;
		font-weight: 600;
	}

	.order-price {
		font-size: 28rpx;
		color: #e64340;
		font-weight: 600;
	}

	.order-tag {
		display: block;
		font-size: 26rpx;
		color: #090909;
		font-weight: 600;
		margin-bottom: 6rpx;
	}

	.order-desc {
		font-size: 26rpx;
		color: #090909;
		font-weight: 500;
		margin-bottom: 12rpx;
	}

	.order-btn {
		margin: 15px auto;
		width: 100%;
		height: 80rpx;
		line-height: 80rpx;
		text-align: center;
		background-color: #e64340;
		color: #fff;
		border-radius: 80rpx;
		font-size: 40rpx;
		font-weight: 800;
	}

	.order-header-content {
		background-color: #eff3f5;
		border-radius: 20rpx;
		padding: 10px;
	}

	::v-deep .u-notice-bar {
		border-radius: 40px;
	}

	// .order-hall-title {
	//   font-size: 32rpx;
	//   color: #333;
	//   font-weight: 700;
	//   margin-bottom: 20rpx;
	// }

	// .order-list {
	//   width: 100%;
	//   /* 高度用 inline style 传入 */
	// }

	.scroll-inner {
		display: flex;
		flex-direction: column;
	}

	// .order-item {
	//   background: #fff;
	//   border-radius: 12rpx;
	//   margin-bottom: 20rpx;
	//   padding: 20rpx;
	//   box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
	//   height: 100rpx; /* 与 itemHeightRpx 一致 */
	// }
	// .order-header {
	//   display: flex;
	//   justify-content: space-between;
	//   align-items: center;
	//   margin-bottom: 10rpx;
	// }
	// .order-status {
	//   font-size: 28rpx;
	//   color: #333;
	//   font-weight: 600;
	// }
	// .order-price {
	//   font-size: 28rpx;
	//   color: #e64340;
	//   font-weight: 600;
	// }
	// .order-tag {
	//   font-size: 26rpx;
	//   color: #090909;
	//   font-weight: 600;
	//   margin-bottom: 6rpx;
	// }
	// .order-desc {
	//   font-size: 26rpx;
	//   color: #090909;
	//   font-weight: 500;
	//   margin-bottom: 12rpx;
	// }
	// .order-btn {
	//   width: 100%;
	//   height: 80rpx;
	//   line-height: 80rpx;
	//   text-align: center;
	//   background-color: #e64340;
	//   color: #fff;
	//   border-radius: 80rpx;
	//   font-size: 40rpx;
	//   font-weight: 800;
	// }
</style>