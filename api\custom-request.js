/** 
 * 自定义封装http请求
 * 在此处可以自定义其他的请求方法
 * 直接在main.js中引入挂载即可在页面中使用
 * 也可以引入到/modules中，使用$u进行api的挂载
 * 建议基于uniapp的request进行封装，请勿使用其他三方非uniapp请求
 */

export const VUE_APP_API_URL = ''

export const httpRequest = (opts, data) => {
	let httpDefaultOpts = {
		url: baseUrl + opts.url,
		data: data,
		method: opts.method,
		header: opts.method == 'GET' ? {
			'X-Requested-With': 'XMLHttpRequest',
			"Accept": "application/json",
			"Content-Type": "application/json; charset=UTF-8"
		} : {
			'X-Requested-With': 'XMLHttpRequest',
			'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8'
		},
		dataType: 'json',
	}
	let promise = new Promise(function(resolve, reject) {
		uni.request(httpDefaultOpts).then(
			(res) => {
				resolve(res[1])
			}
		).catch(
			(response) => {
				reject(response)
			}
		)
	})
	return promise
};

export const httpRequestUploadFile = (opts, filePath, data) => {
	let token = "";
	uni.getStorage({
		key: 'App-Token',
		success: function(ress) {
			token = ress.data
		}
	});
	// token="dd0847da213958adf77e88a2cfb661e5"
	let httpDefaultOpts = {
		url: baseUrl + opts.url,
		header: {
			token: token
		},
		name: "file",
		filePath: filePath,
		name: 'file',
		formData: data,
	}
	let promise = new Promise(function(resolve, reject) {
		uni.uploadFile(httpDefaultOpts).then(
			(res) => {
				resolve(res[1])
			}
		).catch(
			(response) => {
				reject(response)
			}
		)
	})
	return promise
};

//带Token请求
export const httpTokenRequest = (opts, data) => {
	let token = "";
	uni.getStorage({
		key: 'App-Token',
		success: function(ress) {
			token = ress.data
		}
	});
	// 测试用
	// token = "dd0847da213958adf77e88a2cfb661e5"
	//此token是登录成功后后台返回保存在storage中的
	let httpDefaultOpts = {
		url: baseUrl + opts.url,
		data: data,
		method: opts.method,
		header: opts.method == 'GET' ? {
			'Token': token,
			'X-Requested-With': 'XMLHttpRequest',
			"Accept": "application/json",
			"Content-Type": "application/json; charset=UTF-8"
		} : {
			'': token,
			'X-Requested-With': 'XMLHttpRequest',
			'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8'
		},
		dataType: 'json',
	}
	let promise = new Promise(function(resolve, reject) {
		uni.request(httpDefaultOpts).then(
			(res) => {
				resolve(res[1])
			}
		).catch(
			(response) => {
				reject(response)
			}
		)
	})
	return promise
};

//自定义请求
export const httpCustomeRequest = (opts, data) => {
	let httpDefaultOpts = {
		url: opts.url,
		data: data,
		method: opts.method,
		header: opts.method == 'GET' ? {
			'X-Requested-With': 'XMLHttpRequest',
			"Accept": "application/json",
			"Content-Type": "application/json; charset=UTF-8"
		} : {
			'X-Requested-With': 'XMLHttpRequest',
			'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8'
		},
		dataType: 'json',
	}
	let promise = new Promise(function(resolve, reject) {
		uni.request(httpDefaultOpts).then(
			(res) => {
				resolve(res[1])
			}
		).catch(
			(response) => {
				reject(response)
			}
		)
	})
	return promise
};
