<template>
	<view>
		<Navbar title="师傅入驻"></Navbar>
		<view class="container">
			<view class="form-wrapper">
				<u-form :model="form" ref="uForm" label-width="190">
					<!-- 真实姓名 -->
					<u-form-item label="真实姓名" prop="name" :required="true">
						<u-input v-model="form.name" placeholder="请输入真实姓名" />
					</u-form-item>

					<!-- 身份证号 -->
					<u-form-item label="身份证号" prop="idCard" :required="true">
						<u-input v-model="form.idCard" placeholder="请输入身份证号码" />
					</u-form-item>

					<!-- 简历头像 -->
					<u-form-item label="简历头像" prop="avatarImages" :required="true">
						<view class="example-body">
							<u-upload ref="uUpload" :header="header" :action="action" :file-list="avatarImages"
								:max-count="1" @on-change="upChange1" upload-text="点击上传" />
						</view>
					</u-form-item>

					<!-- 接单社区 -->
					<u-form-item label="接单社区" prop="community" :required="true">
						<!-- <u-input v-model="form.community" placeholder="请输入接单社区" /> -->
						<uni-data-picker placeholder="请选择社区" popup-title="请选择所在地区" :localdata="dataTree"
							v-model="form.community" :border="false" :map="dataMap" @nodeclick="onnodeclick"
							@popupopened="onpopupopened">
						</uni-data-picker>
					</u-form-item>

					<!-- 意向行业 -->
					<u-form-item label="意向行业" prop="intendedIndustry" :required="true">
						<u-input type="textarea" height="50" :disabled="true" v-model="intendedIndustry"
							placeholder="请选择意向行业" @click="openPopupSelect()" />
						<u-icon slot="right" name="arrow-right" @click="openPopupSelect()"></u-icon>
					</u-form-item>


					<!-- 工作经验 -->
					<u-form-item label="工作经验" prop="workExperience" :required="true">
						<u-input v-model="form.workExperience" placeholder="请输入工作经验" />
					</u-form-item>

					<!-- 工作性质 -->
					<u-form-item label="工作性质" prop="workNature" :required="true">
						<u-input v-model="form.workNature" :disabled="true" placeholder="请选择工作性质"
							@click="evselect(0)" />
						<u-icon slot="right" name="arrow-right" @click="evselect(0)"></u-icon>
					</u-form-item>

					<!-- 学历 -->
					<u-form-item label="学历">
						<u-input v-model="form.qualification" :disabled="true" placeholder="请选择学历"
							@click="evselect(1)" />
						<u-icon slot="right" name="arrow-right" @click="evselect(1)"></u-icon>
					</u-form-item>

					<!-- 工作经历 -->
					<u-form-item label="工作经历">
						<u-input type="textarea" maxlength="140" height="180" v-model="form.workManager"
							placeholder="请简要描述您的工作经历" />
					</u-form-item>

					<!-- 个人简介 -->
					<u-form-item label="个人简介">
						<u-input type="textarea" maxlength="140" height="180" v-model="form.curriculumVitae"
							placeholder="请简要描述您的个人情况" />
					</u-form-item>

					<!-- 身份证人像面 -->
					<u-form-item label="身份证人像面" prop="idCardFrontImages" :required="true">
						<view class="example-body">
							<u-upload :header="header" :action="action" :file-list="idCardFrontImages" :max-count="1"
								@on-change="upChangeFront" />
						</view>
					</u-form-item>

					<!-- 身份证国徽面 -->
					<u-form-item label="身份证国徽面" prop="idCardBackImages" :required="true">
						<view class="example-body">
							<u-upload :header="header" :action="action" :file-list="idCardBackImages" :max-count="1"
								@on-change="upChangeBack" />
						</view>
					</u-form-item>

					<!-- 工作生活照 -->
					<u-form-item label="工作生活照">
						<view class="example-body">
							<u-upload ref="uUpload" :header="header" :action="action" :file-list="lifeImages"
								:max-count="6" @on-change="upChange3" upload-text="点击上传" />
						</view>
					</u-form-item>

					<!-- 专业证书 -->
					<u-form-item label="专业证书">
						<view class="example-body">
							<u-upload ref="uUpload" :header="header" :action="action" :file-list="certImages"
								:max-count="6" @on-change="upChange4" upload-text="点击上传" />
						</view>
					</u-form-item>
				</u-form>

				<!-- <u-button type="primary" @click="submit">提交申请</u-button> -->
				<view class="btn">
					<u-button class='button' type="warning" shape="circle" @click="submit"><text>提交资料</text></u-button>
				</view>
				<view class="agreement">
					<u-checkbox size='30' v-model='agreement' activeColor='#ffba4d' inactiveColor='#ffba4d'>
						<text @click="agreement = !agreement">已阅读并同意</text> 
					</u-checkbox>
					<text style="color: #ffba4d;" @click="agreementc">《虞邻师傅入住协议》</text>
				</view>
				<u-select v-model="show" :list="list" @confirm="confirm"></u-select>
			</view>
		</view>

		<hy-popup-select v-model="selectArr" :list="takeTheLeadList" ref="popupRef1" @itemClick="itemClick1"
			@save="done" multiple></hy-popup-select>
	</view>
</template>

<script>
	import {
		HTTP_URL_PROD
	} from '@/api/config';
	const token = uni.getStorageSync("App-Token");
	export default {
		data() {
			return {
				list: [],
				agreement: false,
				dataMap: {
					text: 'fullName',
					value: 'code'
				},
				dataTree: [],
				show: false,
				action: HTTP_URL_PROD + '/common/upload',
				header: {
					Authorization: token
				},
				takeTheLeadList: [], //意向行业
				intendedIndustry: "",
				form: {
					name: '', // 真实姓名
					idCard: '', // 身份证号
					community: '', // 接单社区
					intendedIndustry: '', // 意向行业
					workNature: '', // 工作性质 
					workExperience: '', // 工作经验
					qualification: '', // 学历
					workManager: '', // 工作经历
					curriculumVitae: '', // 个人简介
					picture: '', //头像
					idPictureFront: '', //身份证人像面
					idPictureBack: '', //身份证国徽面
					workPicture: '', //工作照
					professionalCertificate: '', //专业证书
					userId: this.$store.state.user.userInfo.userId,
					userName: this.$store.state.user.userInfo.userName
				},
				selectArr: [], //意向行业
				avatarImages: [], // 简历头像
				idCardFrontImages: [], // 身份证人像面
				idCardBackImages: [], // 身份证国徽面
				lifeImages: [], // 工作生活照
				certImages: [], // 专业证书
				rules: {
					name: [{
						required: true,
						message: '请输入真实姓名',
						trigger: ['blur', 'change']
					}],
					idCard: [{
							required: true,
							message: '请输入身份证号',
							trigger: ['blur', 'change']
						},
						{
							validator: (rule, value, callback) => {
								return this.$u.test.idCard(value);
							},
							message: '身份证格式不正确',
							trigger: ['blur', 'change']
						}
					],
					community: [{
						required: true,
						message: '请填写接单社区',
						trigger: ['blur', 'change']
					}],
					intendedIndustry: [{
						required: true,
						message: '请填写意向行业',
						trigger: ['blur', 'change']
					}],
					workNature: [{
						required: true,
						message: '请填写工作性质',
						trigger: ['blur', 'change']
					}],
					workExperience: [{
						required: true,
						message: '请填写工作经验',
						trigger: ['blur', 'change']
					}],
					// avatarImages: [{
					// 	validator: (rule, value, callback) => {
					// 		return value.length > 0;
					// 	},
					// 	message: '请上传简历头像',
					// 	trigger: ['change']
					// }],
					// idCardFrontImages: [{
					// 	validator: (rule, value) => value.length > 0,
					// 	message: '请上传身份证人像面',
					// 	trigger: ['change']
					// }],
					// idCardBackImages: [{
					// 	validator: (rule, value) => value.length > 0,
					// 	message: '请上传身份证国徽面',
					// 	trigger: ['change']
					// }]
				},
				selectid: null
			}
		},
		onReady() {
			this.$refs.uForm.setRules(this.rules);

		},
		onLoad() {
			console.log(this.$store.state.user.userInfo.userId, 'this.$store.statethis.$store.statethis.$store.state')
		},
		methods: {
			agreementc() {
				uni.navigateTo({
					url: `/pages-zone/pages/Techniciancheckin/agreement`
				});
			},
			// 选择社区展开
			onpopupopened(e) {
				this.$u.api.getRegionList('')
					.then(res => {
						this.dataTree = res.data
					})
			},
			// 选择社区确认
			onnodeclick(e) {
				// this.$refs.uForm.validateField('community') // 手动触发验证
				this.form.community = e.code
			},
			openPopupSelect() {
				this.funIntendedIndustry()
			},
			funIntendedIndustry() {
				this.$u.api.getIntendedIndustry()
					.then(res => {
						this.takeTheLeadList = res.data.list
						this.$refs.popupRef1.open()
					})
			},
			evselect(val) {
				this.selectid = val
				if (val == 0) { //工作性质
					this.list = [{
							value: '1',
							label: '兼职'
						},
						{
							value: '2',
							label: '全职'
						},
					]
				} else { //学历
					this.list = [{
							value: '1',
							label: '小学'
						},
						{
							value: '2',
							label: '初中'
						},
						{
							value: '3',
							label: '中专'
						},
						{
							value: '4',
							label: '高中'
						},
						{
							value: '5',
							label: '大专'
						},
						{
							value: '6',
							label: '本科'
						},
						{
							value: '7',
							label: '研究生'
						},
						{
							value: '8',
							label: '博士生'
						},
					]
				}
				this.show = true
			},
			confirm(val) {
				if (this.selectid == 0) {
					this.form.workNature = val[0].label
				} else {
					this.form.qualification = val[0].label
				}
				console.log(val, 'val')
			},
			submit() {
				// 1. 同意协议校验
				if (!this.agreement) {
					uni.$u.toast('请先同意协议');
					return;
				}

				this.$refs.uForm.validate(valid => {
					if (valid) {
						this.handleSubmit();
					} else {
						uni.showToast({
							title: '请完善必填信息',
							icon: 'none'
						});
					}
				});
			},
			handleSubmit() {
				let formData = this.form
				if (!formData.picture) {
					uni.showToast({
						title: '请上传头像',
						icon: 'none'
					});
					return false
				} else if (!formData.idPictureFront) {
					uni.showToast({
						title: '身份证人像面',
						icon: 'none'
					});
					return false
				} else if (!formData.idPictureBack) {
					uni.showToast({
						title: '身份证国徽面',
						icon: 'none'
					});
					return false
				}
				console.log('提交数据:', this.form);
				uni.showLoading({
					title: '提交中...'
				});
				this.$u.api.postMerchantcheckin(formData)
					.then(res => {
						console.log(res, 'res')
						if (res.code == 200) {
							uni.showToast({
								title: '提交成功'
							});
							setTimeout(() => {
								uni.navigateBack();
							}, 1500);
						} else {
							uni.showToast({
								title: res.msg,
								icon: 'none'
							});
						}
					})
					.catch(err => {
						uni.showToast({
							title: err.message || '提交失败',
							icon: 'none'
						});
					})
			},

			// 意向行业 选中事件
			itemClick1(item) {},
			// 意向行业事件 多选确认事件
			done(arr) {
				this.intendedIndustry = arr.name.join(',')
				this.form.intendedIndustry = arr.id.join(',')
			},
			// 简历头像
			upChange1(res, index, lists, name) {
				this.avatarImages = lists
				this.form.picture = lists[0].response.fileName
			},
			// 人像面上传
			upChangeFront(res, index, lists) {
				this.idCardFrontImages = lists
				this.form.idPictureFront = lists[0].response.fileName
			},

			// 国徽面上传
			upChangeBack(res, index, lists) {
				this.idCardBackImages = lists
				this.form.idPictureBack = lists[0].response.fileName
			},
			// 工作生活照
			upChange3(res, index, lists, name) {
				const result = lists
					.map(item => item.response?.fileName)
					.filter(fileName => fileName != null)
					.join(', ');
				this.form.workPicture = result
			},
			// 专业证书
			upChange4(res, index, lists, name) {
				const result = lists
					.map(item => item.response?.fileName)
					.filter(fileName => fileName != null)
					.join(', ');
				this.form.professionalCertificate = result
			},

		}
	}
</script>

<style>
	.input-value {
		width: 410rpx !important;
	}
</style>
<style scoped lang="scss">
	.btn {
		padding: 60rpx 0rpx;

		.button {
			background: #ffba4d;

			text {
				font-weight: 800;
				color: #000;
				font-size: 40rpx
			}

			&:hover {
				background: #ffba4d;
			}
		}
	}

	.agreement {
		text-align: center;

		& ::v-deep .u-checkbox__icon-wrap {
			border: 1px solid #ffba4d;
		}
	}

	.container {
		padding: 30rpx;
		background: #f8f8f8;
		min-height: 100vh;
	}

	.form-wrapper {
		background: #fff;
		padding: 30rpx;
		border-radius: 16rpx;
		box-shadow: 0 4rpx 24rpx rgba(0, 0, 0, 0.06);
	}

	.example-body {
		margin-top: 16rpx;
	}

	.u-button {
		margin-top: 48rpx;
		height: 88rpx;
		font-size: 32rpx;
	}
</style>