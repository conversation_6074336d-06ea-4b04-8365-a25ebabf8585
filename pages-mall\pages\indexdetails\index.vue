<template>
	<view class="page">
		<Navbar :title="title"></Navbar>
		<view class="top-buttons">
			<view v-for="(item, index) in topList" :key="index" class="button-item"
				:class="{ 'selected': selectedIndex === index }" @click="selectItem(index,item.id)">
				<image :src="https+item.imgUrl" class="button-icon"></image>
				<text class="button-text">{{item.name}}</text>
			</view>
			<view class="pilotLamp">
			</view>
		</view>
		<view style="padding: 32rpx;font-weight: bold;font-size: 36rpx;">精选服务</view>
		<view class="selected-service">
			<view v-for="(item, index) in list2" :key="index" class="service-item" @click="chandetail(item)">
				<image :src="https+item.cover" class="service-image"></image>
				<view class="service-info">
					<text class="service-name">{{item.title}}</text>
					<view class="service-price">{{item.amount}}元/{{item.unit||''}}</view>
					<view class="service-meta">
						<text>已售{{item.sold}} 好评率{{item.applauseRate==0?'100%':item.applauseRate+'%'}}</text>
					</view>
				</view>
			</view>
			<view class="empty-container" v-if="list2.length === 0">
				<u-empty mode="list" icon="http://cdn.uviewui.com/uview/empty/car.png"></u-empty>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		HTTP_URL_PROD
	} from '@/api/config';
	export default {
		data() {
			return {
				title: null,
				https: HTTP_URL_PROD,
				topList: [],
				list2: [],
				selectedIndex: 0, // 初始化为 -1 表示没有选中项
				id: null
			};
		},
		onLoad(option) {
			console.log(option, '接受的值');
			this.title = option.name;
			this.getgtabList(option.id)
		},
		methods: {
			selectItem(index, id) {
				this.selectedIndex = index;
				this.id = id
				this.getlist(id)
			},
			chandetail(item) {
				console.log(item, '商品的参数')
				uni.navigateTo({
					url: './detail?item=' + encodeURIComponent(JSON.stringify(item))
				})
			},
			getgtabList(id) {
				let data = {
					pageNum: 1,
					pageSize: 10,
					parentId: id
				}
				this.$u.api.getJGQlistData(data).then(res => {
					this.topList = res.data.list
					this.oid = res.data.list[0].id
					this.id = res.data.list[0].id
					this.getlist(res.data.list[0].id)
				})
			},
			getlist(id) {
				let data = {
					type: id
				}
				this.$u.api.getlistgoods(data).then(res => {
					this.list2 = res.rows
				})
			}
		}
	};
</script>

<style scoped>
	.empty-container {
		display: flex;
		justify-content: center;
		align-items: center;
		height: 70vh;
		/* 设置足够的高度确保垂直居中 */
		padding: 40rpx 0;
	}

	/* 调整整体页面布局 */
	.page {
		background-color: #f5f5f5;
		display: flex;
		flex-direction: column;
		height: 100vh;
		/* 确保页面占满整个视口高度 */
	}

	.selected-service {
		flex: 1;
		/* 占据剩余空间 */
		display: flex;
		flex-direction: column;
		padding: 0 20rpx;
	}

	.pilotLamp {
		background: #ffa500;
		height: 6rpx;
		width: 68rpx;
		position: fixed;
		left: 46%;
		border-radius: 32rpx;
		margin-top: 16rpx;
	}

	.top-buttons {
		white-space: nowrap;
		/* 防止子元素换行 */
		overflow-x: auto;
		/* 开启水平滚动条 */
		-webkit-overflow-scrolling: touch;
		/* 优化在 iOS 设备上的滚动体验 */
		padding: 20rpx;
		border-bottom: 1px solid #e2e2e2;
	}

	.button-item {
		display: inline-flex;
		/* 使用内联弹性布局，使元素水平排列 */
		flex-direction: column;
		align-items: center;
		margin: 0 10rpx;
		padding: 16rpx;
		background-color: #fff;
		border-radius: 10rpx;
		transition: background-color 0.3s;

		/* 添加过渡效果 */
	}

	.button-item.selected {
		box-shadow: 1px 2px 2px 2px rgb(0 0 0 / 20%)
	}

	.button-item.selected .button-text {
		color: #c1771a;
		/* 选中时文字颜色 */
	}

	.button-icon {
		width: 50rpx;
		height: 50rpx;
	}

	.button-text {
		font-size: 24rpx;
		margin-top: 10rpx;
		color: #333;
	}

	.service-item {
		background: #fff;
		border-radius: 15rpx;
		padding: 24rpx;
		margin-bottom: 26rpx;
		display: flex;
		align-items: flex-start;
		box-shadow: 0px 2px 2px 2px rgb(191 191 191 / 20%);
	}

	.service-image {
		width: 120rpx;
		height: 120rpx;
		margin-right: 20rpx;
	}

	.service-info {
		flex: 1;
	}

	.service-name {
		font-size: 32rpx;
		margin-bottom: 10rpx;
	}

	.service-price {
		color: #ff6600;
		font-size: 28rpx;
		margin: 12rpx 0;
	}

	.service-meta {
		font-size: 24rpx;
		color: #999;
	}
</style>