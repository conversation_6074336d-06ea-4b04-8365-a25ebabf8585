<template>
	<view id="comment">
		<navbar title="评价"></navbar>
		<view class="block" @click="upload">
			<view class="upload" v-if="imgs.length==0" >
				<u-icon size="50" name="camera"></u-icon>
				<view class="text">
					添加图片
				</view>
			</view>
			<view class="imgs">
				<view class="img-item" v-for="(item, index) in imgs" :key="index">
					<img :src="item" alt="" />
				</view>
			</view>
		</view>
		<view class="block">
			<u-input v-model="form.comment" :clearable='false' :custom-style="{padding: '20rpx'}" :height='200' type="textarea" placeholder='跟大家分享一下'/>
		</view>
		<view class="block">
			<view class="store">
				<view class="title">
					商家评价
				</view>
				<view class="star">
					<u-rate size="40" allowHalf active-color="#f39800" inactive-color="#f39800" :count="5" v-model="form.shopStar"></u-rate>
				</view>
				<view class="comment">
					{{formatStar(form.shopStar)}}
				</view>
			</view>
			<view class="peiSong" v-if="deliveryFlag==0">
				<view class="title">
					配送评价
				</view>
				<view class="star">
					<u-rate size="40" allowHalf active-color="#f39800" inactive-color="#f39800" :count="5" v-model="form.deliveryStar"></u-rate>
				</view>
				<view class="comment">
					{{formatStar(form.deliveryStar)}}
				</view>
			</view>
		</view>
		<view class="block bottom">
			<view class="btn" @click="submit">
				发布
			</view>
		</view>
	</view>
</template>

<script>
	import ImgPicker from '@/components/img-picker.vue';
	import { baseUrl } from '@/api/config';
	export default {
		data() {
			return {
				form: {
					shopStar:0,
					deliveryStar:0,
					img:'',
					comment:""
				},
				imgs:[],
				baseUrl,
				deliveryFlag:0,
			}
		},
		components:{
			ImgPicker
		},
		onLoad({storeShopId,deliveryFlag}) {
			this.form.storeShopId = storeShopId
			this.deliveryFlag = deliveryFlag
		},
		methods: {
			formatStar(star){
				if(star==1){
					return '非常差'
				}else if (star==2){
					return '差'
				}else if (star==3){
					return '一般'
				}else if (star==4){
					return '好'
				}else if (star==5){
					return '非常好'
				}else{
					return ''
				}
			},
			submit(){
				if(this.form.shopStar == 0 || this.form.deliveryStar == 0){
					uni.$u.toast('请对商铺进行打分')
					return
				}
				if(this.form.comment == ""){
					uni.$u.toast('请填写评价内容')
					return
				}
				// uni.login({
				// 	provider: 'weixin',
				// 	success: (loginRes) => {
						this.$u.api.text(this.form.comment).then(res=>{
							if(res.code == 200){
								this.form.img = this.form.img.slice(0, this.form.img.length - 1)
								this.$u.api.submitComment(this.form).then(()=>{
									uni.$u.toast('评价成功')
									uni.navigateBack(-1)
								})
							}else{
								uni.$u.toast(res.msg)
							}
						})
				// 	}
				// })
			},
			upload(){
				let that =this
				uni.chooseImage({
					count: 5,
					sizeType: ['original', 'compressed'],
					sourceType: ['album', 'camera'],
					success: res => {
						uni.showLoading({
							title: '图片上传中',
						});
						let localPath = res.tempFilePaths[0];
						uni.uploadFile({
							url:baseUrl + '/store/shopComment/imageContentSecurityRecognition',
							filePath: localPath,
								name: 'file',
							header: {
								"Content-Type": "multipart/form-data",
								"Authorization": uni.getStorageSync("App-Token"),
							},
							success: function(res) {
								if(res){
									uni.uploadFile({
										url: baseUrl + '/common/upload',
										filePath: localPath,
										name: 'file',
										header: {
											"Content-Type": "multipart/form-data",
											"Authorization": uni.getStorageSync("App-Token"),
										},
										success: function(res) {
											uni.hideLoading();
											that.imgs.push(baseUrl + JSON.parse(res.data).fileName)
											that.form.img += JSON.parse(res.data).fileName + ','
										},
										fail: function(res) {
											console.log(res, 'err')
											uni.hideLoading();
											uni.showToast({
												title: '上传图片失败'
											})
										}
									})
								}else{
									uni.showToast({
										title: '图片存在违规'
									})
								}
							},
						})
					}
				})
			},
		}
	}
</script>

<style scoped lang="scss">
#comment{
	height: 100vh;
	background: #f8f8f8;
	
	.block{
		background: #fff;
		padding: 30rpx;
		margin: 20rpx;	
		border-radius: 20rpx;
		
		.store, .peiSong{
			display: flex;
			
			.title{
				font-size: 32rpx;
				font-weight: 800;
			}
			
			.star{
				margin: 0 20rpx;
			}
		}
		
		.upload{
			height: 200rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			flex-direction: column;
			font-weight: 700;
		}
		
		.imgs{
			display: grid;
			grid-template-columns: 1fr 1fr;
			.img-item{
				img{
					width: 100%;
					height: 200rpx;
				}
			}
		}
	
		
		&.bottom{
			position: fixed;
			width: 100%;
			height: 200rpx;
			bottom: 0;
			display: flex;
			flex-direction: row-reverse;
			align-items: center;
			
			.btn{
				background: #ff5c02;
				border-radius: 40rpx;
				width: 200rpx;
				height: 80rpx;
				color: #fff;
				line-height: 80rpx;
				text-align: center;
				font-size: 36rpx;
			}
		}
	}
}

</style>
