<template>
	<view class="page">
		<!-- 顶部导航 -->
		<view class="navbar">
			<Navbar :title="navbarTitle" />
		</view>

		<!-- 内容区，下移 44px 防止遮挡 -->
		<scroll-view class="content" scroll-y>
			<!-- 当前小区 & 地址 -->
			<view class="section">
				<view class="row readonly">
					<text class="label">当前小区</text>
					<text class="value" style="padding-left: 20px;">{{ nowSelectedCity }}</text>
				</view>
				<view class="row">
					<image class="icon icon-clsaa" :src="quicon" mode="widthFix" />
					<text class="label">在哪里取件</text>
					<input class="value input" v-model="form.pickupAddress" :placeholder="pickupPlaceholder" />
				</view>
				<view class="row">
					<image class="icon icon-clsaa" :src="songicon" mode="widthFix" />
					<text class="label">送到哪里去</text>
					<input class="value input" v-model="form.deliveryAddress" placeholder="送货地址(省市区街道门牌等)" />
				</view>
			</view>

			<!-- 需求信息 & 上传 -->
			<view class="section">
				<view class="row">
					<text class="label">需求信息</text>
				</view>
				<textarea class="details" v-model="form.requirementText" placeholder="请上传订单截图或填写取件信息" />
				<!-- 使用封装好的图片上传组件 -->
				<img-picker :limit="1" @imglist="handleImgList" />
			</view>

			<!-- 数量/重量/优惠券/配送时间/合计/赏金 -->
			<view class="section">
				<!-- 数量 -->
				<view class="row">
					<text class="label">数量</text>
					<view class="stepper">
						<view class="btn" :class="{ disabled: form.quantity <= 0 }" @click="change('quantity', -1)">−
						</view>
						<text class="num">{{ form.quantity }}</text>
						<view class="btn" @click="change('quantity', 1)">＋</view>
					</view>
					<text class="suffix">件数</text>
				</view>

				<!-- 重量 -->
				<view class="row">
					<text class="label">重量</text>
					<view class="stepper">
						<view class="btn" :class="{ disabled: form.weight <= 0 }" @click="change('weight', -1)">−</view>
						<text class="num">{{ form.weight }}</text>
						<view class="btn" @click="change('weight', 1)">＋</view>
					</view>
					<text class="suffix">千克</text>
				</view>

				<!-- 优惠券 -->
				<view class="row link" @click="openCoupon">
					<text class="label">优惠券</text>
					<text class="value" style="text-align: right;">
						{{ form.couponCode || '请选择' }}
					</text>
					<text class="arrow">›</text>
				</view>

				<!-- 配送时间 -->
				<view class="row link" @click="showCalendar = true">
					<text class="label">配送时间</text>
					<text class="value"
						style="text-align: right; font-size: 12px;">{{ form.deliveryTime || '请选择配送时间' }}</text>
					<text class="arrow">›</text>
				</view>
				<!-- 合计 -->
				<view class="row">
					<text class="label">合计（共{{ form.totalItems }}件）</text>
					<text class="value price" style="text-align: right;">¥{{ serviceFee }}</text>
				</view>

				<!-- 赏金 -->
				<view class="row">
					<text class="label">赏金</text>
					<input style="color: #ff4a4a;" type="number" min="0" class="value input"
						v-model.number="form.tipAmount" placeholder="请填写赏金金额" />
					<!-- <text class="suffix" style="color: #ff4a4a;;">元</text> -->
				</view>
				<!-- 注意事项 -->
				<view class="row note-row note-style">
					<text class="note">注：若产生其他费用或发生其他情况与平台无关。</text>
				</view>
			</view>
		</scroll-view>
		<!--优惠券弹窗 -->
		<u-popup v-model="showCoupon" mode="bottom">
			<view class="popup-list">
				<view v-for="(c, i) in couponList" :key="c.id || i" class="popup-item" @click="applyCoupon(i)">
					<!-- 左侧：优惠金额 -->
					<view class="coupon-left">
						<text class="amount">¥{{ c.amount }}</text>
						<text class="unit"></text>
					</view>
					<!-- 右侧：说明 -->
					<view class="coupon-right">
						<text class="condition">满{{ c.minUseAmount }}可用</text>
						<text class="validity">有效期至{{ c.endTime.split(' ')[0] }}</text>
					</view>
				</view>

				<view v-if="couponList.length === 0" class="popup-empty">
					<text>暂无优惠券，请去领取！</text>
				</view>
			</view>
		</u-popup>

		<!-- 日期和时间范围选择弹窗 -->
		<u-popup v-model="showCalendar" mode="bottom" length="60%">
			<view class="dt-picker">
				<text class="picker-title">选择配送时间范围</text>
				<uni-datetime-picker v-model="tmpDateTimeRange" type="datetimerange" :start="startDate" :end="endDate"
					range-separator="~" />
				<u-button class="dt-confirm" type="primary" @click="confirmTimeRange">确认</u-button>
			</view>
		</u-popup>
		<!-- 底部固定栏：改为触发 confirm 提示 -->
		<view class="footer">
			<text class="footer-text">合计：<text class="price">¥ {{ serviceFee }}</text></text>
			<u-button class="footer-btn" type="error" @click="submitOrder">
				合计：¥ {{ serviceFee }}
			</u-button>
		</view>
	</view>
</template>

<script>
	// import uniDatetimePicker from '@dcloudio/uni-ui/lib/uni-datetime-picker/uni-datetime-picker.vue'
	import {
		HTTP_URL_PROD
	} from '@/api/config';
	import imgPicker from '@/components/img-picker.vue'
	export default {
		components: {
			imgPicker
		},
		data() {
			return {
				songicon: HTTP_URL_PROD +
					"/profile/upload/2025/05/22/2K1jiPIQUCuH8837fa419f9eecb545d1cc0bc331fbf7_20250522153902A077.png",
				quicon: HTTP_URL_PROD +
					"/profile/upload/2025/05/22/WmFTI2YMTHvqeb5de89c4b8c8d29437bdd70e3a24059_20250522153846A076.png",
				pickupPlaceholder: '取餐点(省市区街道门牌等)', // 默认是外卖
				navbarTitle: '代取外卖', // 默认
				form: {
					status: 0,
					type: '代取外卖',
					deliveryAddress: '',
					pickupAddress: '',
					delivery: '',
					requirementText: '',
					orderImageUrls: '', // 是一个字符串
					quantity: 0,
					totalItems: '',
					weight: 0,
					couponCode: '',
					deliveryTime: '',
					tipAmount: '',
					serviceFee: '',
				},
				nowSelectedCity: "",
				// pickupAddress2: this.nowSelectedCity,
				showCoupon: false,
				couponList: [],
				showCalendar: false,
				tmpDateTimeRange: [],
				startDate: this.getTodayStart(),
				endDate: '2030-12-31 23:59:59'
			}
		},
		computed: {
			baseTotal() {
				return this.form.quantity + this.form.weight;
			},
			serviceFee() {
				return (this.baseTotal + (parseFloat(this.form.tipAmount) || 0)).toFixed(2);
			}
		},
		onLoad(option) {
			console.log(option.type); // 取外卖 或 取快递
			this.form.type = option.type;

			if (option.type === '取外卖') {
				this.pickupPlaceholder = '取餐点(省市区街道门牌等)';
				this.navbarTitle = '代取外卖';
			} else if (option.type === '取快递') {
				this.pickupPlaceholder = '快递点(省市区街道门牌等)';
				this.navbarTitle = '代取快递';
			}
		},
		mounted() {
			const storedCommunity = uni.getStorageSync('community');
			if (storedCommunity) {
				this.nowSelectedCity = storedCommunity;
			} else {
				this.nowSelectedCity = this.$store.state.user.locationCom;
			}

			console.log(this.nowSelectedCity, 'this.nowSelectedCity');
		},


		methods: {
			change(field, delta) {
				const next = this.form[field] + delta;
				this.form[field] = next < 0 ? 0 : next;
				// 如果是改数量，就同步 totalItems
				if (field === 'quantity') {
					this.form.totalItems = this.form.quantity;
				}
			},
			handleImgList(imgs) {
				// 保存原始文件名数组
				this.orderImageFiles = imgs;
				// 拼接成完整 URL 给页面展示（可选）
				this.form.orderImageUrls = imgs.map(fileName => {
					return {
						url: this.$imgUrl + '/uploadPath/' + fileName
					};
				});
			},
			// 打开弹窗并请求优惠券
			async openCoupon() {
				try {
					this.showCoupon = true;
					const res = await this.$u.api.getAvailableCoupons({
						orderAmount: this.serviceFee
					});
					console.log('接口原始返回:', res);

					// 根据上面打印，选一种正确的赋值方式：
					this.couponList = Array.isArray(res.data) ? res.data : Array.isArray(res) ? res : [];

					console.log('拉取到优惠券列表：', this.couponList);
				} catch (err) {
					console.error('拉取可用优惠券失败', err);
					uni.showToast({
						title: '获取优惠券失败',
						icon: 'none'
					});
					this.showCoupon = false;
				}
			},



			// 选择某个优惠券
			applyCoupon(i) {
				const c = this.couponList[i];
				// 把“满X减Y”样式文字赋给 couponCode
				this.form.couponCode = `满${c.minUseAmount}减${c.amount}`;
				// 如果要提交给后端的参数里需要 id，就把它存进去
				// this.form.couponId = c.id;
				// 可选：存对象
				// this.selectedCoupon = c;
				this.showCoupon = false;

				// 如果选中后要重新计算价格，也可以在这里调用
				// this.recalculatePrice();
			},
			getTodayStart() {
				const now = new Date();
				const y = now.getFullYear();
				const m = String(now.getMonth() + 1).padStart(2, '0');
				const d = String(now.getDate()).padStart(2, '0');
				return `${y}-${m}-${d} 00:00:00`;
			},
			confirmTimeRange() {
				if (this.tmpDateTimeRange.length < 2) {
					uni.showToast({
						title: '请选择完整的时间范围',
						icon: 'none'
					});
					return;
				}
				this.form.deliveryTime = this.tmpDateTimeRange.join(' ~ ');
				this.showCalendar = false;
			},

			// —— 新增：先弹框确认 —— 
		  // 新增：表单校验
		  validateForm() {
		    if (!this.form.pickupAddress) {
		      uni.showToast({ title: '请填写取件地址', icon: 'none' });
		      return false;
		    }
		    if (!this.form.deliveryAddress) {
		      uni.showToast({ title: '请填写送货地址', icon: 'none' });
		      return false;
		    }
		    if (!this.form.requirementText && (!this.orderImageFiles || !this.orderImageFiles.length)) {
		      uni.showToast({ title: '请填写需求信息或上传订单截图', icon: 'none' });
		      return false;
		    }
		    if (this.form.quantity <= 0) {
		      uni.showToast({ title: '请设置数量', icon: 'none' });
		      return false;
		    }
		    if (this.form.weight <= 0) {
		      uni.showToast({ title: '请设置重量', icon: 'none' });
		      return false;
		    }
		    if (!this.form.couponCode) {
		      uni.showToast({ title: '请选择优惠券', icon: 'none' });
		      return false;
		    }
		    if (!this.form.deliveryTime) {
		      uni.showToast({ title: '请选择配送时间', icon: 'none' });
		      return false;
		    }
		    if (!this.form.tipAmount || this.form.tipAmount <= 0) {
		      uni.showToast({ title: '请填写赏金金额', icon: 'none' });
		      return false;
		    }
		    return true;
		  },
		
		  // 修改：提交前先校验
		  submitOrder() {
		    if (!this.validateForm()) {
		      return;
		    }
		    uni.showModal({
		      title: '确认提交',
		      content: `您确认要提交订单吗？\n共 ${this.form.quantity} 件，合计 ¥${this.serviceFee}`,
		      confirmText: '确定',
		      cancelText: '取消',
		      success: (res) => {
		        if (res.confirm) {
		          this._doSubmitOrder();
		        }
		      }
		    });
		  },
			// 提交逻辑 
			async _doSubmitOrder() {
				// 把计算结果写回 form，确保传给后端的是最新的数字
				this.form.serviceFee = parseFloat(this.serviceFee);

				// 添加空值检查 - 确保 orderImageFiles 是数组
				const files = this.orderImageFiles || [];
				const urls = files.map(fileName => this.$imgUrl + '/uploadPath/' + fileName).join(',');
				this.form.orderImageUrls = urls;

				if (!this.form.totalItems) {
					uni.showToast({
						title: '请选择件数',
						icon: 'none'
					});
					return false
				}
				console.log('订单数据', this.form);
				console.log('最终提交的图片路径：', this.form.orderImageUrls);

				try {
					const res = await this.$u.api.laddRequest(this.form);
					if (res.code === 200) {
						uni.showToast({
							title: '提交成功',
							icon: 'success'
						});
						setTimeout(() => {
							uni.navigateBack(-1);
						}, 1000);
					} else {
						uni.showToast({
							title: res.message || '提交失败',
							icon: 'none'
						});
					}
				} catch (err) {
					console.error('请求异常', err);
					uni.showToast({
						title: '网络异常，请稍后再试',
						icon: 'none'
					});
				}
			}

		}
	}
</script>

<style lang="scss" scoped>
	.page {
		display: flex;
		flex-direction: column;
		height: 100%;
		background: #f5f5f5;
	}

	.arro

	/* 头部 */
	.navbar {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		height: 44px;
		background: #fff;
		z-index: 10;
	}

	/* 主体，下移100px */
	.content {
		margin-top: 10px;
		flex: 1;
	}

	.section {
		margin: 10px;
		background: #fff;
		border-radius: 6px;
		margin-bottom: 12px;
		overflow: hidden;
	}

	.row {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 12px 16px;
		border-bottom: 1px solid #eee;
	}

	.row:last-child {
		border-bottom: none;
	}

	.readonly .label {
		font-weight: bold;
	}

	.icon {
		margin-right: 8px;
		font-size: 18px;
	}

	.label {
		// width: 100px;
		font-size: 14px;
		color: #333;
	}

	.time-picker-row {
		display: flex;
		justify-content: space-between;
		padding: 10rpx 20rpx;
		border-bottom: 1px solid #eee;
	}


	.value {
		flex: 1;
		font-size: 14px;
		color: #666;
	}

	.input {
		flex: 1;
		text-align: right;
		padding: 6px 8px;
		background: #f9f9f9;
		border-radius: 4px;
	}

	.details {
		width: 90%;
		min-height: 80px;
		margin: 0 auto;
		padding: 8px;
		background: #f9f9f9;
		border-radius: 4px;
	}

	.uploader {
		padding-bottom: 12px;
	}

	.stepper {
		display: flex;
		align-items: center;
		border: 1px solid #ddd;
		border-radius: 4px;
		overflow: hidden;
	}

	.btn {
		width: 28px;
		height: 28px;
		line-height: 28px;
		text-align: center;
		font-size: 18px;
		color: #333;
	}

	.btn.disabled {
		color: #ccc;
	}

	.num {
		width: 32px;
		text-align: center;
	}

	.suffix {
		margin-left: 8px;
		color: #333;
	}

	.link {
		cursor: pointer;
	}

	.arrow {
		margin-left: 4px;
		color: #ccc;
	}

	.price {
		color: #ff4a4a;
		font-weight: bold;
	}

	.note-row {
		justify-content: center;
		padding: 8px 16px;
	}

	.note {
		font-size: 12px;
		color: #999;
	}

	.popup-list {
		background: #fff;
		height: 260px;
		padding: 12px;
	}

	.icon-clsaa {
		width: 22px;
		height: 22px;
	}

	/* 每张券的卡片容器 */
	.popup-item {
		display: flex;
		margin-bottom: 12px;
		border-radius: 8px;
		overflow: hidden;
		background: #ffffff;
		/* 阴影 */
		box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
	}

	/* 左侧金额区 */
	.coupon-left {
		width: 120rpx;
		background: #ff7f50;
		/* 只对左侧圆角 */
		border-top-left-radius: 8px;
		border-bottom-left-radius: 8px;
		padding: 12rpx 0;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
	}

	.coupon-left .amount {
		font-size: 32rpx;
		font-weight: bold;
		color: #ffffff;
		line-height: 1;
	}

	.coupon-left .unit {
		font-size: 20rpx;
		color: #ffffff;
		margin-top: 4rpx;
	}

	/* 右侧说明区 */
	.coupon-right {
		flex: 1;
		padding: 12rpx 16rpx;
		display: flex;
		flex-direction: column;
		justify-content: center;
	}

	.coupon-right .condition {
		font-size: 31rpx;
		color: #333333;
		margin-bottom: 6rpx;
	}

	.coupon-right .validity {
		font-size: 20rpx;
		color: #999999;
	}

	/* 空状态提示 */
	.popup-empty {
		text-align: center;
		color: #999999;
		font-size: 24rpx;
		padding: 40rpx 0;
	}

	.popup-item:last-child {
		border-bottom: none;
	}

	.dt-picker {
		background: #fff;
		padding: 16px;
	}

	.picker-title {
		margin-top: 12px;
		font-size: 14px;
		color: #333;
		text-align: center;
	}

	.time-range-picker {
		display: flex;
		flex-direction: row;
		align-items: center;
		justify-content: center;
		margin: 8px 0;
	}

	.to {
		margin: 0 8px;
		font-size: 18px;
		color: #333;
	}

	.picker-item {
		padding: 12px 0;
		text-align: center;
		font-size: 16px;
		border-bottom: 1px solid #eee;
	}

	.dt-confirm {
		margin-top: 16px;
		width: 100%;
	}

	.footer {
		position: fixed;
		bottom: 0;
		left: 0;
		right: 0;
		height: 50px;
		background: #fff;
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 0 16px;
		border-top: 1px solid #eee;
	}

	.footer-text {
		font-size: 16px;
	}

	.footer-btn {
		// font-size: 28px;
		height: 36px;
		line-height: 36px;
		// padding: 0 24px;
		border-radius: 30px;
		font-weight: 600;
	}

	::v-deep .u-size-default {
		font-size: 36rpx !important;
		border-radius: 30px;
	}

	.note-style {
		// padding: 30px 10px 15px 10px;
		margin: 40px auto;
	}

	::v-deep .uni-date {
		margin: 30px auto;
	}
</style>