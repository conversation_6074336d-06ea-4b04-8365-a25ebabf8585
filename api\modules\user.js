export const useWxApi = (Vue, vm) => {
	return {
		// 获取openid
		getOpenId: (code) => vm.$u.get('/appletsUser/openId', {
			code
		}),
		// 通过openid登录
		loginByOpenId: (openId) => vm.$u.get('/appletsUser/loginByOpenId', {
			openId
		}),
		// 获取验证码
		getCodeImg: () => vm.$u.get('/captchaImage'),
		// 账号密码登录
		loginByPassword: (data) => vm.$u.post('/login', data),
		// 手机号登录
		wxLogin: (data) => vm.$u.post('/system/wechat/freeLogin', data),
		logout: () => vm.$u.post('/logout'),
		getInfo: () => vm.$u.get('/getInfo'),
		getRegionList: (name) => vm.$u.get(`/administrative/show-list?name=${name}`),
		bindRegion: (code) => vm.$u.post(`/administrative/bind-division?code=${code}`),
		getBindRegion: () => vm.$u.get(`/administrative/bind-list`),
		delBindRegion: (code) => vm.$u.post(`/administrative/unbind-division?code=${code}`),
		setDefaultRegion: (code) => vm.$u.post(`/administrative/enable-division?code=${code}`),
		getMyPosts: (data) => vm.$u.get(`/api/post/my-posts`, data),
		addPost: (data) => vm.$u.post(`/api/post/createPost`, data),
		getDet: (id) => vm.$u.get(`/api/post/post-detail?postId=${id}`),
		addLike: (data) => vm.$u.post(`/api/post/add-like`,data),
		addComment: (data) => vm.$u.post(`/api/post/add-comment`,data),
		getTopicPosts: (data) => vm.$u.get(`/api/post/activity-posts`,data),
		getActivityPosts: (data) => vm.$u.get(`/api/post/topic-posts`,data),
		getFollowList: (data) => vm.$u.get(`/api/post/follow-list`,data),
		addFollow: (data) => vm.$u.post(`/api/post/add-follow`,data),
		myLikedPosts: (data) => vm.$u.get(`/api/post/my-liked-posts`,data),
		delPosts: (id) => vm.$u.delete(`/api/post/delete-post?isDelete=true&id=${id}`),
	}
}