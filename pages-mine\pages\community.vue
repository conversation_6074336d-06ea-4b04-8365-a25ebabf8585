<template>
	<view class="slot">
		<Navbar title="我的帖子"></Navbar>
		<PostCard @getData="getData" v-for="(item, index) in circleList" :key="index" :data="{...item,pageType:'myT'}"
			:border="index != circleList.length - 1"></PostCard>
		<view v-if="circleList.length==0" style="margin-top: 100rpx;">
			<u-empty text="暂无帖子" mode="list"></u-empty>
		</view>
	</view>
</template>

<script>
	import PostCard from '@/pages/community/components/post-card';
	const token = uni.getStorageSync("App-Token");
	export default {
		components: {
			PostCard
		},
		data() {
			return {
				circleList: [],
				token: token,
			};
		},
		onShow() {
			this.getData()
		},
		methods: {
			getData() {
				if (this.token) {
					this.$u.api.getMyPosts({
						userId: this.$store.state.user.userInfo.userId
					}).then(r => {
						console.log(r,'rrrrs执行1')
						this.circleList = r.data
					})
				}

			},
			change(index) {
				this.current = index;
			},
		}
	};
</script>

<style lang="scss" scoped>
	.hot-nav {
		padding: 0rpx 0rpx 30rpx 0rpx;
		background-color: $app-theme-bg-color;
	}
</style>