<template>
	<view class="shop-detail-top">
		<!-- 背景图片层 -->
		<view class="background-layer" :style="{'backgroundImage': `url(${baseUrl + shop.backImg})`}">
			<view class="overlay"></view>
		</view>

		<!-- 商户信息卡片 -->
		<view class="shop-card">
			<!-- 商户头部信息 -->
			<view class="shop-header">
				<view class="shop-logo">
					<img class="logo-img" :src="baseUrl + shop.logoImg" alt="商户logo" />
					<view class="logo-badge" v-if="shop.shopStar >= 4.5">
						<text class="badge-text">优质</text>
					</view>
				</view>

				<view class="shop-info">
					<view class="shop-name">{{shop.name}}</view>
					<view class="shop-rating">
						<view class="rating-stars">
							<u-rate disabled active-color="#FFD700" inactive-color="#E5E5E5" :count="5" :value="Math.round(shop.shopStar || 0)"></u-rate>
						</view>
						<text class="rating-score">{{shop.shopStar ? shop.shopStar.toFixed(1) : '0.0'}}</text>
						<text class="monthly-sales">月售{{shop.order ? shop.order : 0}}</text>
					</view>
				</view>
			</view>

			<!-- 商户详细信息 -->
			<view class="shop-details">
				<view class="detail-row">
					<view class="detail-item">
						<view class="detail-icon">🕒</view>
						<view class="detail-content">
							<text class="detail-label">营业时间</text>
							<text class="detail-value">{{shop.businessTime || '未知'}}</text>
						</view>
					</view>
				</view>

				<view class="detail-row">
					<view class="detail-item">
						<view class="detail-icon">💰</view>
						<view class="detail-content">
							<text class="detail-label">起送费</text>
							<text class="detail-value">¥{{shop.qiSong || 0}}</text>
						</view>
					</view>
					<view class="detail-item">
						<view class="detail-icon">🚚</view>
						<view class="detail-content">
							<text class="detail-label">配送费</text>
							<text class="detail-value">¥{{shop.peiSong || 0}}</text>
						</view>
					</view>
				</view>

				<view class="detail-row">
					<view class="detail-item full-width">
						<view class="detail-icon">📍</view>
						<view class="detail-content">
							<text class="detail-label">店铺地址</text>
							<text class="detail-value address">{{shop.address || '暂无地址信息'}}</text>
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import { baseUrl } from '@/api/config';
	export default {
		name:"shop-detail-top",
		data() {
			return {
				baseUrl
			};
		},
		props:['shop']
	}
</script>

<style scoped lang="scss">
.shop-detail-top {
	position: relative;
	height: 420rpx;
	overflow: hidden;

	.background-layer {
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background-repeat: no-repeat;
		background-size: cover;
		background-position: center;
		filter: blur(2rpx);

		.overlay {
			position: absolute;
			top: 0;
			left: 0;
			right: 0;
			bottom: 0;
			background: linear-gradient(135deg, rgba(0,0,0,0.3) 0%, rgba(0,0,0,0.1) 100%);
		}
	}

	.shop-card {
		position: relative;
		z-index: 10;
		background: rgba(255, 255, 255, 0.95);
		backdrop-filter: blur(20rpx);
		border-radius: 32rpx;
		margin: 60rpx 30rpx 0;
		padding: 40rpx 30rpx;
		box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.1);
		border: 1rpx solid rgba(255, 255, 255, 0.2);

		.shop-header {
			display: flex;
			align-items: flex-start;
			margin-bottom: 40rpx;

			.shop-logo {
				position: relative;
				margin-right: 30rpx;

				.logo-img {
					width: 120rpx;
					height: 120rpx;
					border-radius: 24rpx;
					border: 4rpx solid #fff;
					box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.15);
				}

				.logo-badge {
					position: absolute;
					top: -8rpx;
					right: -8rpx;
					background: linear-gradient(135deg, #FF6B6B, #FF8E53);
					border-radius: 20rpx;
					padding: 4rpx 12rpx;

					.badge-text {
						color: #fff;
						font-size: 20rpx;
						font-weight: 600;
					}
				}
			}

			.shop-info {
				flex: 1;

				.shop-name {
					font-size: 44rpx;
					font-weight: 700;
					color: #2C3E50;
					margin-bottom: 16rpx;
					line-height: 1.2;
				}

				.shop-rating {
					display: flex;
					align-items: center;
					flex-wrap: wrap;
					gap: 16rpx;

					.rating-stars {
						display: flex;
						align-items: center;
					}

					.rating-score {
						color: #FF6B6B;
						font-size: 32rpx;
						font-weight: 600;
					}

					.monthly-sales {
						color: #7F8C8D;
						font-size: 28rpx;
						background: #F8F9FA;
						padding: 8rpx 16rpx;
						border-radius: 16rpx;
					}
				}
			}
		}

		.shop-details {
			.detail-row {
				display: flex;
				margin-bottom: 24rpx;
				gap: 20rpx;

				&:last-child {
					margin-bottom: 0;
				}

				.detail-item {
					flex: 1;
					display: flex;
					align-items: flex-start;
					background: #F8F9FA;
					border-radius: 16rpx;
					padding: 20rpx;

					&.full-width {
						flex: none;
						width: 100%;
					}

					.detail-icon {
						font-size: 32rpx;
						margin-right: 16rpx;
						margin-top: 4rpx;
					}

					.detail-content {
						flex: 1;

						.detail-label {
							display: block;
							color: #7F8C8D;
							font-size: 24rpx;
							margin-bottom: 8rpx;
							font-weight: 500;
						}

						.detail-value {
							display: block;
							color: #2C3E50;
							font-size: 28rpx;
							font-weight: 600;

							&.address {
								line-height: 1.4;
								word-break: break-all;
							}
						}
					}
				}
			}
		}
	}
}
</style>