<template>
	<view class="shop-detail-top" :style="{'backgroundImage': `url(${baseUrl + shop.backImg})`}">
		<view class="shop">
			<view class="head">
				<img class="img" :src="baseUrl + shop.logoImg" alt="" />
				<view class="title">
					<view class="name">
						{{shop.name}}
					</view>
					<view class="star">
						<u-rate disabled active-color="#f39800" inactive-color="#f39800" :count="1" :value="1"></u-rate>
						{{shop.shopStar ? shop.shopStar : 0}}
						<text class="sell">
							月售{{shop.order ? shop.order : 0}}
						</text>
					</view>
				</view>
			</view>
			<view class="detail">
				<text style="margin-right: 20rpx;">营业时间： {{shop.businessTime ? shop.businessTime :'未知'}}</text>		
				<text style="margin-right: 20rpx;">起送费： {{shop.qiSong ? shop.qiSong : 0}}元</text>		
				<text style="margin-right: 20rpx;">配送费： {{shop.peiSong ? shop.peiSong : 0}}元</text>		
				<text>店铺地址： {{shop.address}}</text>
			</view>
		</view>
	</view>
</template>

<script>
	import { baseUrl } from '@/api/config';
	export default {
		name:"shop-detail-top",
		data() {
			return {
				baseUrl
			};
		},
		props:['shop']
	}
</script>

<style scoped lang="scss">
.shop-detail-top{
	background-repeat: no-repeat;
	background-size: 100% 300rpx;
	height: 250px;
	padding-top: 120rpx;
}
.shop{
	border-radius: 20rpx;
	background: #fff;
	width: 90%;
	margin: 0 auto;
	padding: 40rpx 30rpx;
	
	.head{
		display: flex;
		align-items: center;
		
		.img{
			width: 160rpx;
			height: 160rpx;
			border-radius: 10rpx;
			margin-right: 20rpx;
		}
		
		.title{
			.name{
				font-size: 50rpx;
				font-weight: 700;
			}
			
			.sell{
				margin-left: 60rpx;
			}
		}
	}
	
	.detail{
		margin-top: 30rpx;
	}
}
</style>