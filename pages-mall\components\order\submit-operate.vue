<template>
	<view class="slot">
		<view class="inner">
			<view class="price">
				<text>￥</text>
				<text>{{ data.currentPrice }}</text>
			</view>
			<view class="right">
				<u-button :loading="btnLoading" loadingText="支付中.." type="primary" @click="goPayResult"
					:disabled="btnLoading">
					立即支付
				</u-button>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		HTTP_URL_PROD
	} from '@/api/config'; // 引入基础URL

	export default {
		props: {
			data: {
				type: Object,
				default: () => ({})
			}
		},
		data() {
			return {
				btnLoading: false
			}
		},
		computed: {
			totalPrice() {
				return (this.data.originalPrice * this.data.num).toFixed(2)
			}
		},
		methods: {
			// 获取微信登录code
			getWxCode() {
				return new Promise((resolve, reject) => {
					wx.login({
						success: (res) => {
							if (res.code) {
								resolve(res.code); // 成功返回code
							} else {
								reject(new Error('获取登录凭证失败: ' + res.errMsg));
							}
						},
						fail: (err) => {
							reject(new Error('微信登录接口调用失败: ' + err.errMsg));
						}
					});
				});
			},

			// 生成支付订单
			createOrder() {
				return new Promise((resolve, reject) => {
					this.$u.api.posyOrderGenerated(this.data)
						.then(res => {
							// 假设订单ID在res.msg中，根据实际接口返回调整
							if (res.msg) {
								resolve(res.msg); // 返回orderId
							} else {
								reject(new Error('生成订单失败: 未返回订单ID'));
							}
						})
						.catch(err => {
							reject(new Error('生成订单接口调用失败: ' + err.message));
						});
				});
			},

			// 调用微信支付
			requestPayment(payParams) {
				return new Promise((resolve, reject) => {
					wx.requestPayment({
						timeStamp: payParams.timeStamp.toString(),
						nonceStr: payParams.nonceStr,
						package: payParams.package,
						signType: payParams.signType || 'MD5',
						paySign: payParams.paySign,
						success: (res) => resolve(res),
						fail: (err) => reject(new Error('支付失败: ' + err.errMsg))
					});
				});
			},

			// 主流程
			async goPayResult() {
				try {
					this.btnLoading = true;

					// 1. 获取微信code
					const code = await this.getWxCode();
					console.log('获取到code:', code);

					// 2. 生成支付订单，获取orderId
					const orderId = await this.createOrder();
					console.log('获取到orderId:', orderId);

					// 3. 调用原生支付接口获取支付参数
					const payParams = await new Promise((resolve, reject) => {
						uni.request({
							url: HTTP_URL_PROD + '/system/wechat/pay/createPrepay',
							method: 'POST',
							header: {
								'Content-Type': 'application/x-www-form-urlencoded', // 表单提交格式
								'Authorization': this.$store.state.user.token // 携带token
							},
							data: {
								code: code,
								orderId: orderId
							},
							success: (res) => {
								if (res.data && res.data.code === 200 && res.data.data) {
									resolve(res.data.data); // 返回支付参数
								} else {
									reject(new Error('获取支付参数失败: ' + (res.data?.msg || '未知错误')));
								}
							},
							fail: (err) => {
								reject(new Error('支付接口调用失败: ' + err.errMsg));
							}
						});
					});
					console.log('获取到支付参数:', payParams);
					// 4. 发起微信支付
					const payResult = await this.requestPayment(payParams);
					if (payResult.errMsg === 'requestPayment:ok') {
						uni.showToast({
							title: '支付成功',
							icon: 'success',
							duration: 2000
						});
						// 支付成功后返回上一页
						setTimeout(() => {
							// uni.navigateBack();
							uni.navigateTo({
								url: '/pages-mall/pages/order/pay-result?currentPrice=' + this.data
									.currentPrice
							});
						}, 2000);
					}

				} catch (err) {
					// 错误处理
					console.error('流程错误:', err);
					uni.showToast({
						title: err.message || '操作失败，请重试',
						icon: 'none',
						duration: 3000
					});
				} finally {
					this.btnLoading = false; // 无论成功失败，都关闭加载状态
				}
			}
		}
	};
</script>
<style lang="scss" scoped>
	.slot {
		position: fixed;
		bottom: 0;
		left: 0;
		width: 100%;
		background-color: $app-theme-bg-color;
		min-height: 100rpx;
		box-shadow: $app-theme-shadow;
		padding-bottom: constant(safe-area-inset-bottom);
		padding-bottom: env(safe-area-inset-bottom);

		.inner {
			display: flex;
			justify-content: space-between;
			align-items: center;
			padding: 0 24rpx;

			.price {
				padding-left: 30rpx;

				text:nth-child(1) {
					font-size: 24rpx;
					font-family: PingFangSC-Medium, PingFang SC;
					font-weight: 500;
					color: $app-theme-text-money-color;
				}

				text:nth-child(2) {
					font-size: 36rpx;
					font-family: PingFangSC-Regular, PingFang SC;
					font-weight: 400;
					color: $app-theme-text-money-color;
				}
			}

			.right {
				display: flex;
				align-items: center;

				button {
					height: 100%;
					line-height: 100rpx;
					border-radius: 0 !important;
				}
			}
		}
	}
</style>