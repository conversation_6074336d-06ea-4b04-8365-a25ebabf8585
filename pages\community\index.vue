<template>
	<view class="page">
		<navbar-city :is-back="false" :cityListSelected="nowSelectedCity" title="社区互动"></navbar-city>
		<view v-if="phoneType=='h5'" style="height: 30px;background: linear-gradient(to bottom, #ffd9a4, #fffaf3);">
		</view>
		<view class="fixed-header" :style="{ top: fixedHeaderTop + 'px' }">
			<view class="xfx-sinput">
				<u-search bg-color="#fff" style="width: 80%;" @clear="getList" @search="getList" placeholder="搜索标题..."
					v-model="title" :show-action="false"></u-search>
				<view
					style="width: 20%;margin-left: 20rpx;display: flex;align-items: center;justify-content: space-around;">
					<!-- <u-icon name="bell" @click="toLink('/pages-mine/pages/message')" size="45"></u-icon> -->
					<u-icon name="plus" @click="toLink('/pages-community/pages/post/add')" size="45"></u-icon>
				</view>
			</view>
			<view class="tabsSel">
				<view class="button-item" v-for="(item, index) in scrollData" :key="index"
					:class="{ 'selected': current === index }" @click="selectItem(index)">
					{{ item }}
				</view>
			</view>
		</view>
		<view style="height:150rpx;"></view>
		<view v-if="circleList.length == 0"
			style="display: flex;align-items: center;justify-content: center;margin-top: 200rpx;">
			<u-empty text="暂无数据" mode="list"></u-empty>
		</view>
		<PostCard v-for="(item, index) in circleList" :key="index" :data="item"
			:border="index != circleList.length - 1"></PostCard>
		<view style="height: 100rpx;"></view>
	</view>
</template>
<script>
	import NavbarCity from '@/components/navbar/navbar-city.vue';
	import PostCard from '@/pages/community/components/post-card';
	const token = uni.getStorageSync("App-Token");
	export default {
		data() {
			const app = getApp();
			return {
				token: token,
				phoneType: process.env.UNI_PLATFORM,
				circleList: [],
				title: '',
				searchValue: '',
				nowSelectedCity: '',
				current: 0,
				scrollData: [
					"热门话题", "热门活动",
				],
				// 导航栏高度
				menuTop: app.globalData.menuTop,
				navBarHeight: app.globalData.navBarHeight,
				menuRight: app.globalData.menuRight,
				menuBotton: app.globalData.menuBotton,
				menuHeight: app.globalData.menuHeight,
				statusBarHeight: app.globalData.statusBarHeight,
				fixedHeaderTop: app.globalData.menuTop + app.globalData.navBarHeight, // 计算固定头部的顶部位置
			};
		},
		components: {
			PostCard,
			NavbarCity
		},
		mounted() {
			const app = getApp();
			if (process.env.UNI_PLATFORM === 'h5') {
				this.fixedHeaderTop = 32;
			} else {
				this.fixedHeaderTop = app.globalData.navBarHeight;
			}
			let that = this;
			// this.onShowHandler = function(data) { // 保存监听函数引用
			// 	that.nowSelectedCity = that.$store.state.user.locationCom;
			// 	that.getList()
			// };
			// uni.$on('onShow', this.onShowHandler);
		},
		beforeUnmount() {
			// uni.$off('onShow', this.onShowHandler); // 移除监听
		},
		methods: {
			getList() {
				if (this.token && this.$store.state.user.userInfo) {
					this.$u.api.getMyPosts({
						postType: this.current == 0 ? 'topic' : 'activity',
						title: this.title
					}).then(r => {
						console.log(r, '执行3')
						this.circleList = r.data
					})
				}

			},
			selectItem(index) {
				this.current = index;
				this.getList()
			},
			// 添加
			add() {
				let $this = this;
				uni.hideTabBar({
					animation: true,
					complete() {
						$this.$refs.AddPopup.open();
					}
				});
			},
			toLink(url) {
				uni.navigateTo({
					url: url
				})
			},
			// 关闭
			closeAddPopup() {
				uni.showTabBar({
					animation: true
				});
			}
		}
	};
</script>

<style lang="scss" scoped>
	.page {
		position: relative;
		background-color: #fff;
	}

	.fixed-header {
		position: fixed;
		left: 0;
		width: 100vw;
		height: 160rpx;
		z-index: 99;
		background: linear-gradient(to bottom, #ffd9a4, #fffaeb);
	}

	.xfx-sinput {
		width: 100%;
		padding: 10rpx 30rpx 14rpx 30rpx;
		box-sizing: border-box;
		display: flex;
		justify-content: space-between;
		align-items: center;
		border-radius: 20rpx;
	}

	.tabsSel {
		display: flex;
		padding: 20rpx 4%;
		justify-content: space-around;
		color: #999;

		.button-item {}

		.selected {
			font-weight: bold;
			color: #000;
		}
	}
</style>