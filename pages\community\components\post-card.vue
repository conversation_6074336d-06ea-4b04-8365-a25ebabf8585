<template>
	<view class="slot" @click="goPostDetail()" :class="{ border }">
		<!-- 用户 -->
		<CommuntiyAvatar :data="data" @more="more"></CommuntiyAvatar>
		<!--内容-->
		<view class="main-text">{{ data.title }}</view>
		<view class="imglist">
			<view style="margin-right: 20rpx;" v-for="(val, x) in data.imgs" :key="x">
				<u-image v-if="smallCard" width="180rpx" height="180rpx" :src="imgUrl + val">
					<view slot="error" style="font-size: 24rpx;">加载失败</view>
				</u-image>
				<u-image v-if="!smallCard" width="216rpx" height="216rpx" :src="imgUrl + val">
					<view slot="error" style="font-size: 24rpx;">加载失败</view>
				</u-image>
			</view>
		</view>
		<!--商品区-->
		<!-- <view class="shopp"><CommunityGoods :data="data.content" v-if="data.content"></CommunityGoods></view> -->
		<!--位置 点赞 评论-->
		<view class="seting">
			<view class="seting-text">
				<u-icon name="eye" size="32rpx" :color="iconColor"></u-icon>
				<view class="seTnum">{{ data.readCount || 0 }}</view>
			</view>
			<view class="seting-text">
				<u-icon name="chat" size="32rpx" :color="iconColor"></u-icon>
				<view class="seTnum">{{ data.commentCount || 0 }}</view>
			</view>
			<view class="seting-text">
				<u-icon name="thumb-up" size="32rpx" :color="data.isLike == 1 ? appThemeColor : iconColor"></u-icon>
				<view class="seTnum">{{ data.likeCount || 0 }}</view>
			</view>
		</view>
	</view>
</template>

<script>
	// import CommunityGoods from '@/pages/community/components/community-goods';
	import CommuntiyAvatar from '@/pages/community/components/community-avatar';
	// import CommunityTag from '@/pages/community/components/community-tag';
	// import AddPopup from '@/components/add-popup';
	export default {
		name: 'community-card',
		components: {
			CommuntiyAvatar,
			// CommunityTag
		},
		props: {
			data: {
				type: Object,
				default: () => {
					return {};
				}
			},
			border: {
				type: Boolean,
				default: false
			},
			smallCard: {
				type: Boolean,
				default: false
			}
		},
		data() {
			return {
				imgUrl: this.$imgUrl,
				iconColor: this.$appTheme.appThemeTextGrayColor,
				appThemeColor: this.$appTheme.appThemeColor
			};
		},
		methods: {

			// 详情
			goPostDetail() {
				uni.navigateTo({
					url: '/pages-community/pages/post/detail?id=' + this.data.id
				});
			},

			// 更多
			more() {
				let that = this;
				uni.showActionSheet({
					// '置顶', '关注',
					itemList: ['删除'],
					success(res) {
						if (res.tapIndex == 0) {
							uni.showModal({
								title: "提示",
								content: "是否删除该帖子？",
								cancelText: "取消",
								confirmText: "确定",
								success: function(res) {
									if (res.confirm) {
										that.$u.api.delPosts(that.data.id).then(r => {
											uni.showToast({
												title: r.msg,
												icon: 'none'
											});
											that.$emit('getData');
										});
									}

								},
							});
						}
						// else if (res.tapIndex == 1) { } else if (res.tapIndex == 2) { }
					}
				});
			}
		}
	};
</script>

<style lang="scss" scoped>
	.slot {
		padding: 30rpx;
		background-color: $app-theme-bg-color;

		&.border {
			border-bottom: 1rpx solid $app-theme-border-color;
		}
	}

	.main-text {
		font-size: 28rpx;
		font-weight: 400;
		color: $app-theme-text-black-color;
		line-height: 40px;
	}

	.imglist {
		display: flex;
		justify-content: flex-start;
	}

	//购物
	.shopp {
		margin-top: 24rpx;
	}

	//设置 评论点赞
	.seting-text {
		display: flex;
		align-items: center;
		background-color: none;

		&.shadow {
			background-color: $app-theme-bg-gray-deep-color;
			padding: 10rpx 16rpx;
			border-radius: 6rpx;

			view {
				font-size: 22rpx;
				font-family: PingFangSC-Regular, PingFang SC;
				font-weight: 400;
				color: $app-theme-text-black-color;
			}
		}
	}

	.seting {
		display: flex;
		align-items: center;
		justify-content: space-between;
		margin: 36rpx 0 0 0;
	}

	.seTnum {
		font-size: 24rpx;
		color: $app-theme-shop-gray-color;
		line-height: 28rpx;
		margin-left: 4rpx;
	}
</style>