<template>
	<view class="merchant-cart" @click="toCart">
		<u-icon size='60' name="shopping-cart"></u-icon>
		<view class="count">
			{{cartCount}}
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				cartCount: 0
			}
		},
		mounted() {
			this.getCartCount()
		},
		methods: {
			getCartCount() {
				if (this.$store.state.user.token) {
					this.$u.api.getCartCount().then(res => {
						this.cartCount = res.data
						this.$store.commit('cart/SET_COUNT', res.data)
					})
				}
			},
			toCart() {
				if (this.$store.state.user.token) {
					uni.navigateTo({
						url: '/pages-zone/pages/store/cart/cart'
					})
				} else {
					uni.showModal({
						title: "提示",
						content: "您还未登录，请先登录",
						cancelText: "取消",
						confirmText: "确定",
						success: function(res) {
							if (res.confirm) {
								uni.reLaunch({
									url: "/pages/login/index",
								});
							}
						},
					});
				}
			},
		}
	}
</script>

<style scoped lang="scss">
	.merchant-cart {
		position: fixed;
		width: 120rpx;
		height: 120rpx;
		right: 20rpx;
		bottom: 300rpx;
		border-radius: 50%;
		background: #fff;
		display: flex;
		box-shadow: #dbd8db 0rpx 0rpx 10rpx 2rpx;
		align-items: center;
		justify-content: center;

		.count {
			position: absolute;
			width: 30rpx;
			height: 30rpx;
			color: #fff;
			background: #f47500;
			border-radius: 50%;
			text-align: center;
			line-height: 30rpx;
			right: 10rpx;
			top: 10rpx;
		}
	}
</style>