<template>
	<view class="navbar">
		<view class="home" :class='{active:navbarIndex==0}' @click="handleNavbarChange(0)">
			<u-icon size='36' name="home"></u-icon>
			首页
		</view>
		<view class="category" :class='{active:navbarIndex==1}' @click="handleNavbarChange(1)">
			<u-icon size='36' name="grid"></u-icon>
			分类
		</view>
		<view class="order" :class='{active:navbarIndex==2}' @click="handleNavbarChange(2)">
			<u-icon size='36' name="list-dot"></u-icon>
			订单
		</view>
	</view>
</template>

<script>
	export default {
		name: 'merchant-navbar',
		props:{
			navbarIndex:{
				type: Number,
				default: 0
			}
		},
		methods:{
			handleNavbarChange(index){
				this.$emit("change", index)
			}
		}
	}
</script>

<style scoped lang="scss">
.navbar{
	display: grid;
	grid-template-columns: 1fr 1fr 1fr;
	position: fixed;
	width: 100%;
	height: 120rpx;
	bottom: 0;
	left: 0;
	background: #fff;
	
	.home,.category,.order{
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		
		&.active{
			color: #f68400;
		}
	}
}
</style>
