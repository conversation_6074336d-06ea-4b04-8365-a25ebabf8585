<template>
	<view class="page">
		<!-- 自定义头部 navbar -->
		<u-navbar back-icon-name="arrow-left" title=" " :custom-back="navbarBack"
			:background="{ backgroundColor: 'rgba(0,0,0,0)' }" :border-bottom="false"
			:is-back="pageStatus != 'home'"></u-navbar>

		<!-- logo -->
		<view class="logo-wrap" v-if="pageStatus == 'home' || pageStatus == 'getWxRole' || pageStatus == 'loginByCode'">
			<image class="logo" src=""></image>
			<view class="app-name">上虞 96345</view>
		</view>

		<!-- 默认登录页面显示 -->
		<view style="width: 100%;margin-top: 120rpx;" v-if="pageStatus == 'home'">
			<view v-if="phoneType=='mp-weixin'" class="text-area" @click="loginByWx">手机号快捷登录</view>
			<view class="login" @click="loginByCode">账号密码登录</view>
			<view style="padding-top:120rpx">
				<view style="color: #565656;text-align: center;" @click="lchenreturn()">随便逛逛</view>
			</view>
		</view>

		<!-- 微信授权登录，获取用户信息 -->
		<!-- 		<view style="width: 100%;" v-if="pageStatus == 'getWxRole'">
			<view class="info">为提供优质服务,需要获取你的以下信息 :</view>
			<view class="public">
				<view class="public-dot"></view>
				<view class="public-text">你的公开信息(头像、昵称等)</view>
			</view>
			<view class="text-enter" @click="getWxLoginRole">授权进入</view>
		</view> -->

		<!-- 微信登录小程序，再授权获取用户信息之后 -->
		<view class="loginWx" v-if="pageStatus == 'getWxRole'">
			<view class="title">欢迎使用 上虞 96345</view>
			<view class="desc">立即登录享受优质服务</view>
			<!-- 			<view class="avatar">
				<image mode="aspectFill" :src="wxLoginUserInfo.userInfo.avatarUrl"></image>
			</view> -->
			<u-button type="warning" open-type="getPhoneNumber" @getphonenumber="getphonenumber">手机号快捷登录</u-button>
		</view>

		<!-- 手机号密码登录 -->
		<view class="loginSmsCode" v-if="pageStatus == 'loginByCode'">
			<u-form :model="loginCodeForm" ref="loginCodeForm">
				<u-form-item :border-bottom="true">
					<u-input v-model="loginCodeForm.phone" placeholder="请输入手机号"
						:placeholder-style="{ color: '#ccc', fontSize: '30rpx' }" />
				</u-form-item>
				<u-form-item :border-bottom="true">
					<u-input v-model="loginCodeForm.password" placeholder="请输入密码"
						:placeholder-style="{ color: '#ccc', fontSize: '30rpx' }" />
				</u-form-item>
				<u-form-item :border-bottom="true">
					<!-- 					<view style="display: flex;justify-content: space-between;align-items: center;">
						<u-input style="width: 60%;" v-model="loginCodeForm.smsCode" placeholder="请输入验证码"
							:placeholder-style="{ color: '#ccc', fontSize: '30rpx' }" />
						<view class="smscode" @click="getCode" v-if="!codeOPS.isGetting">{{ codeOPS.getted ? '重新获取验证码' :
							'获取验证码' }}</view>
						<view class="smscode" v-else>{{ codeOPS.countDownTime }}s</view>
					</view> -->
					<view class="yzm">
						<input type="number" name="code" maxlength="6" placeholder="请输入验证码" v-model="Codeyzm" />
						<view class="yanzhengma" @click="getCodeImg">
							<image class="aimg" :src="'data:image/gif;base64,'+CodeForm.img" mode=""></image>
						</view>
					</view>
				</u-form-item>
				<!-- <view class="forgot-password" @click="navToForgotPassword">忘记密码？</view> -->
			</u-form>
			<view style="padding-top:64rpx">
				<u-button :disabled="!loginCodeForm.phone || !loginCodeForm.password" type="primary" shape="circle"
					@click="login('password')">
					登录
				</u-button>
			</view>

		</view>
		<!-- #ifdef MP-WEIXIN -->
		<!-- 协议勾选 -->
		<view class="" v-if="agree_flag_name">
			<view class="agree" v-if="pageStatus == 'home' || pageStatus == 'getWxRole' || pageStatus == 'loginByCode'">
				<view class="agree_img" @click="changeAgreeFlag" v-show="!agree_flag">
					<image src="../../static/operate/check.png"></image>
				</view>
				<view class="agree_img" @click="changeAgreeFlag" v-show="agree_flag">
					<image src="../../static/operate/checked.png"></image>
				</view>
				<view class="agree_text">
					用户隐私协议
					<!-- <span @click="privacy">《用户服务协议》</span> -->
				</view>
			</view>
		</view>
		<!-- #endif -->
		<!-- #ifdef MP-WEIXIN -->
		<ws-wx-privacy id="privacy-popup" themeColor="#ffa11e"></ws-wx-privacy>
		<!-- #endif -->
	</view>
</template>

<script>
	import FunUniappTheme from '@/theme.scss';
	import {
		HTTP_URL_DEV
	} from '@/api/config';
	export default {
		data() {
			return {
				phoneType: process.env.UNI_PLATFORM,
				pageStatus: 'home',
				https: HTTP_URL_DEV,
				agree_flag: false,
				agree_flag_name: true,
				wxLoginUserInfo: {},
				openId: '',
				userInfo: {},
				showPassword: false, // 新增密码可见状态
				loginCodeForm: {
					phone: null,
					password: null // 改为password字段
				},
				CodeForm: null, //验证码图片
				Codeyzm: '', //验证码
				loginCodeFormRules: {
					phone: [{
							required: true,
							message: '请输入手机号',
							trigger: ['change', 'blur']
						},
						{
							validator: (rule, value, callback) => {
								return this.$u.test.mobile(value);
							},
							message: '手机号码不正确',
							trigger: ['change', 'blur']
						}
					],
					password: [ // 密码验证规则
						{
							required: true,
							message: '请输入密码',
							trigger: ['change', 'blur']
						},
						{
							min: 6,
							max: 20,
							message: '密码长度在6-20个字符之间',
							trigger: ['change', 'blur']
						}
					]
				}
			};
		},
		onReady() {

			if (this.pageStatus == 'loginByCode') {
				this.$refs.loginCodeForm.setRules(this.loginCodeFormRules);
			}
		},
		onLoad() {
			console.log(this.phoneType)
			this.getCodeImg()
			this.changeAgreeFlag()
			// #ifdef MP-WEIXIN
			this.changetPrivacySetting()
			// #endif


		},
		methods: {
			changetPrivacySetting() {
				wx.getPrivacySetting({
					success: res => {
						if (res.needAuthorization) {
							// 需要弹出隐私协议
							this.agree_flag_name = true
						} else {
							this.agree_flag_name = false
							// 用户已经同意过隐私协议，所以不需要再弹出隐私协议，也能调用已声明过的隐私接口
							// wx.getUserProfile()
							// wx.chooseMedia()
							// wx.getClipboardData()
							// wx.startRecord()
						}
					},
					fail: () => {},
					complete: () => {}
				})
			},

			// privacy() {
			// 	wx.openPrivacyContract({
			// 		success: () => {}, // 打开成功
			// 		fail: () => {}, // 打开失败
			// 		complete: () => {}
			// 	})
			// },
			getCodeImg() {
				this.$u.api.getCodeImg().then(res => {
					this.CodeForm = res
				}).finally(() => {
					uni.hideLoading();
				});
			},
			changeAgreeFlag() {
				wx.getPrivacySetting({
					success: res => {
						console.log(res, 'res隐私协议')
						if (res.needAuthorization) {
							// 需要弹出隐私协议
							console.log("未授权，隐私协议")
							uni.requirePrivacyAuthorize({
								success: () => {
									console.log('同意');
									this.agree_flag = true;
									this.agree_flag_name = false
									// 用户同意授权
									// 继续小程序逻辑
								},
								fail: () => {
									console.log('拒绝');
									this.agree_flag = false;
									this.agree_flag_name = true
								}, // 用户拒绝授权
								complete: () => {}
							})
						} else {
							console.log("已授权，隐私协议")
							// 用户已经同意过隐私协议，所以不需要再弹出隐私协议，也能调用已声明过的隐私接口
							// wx.getUserProfile()
							// wx.chooseMedia()
							// wx.getClipboardData()
							// wx.startRecord()
						}
					},
					fail: () => {},
					complete: () => {}
				})
			},

			loginByWx() {
				if (!this.agree_flag && this.agree_flag_name) {
					uni.showToast({
						icon: 'none',
						title: '请先阅读并同意隐私协议'
					});
					return;
				}
				this.pageStatus = 'getWxRole';
			},

			navbarBack() {
				if (this.pageStatus == 'getWxRole' || this.pageStatus == 'loginByWx' || this.pageStatus == 'loginByCode') {
					this.pageStatus = 'home';
				}
			},

			getphonenumber(e) {
				if (e.detail.errMsg == 'getPhoneNumber:fail user deny') {
					uni.showToast({
						icon: 'none',
						title: '请先授权获取手机号'
					});
					return;
				}
				this.$store.dispatch('user/Login', e.detail.code).then((res) => {
					this.loginSuccess()
				}).catch((err) => {
					uni.showToast({
						icon: 'none',
						title: '登录失败，请重试'
					});
				})
			},
			loginSuccess() {
				this.$store.dispatch('user/GetInfo').then(res => {
					uni.reLaunch({
						url: '/pages/home/<USER>'
					});
				})
			},
			loginByCode() {
				if (!this.agree_flag && this.agree_flag_name) {
					uni.showToast({
						icon: 'none',
						title: '请先阅读并同意隐私协议'
					});
					return;
				}
				this.pageStatus = 'loginByCode';
			},

			navToForgotPassword() {
				uni.navigateTo({
					url: '/pages/forgot-password/index'
				});
			},

			login(type) {
				const data = {
					username: this.loginCodeForm.phone,
					password: this.loginCodeForm.password,
					code: this.Codeyzm,
					uuid: this.CodeForm.uuid,
				};
				this.$store.dispatch('user/PasswordLogin', data)
					.then(() => {
						this.loginSuccess();
					})
					.catch(err => {
						uni.showToast({
							title: err.message || '登录失败',
							icon: 'none'
						});
						this.getCodeImg(); // 刷新验证码
					});
			},
			lchenreturn() {
				uni.reLaunch({
					url: '/pages/home/<USER>'
				});
			}
		}
	};
</script>

<style lang="scss" scoped>
	.page {
		padding: 0 40rpx;
		background: linear-gradient(to bottom, #ffd9a4, #fffaf3);
		// background: linear-gradient(to bottom, #ffff83 50%, #fff 100%);
	}

	.yzm {
		position: relative;
		width: 100%;

		input {
			width: 104px;
		}

		.yanzhengma {
			// border: 1px solid #d8d8d8;
			width: 172rpx;
			text-align: center;
			margin-left: 24rpx;
			height: 76rpx;
			border-radius: 8rpx;
			position: absolute;
			top: -16rpx;
			right: 34rpx;
			z-index: 11;

			.aimg {
				width: 100%;
				height: 100%;
				object-fit: cover;
			}
		}
	}



	.logo-wrap {
		display: flex;
		justify-content: center;
		padding-top: 100rpx;
		padding-bottom: 140rpx;
		flex-wrap: wrap;

		.logo {
			height: 220rpx;
			width: 220rpx;
		}

		.app-name {
			width: 100%;
			padding-top: 40rpx;
			text-align: center;
			font-size: 44rpx;
			font-weight: 500;
			color: $app-theme-text-black-color;
		}
	}

	.text-area {
		width: 100%;
		height: 96rpx;
		background: #ffba4d;
		font-weight: 400;
		border-radius: 56rpx;
		color: $app-theme-text-white-color;
		line-height: 16px;
		font-size: 32rpx;
		text-align: center;
		line-height: 96rpx;
		/* background: url(../../static/img/logo_newhope.png); */
	}

	.info {
		font-size: 28rpx;
		font-weight: 400;
		color: $app-theme-text-black-color;
		line-height: 28rpx;
		margin-top: 180rpx;
	}

	.public {
		display: flex;
		align-items: center;
		margin-top: 30rpx;

		.public-dot {
			width: 6rpx;
			height: 6rpx;
			background: #999999;
			margin-right: 10rpx;
		}

		.public-text {
			font-size: 24rpx;
			font-weight: 400;
			color: #8f92a1;
			line-height: 24rpx;
		}
	}

	.text-enter {
		width: 100%;
		margin-top: 50rpx;
		height: 96rpx;
		background: #ffba4d;
		font-weight: 400;
		border-radius: 56rpx;
		color: #ffffff;
		line-height: 16px;
		font-size: 32rpx;
		text-align: center;
		line-height: 96rpx;
	}

	.head {
		margin-top: 70rpx;
		width: 100%;
		display: flex;
		justify-content: space-between;
	}

	.back {
		width: 40rpx;
		height: 40rpx;
	}

	.login {
		text-align: center;
		font-size: 32rpx;
		font-weight: 400;
		line-height: 32rpx;
		margin-top: 48rpx;
		color: #d09941;
		font-weight: bold;
	}

	.agree {
		position: fixed;
		left: 50%;
		transform: translate(-50%, 0);
		bottom: 66rpx;
		width: 100vw;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.agree_img image {
		width: 32rpx;
		height: 32rpx;
		display: flex;
		align-items: center;
	}

	.agree_text {
		margin-left: 8rpx;
		font-size: 24rpx;
		font-weight: 400;
		color: #8f92a1;
		line-height: 24rpx;
		display: flex;
		align-items: center;

		span {
			color: #ffba4d;
		}
	}

	.loginWx {
		width: 100%;
		padding-top: 64rpx;

		.title {
			text-align: left;
			font-size: 22px;
			font-family: PingFang-SC-Regular, PingFang-SC;
			font-weight: 400;
			color: #171717;
			line-height: 30px;
			margin-bottom: 12rpx;
		}

		.desc {
			text-align: left;
			font-size: 14px;
			font-family: PingFang-SC-Regular, PingFang-SC;
			font-weight: 400;
			color: #8f92a1;
			line-height: 14px;
			margin-bottom: 120rpx;
		}

		.avatar {
			width: 160rpx;
			height: 160rpx;
			overflow: hidden;
			border-radius: 50%;
			display: flex;
			align-items: center;
			justify-content: center;
			margin: 128rpx auto 160rpx auto;

			image {
				width: 100%;
				height: 100%;
			}
		}
	}

	.loginSmsCode {
		width: 100%;
	}

	.smscode {
		font-size: 28rpx;
		font-family: PingFang-SC-Regular, PingFang-SC;
		font-weight: 400;
		color: #ffba4d;
		line-height: 28rpx;
	}
</style>