<template>
	<view class="page">
		<Navbar title="确认支付"></Navbar>

		<!-- 金额展示 -->
		<view class="money-show">
			<view class="desc">实付金额</view>
			<view class="money">
				<text>￥</text>
				<text>{{dingdan.currentPrice}}</text>
			</view>
			<view class="time">支付剩余时间 07:58</view>
		</view>

		<!-- 支付方式 -->
		<view class="pay-type">
			<u-radio-group v-model="payType">
				<view class="title">选择支付方式</view>
				<view class="item" @click="payType = 'wxPay'">
					<view class="left">
						<view class="logo">
							<image src="../../static/icon-wx-pay.png" mode=""></image>
						</view>
						<view class="name">微信支付</view>
					</view>

					<view class="check"><u-radio shape="circle" :active-color="appThemeColor" icon-size="16"
							name="wxPay"></u-radio></view>
				</view>
				<view class="item" @click="payType = 'aliPay'">
					<view class="left">
						<view class="logo">
							<image src="../../static/icon-ali-pay.png" mode=""></image>
						</view>
						<view class="name">支付宝</view>
					</view>
					<view class="check"><u-radio shape="circle" :active-color="appThemeColor" icon-size="16"
							name="aliPay"></u-radio></view>
				</view>
			</u-radio-group>
		</view>

		<!-- 支付 -->
		<view class="btn">
			<u-button type="primary" shape="circle" @click="goPayResult">
				<text v-show="payType == 'wxPay'">微信支付</text>
				<text v-show="payType == 'aliPay'">支付宝支付</text>
				<text>￥{{dingdan.currentPrice}}</text>
			</u-button>
		</view>
		<view class="qxx" @click="order">
			取消订单
		</view>
		<u-modal v-model="orshow" :show-cancel-button="true" :content="content" @confirm="confirm"></u-modal>
		<u-toast ref="uToast" />
	</view>
</template>

<script>
	export default {
		data() {
			return {
				payType: 'wxPay',
				appThemeColor: this.$appTheme.appThemeColor,
				dingdan: null,
				ddId: null,
				orshow: false,
				content: '确认取消当前订单?'
			};
		},
		onLoad(options) {
			let userInfoAddress = JSON.parse(decodeURIComponent(options.dingdan));
			this.dingdan = userInfoAddress
			this.orderGenerated()
		},
		methods: {
			order() {
				this.orshow = true
			},
			// 取消订单
			confirm() {
				this.$u.api.postcancellationoforder(this.ddId).then(res => {
					if (res.code === 200) {
						this.$refs.uToast.show({
							title: '已取消',
							type: 'success',
						})
						setTimeout(()=>{
							uni.navigateBack({
								delta: 3
							});
						},1500)
						
					}
				});
			},
			// 生成订单
			orderGenerated() {
				this.$u.api.posyOrderGenerated(this.dingdan).then(res => {
					this.ddId = res.msg
				});
			},
			// 支付
			goPayResult() {
				this.$u.api.posyPayment(this.ddId).then(res => {
					console.log(this.dingdan, '完成支付')
					if (res.code === 200) {
						uni.navigateTo({
							url: '/pages-mall/pages/order/pay-result?currentPrice=' + this.dingdan
								.currentPrice
						});
					}
				});
				// 订单对象，从服务器获取
				// var orderInfo = {
				//   "appid": "wx499********7c70e",  // 应用ID（AppID）
				//   "partnerid": "148*****52",      // 商户号（PartnerID）
				//   "prepayid": "wx202254********************fbe90000", // 预支付交易会话ID
				//   "package": "Sign=WXPay",        // 固定值
				//   "noncestr": "c5sEwbaNPiXAF3iv", // 随机字符串
				//   "timestamp": **********,        // 时间戳（单位：秒）
				//   "sign": "A842B45937F6EFF60DEC7A2EAA52D5A0" // 签名，这里用的 MD5 签名
				// };
				// uni.getProvider({
				//     service: 'payment',
				//     success: function (res) {
				//         console.log(res.provider)
				//         if (~res.provider.indexOf('wxpay')) {
				//             uni.requestPayment({
				//                 "provider": "wxpay",  //固定值为"wxpay"
				//                 "orderInfo": orderInfo, 
				//                 success: function (res) {
				//                     var rawdata = JSON.parse(res.rawdata);
				//                     console.log("支付成功");
				//                 },
				//                 fail: function (err) {
				//                     console.log('支付失败:' + JSON.stringify(err));
				//                 }
				//             });
				//         }
				//     }
				// });
			}
		}
	};
</script>

<style lang="scss" scoped>
	.page {
		background-color: $app-theme-bg-color;
	}

	.money-show {
		padding: 24rpx 0 48rpx 0;

		.desc {
			width: 100%;
			text-align: center;
			font-size: 24rpx;
			font-family: PingFang-SC-Medium, PingFang-SC;
			font-weight: 500;
			color: #2d2b36;
			margin-bottom: 8rpx;
		}

		.money {
			margin-bottom: 22rpx;
			display: flex;
			align-items: center;
			justify-content: center;

			text:nth-child(1) {
				font-size: 48rpx;
				font-family: PingFang-SC-Medium, PingFang-SC;
				font-weight: 500;
				color: #2d2b36;
			}

			text:nth-child(2) {
				font-size: 72rpx;
				font-family: DINAlternate-Bold, DINAlternate;
				font-weight: bold;
				color: #2d2b36;
			}
		}

		.time {
			text-align: center;
			font-size: 24rpx;
			font-family: PingFang-SC-Medium, PingFang-SC;
			font-weight: 500;
			color: #2d2b36;
		}
	}

	.pay-type {
		padding: 0 30rpx;
		margin-top: 80rpx;

		.title {
			font-size: 30rpx;
			font-family: PingFangSCSemibold-, PingFangSCSemibold;
			font-weight: normal;
			color: #2d2b36;
			padding-bottom: 4rpx;
		}

		.item {
			display: flex;
			align-items: center;
			justify-content: space-between;
			padding-bottom: 24rpx;
			padding-top: 26rpx;
			border-bottom: 1rpx solid #efefef;

			.left {
				display: flex;
				align-items: center;
				justify-content: flex-start;

				.logo {
					width: 64rpx;
					height: 64rpx;
					margin-right: 30rpx;

					image {
						width: 100%;
						height: 100%;
					}
				}

				.name {
					font-size: 28rpx;
					font-family: PingFangSC-Regular, PingFang SC;
					font-weight: 400;
					color: #171717;
				}
			}

			.check {}
		}
	}

	.u-icon {
		display: flex !important;
	}

	.btn {
		padding: 0 30rpx;
		position: absolute;
		bottom: 140rpx;
		left: 0;
		width: 100%;
	}

	.qxx {
		position: absolute;
		bottom: 80rpx;
		left: 0;
		width: 100%;
		text-align: center;
	}
</style>