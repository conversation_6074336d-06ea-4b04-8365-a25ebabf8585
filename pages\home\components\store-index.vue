<template>
	<view class="shop">
		<view class="shop-cls">
			<Tabs backgroundColor='#fff' :listData="shopCategoryList" @change="activeCategory"></Tabs>
			<!-- <view class="shop-cls-item" :class="{active:active===item.id}" v-for="(item, index) in shopCategoryList"
				:key="item.id" @click="activeCategory(item.id)">
				{{item.text}}
			</view> -->
		</view>
		<view class="shop-list">
			<view class="shop-list-item" v-for="(item, index) in shopList" :key="index" @click="goShops(item)">
				<view class="list_left">
					<image :src="baseUrl + '' + item.logoImg"></image>
				</view>
				<view class="list_right">
					<view class="right_top">
						<view class="shop-name">{{ item.name }}</view>
						<view class="shop-address">{{ item.address }}</view>
						<view class="shop-star">
							<u-rate disabled allowHalf active-color="#f39800" inactive-color="#f39800" :count="5"
								:value="Math.round(item.shopStar)"></u-rate>
							{{ (item.shopStar || 0).toFixed(2) }}
						</view>
					</view>
					<view class="right_bottom">
						<view class="bottom_left">
							<span class="qi-song">起送￥{{ item.qiSong }}</span>
							<span class="pei-song">配送约￥{{ item.peiSong }}</span>
						</view>
						<view class="bottom_right">月售{{item.order > 1000 ? '1000+' : item.order}}</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		baseUrl
	} from '@/api/config';
	import Tabs from '@/components/tabs.vue';
	export default {
		name: "shop",
		data() {
			return {
				// 店铺分类列表
				shopCategoryList: [],
				// 分类选中索引
				active: 1,
				// 店铺列表
				shopList: [
					// {
					// 	id:1,
					// 	img: require('./1.png'),
					// 	name: '7天超市',
					// 	address: '福建省上街镇学府南路16-1号',
					// 	star: 5.00,
					// 	qiSong: 30,
					// 	peiSong: 1,
					// 	sellCount: 136,
					// },
					// {
					// 	id:2,
					// 	img: require('./1.png'),
					// 	name: '7天超市',
					// 	address: '福建省上街镇学府南学府南路16-1号',
					// 	star: 4.20,
					// 	qiSong: 30,
					// 	peiSong: 1,
					// 	sellCount: 1001
					// },
				],
				baseUrl,
			};
		},
		components: {
			Tabs
		},
		mounted() {
			this.getShopCategoryList().then(() => {
				this.getShopList(this.shopCategoryList[0].id)
			})
		},
		methods: {
			getShopList(id) {
				// if (this.token) {
				this.$u.api.getShopByCategory(id).then(res => {
					console.log(res, '是否为空2')
					this.shopList = res.data.list
				})
				// }
			},
			getShopCategoryList() {
				return this.$u.api.getShopCategory().then(res => {
					this.shopCategoryList = res.data.list.map(i => ({
						text: i.name,
						id: i.id
					}))
				})
				this.shopCategoryList = [{
					text: '快送超市',
					id: 1
				}, {
					text: '土特产',
					id: 2
				}, {
					text: '社区美食',
					id: 3
				}, {
					text: '养老中心',
					id: 4
				}]
			},
			activeCategory(index) {
				let id = this.shopCategoryList[index].id
				this.getShopList(id)
			},
			goShops({
				id
			}) {
				uni.navigateTo({
					url: '/pages-zone/pages/store/shop/shop?id=' + id,
				})
			}
		}
	}
</script>

<style scoped lang="scss">
	.shop-cls {
		height: 100rpx;
		background: #fff;

		.shop-cls-item {
			text-align: center;
			line-height: 100rpx;
			color: #a0a0a0;

			&.active {
				font-weight: 700;
				color: #000;
			}
		}
	}

	.shop-list {
		margin: 20rpx;

		.shop-list-item {
			display: flex;
			background: #fff;
			padding: 24rpx 24rpx 24rpx 24rpx;
			margin-bottom: 20rpx;

			.list_left {
				display: flex;
				align-items: center;

				image {
					width: 200rpx;
					height: 200rpx;
				}
			}

			.list_right {
				margin-left: 24rpx;
				width: 100%;

				.right_top {
					font-size: 32rpx;
					margin-top: 6rpx;

					.shop-name {
						font-size: 38rpx;
						font-weight: 700;
					}

					.shop-star {
						color: #f39800;
						font-weight: 600;
					}
				}

				.right_bottom {
					display: flex;
					justify-content: space-between;
					align-items: center;
					padding-bottom: 12rpx;

					.bottom_left {
						display: flex;
						align-items: center;

						.qi-song {
							margin-right: 10rpx;
						}
					}

					.bottom_right {
						text-align: center;
						line-height: 56rpx;
						width: 132rpx;
						height: 56rpx;
						border-radius: 30rpx;
					}
				}
			}
		}
	}
</style>