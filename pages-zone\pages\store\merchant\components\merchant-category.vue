<template>
	<view class="merchant-category">
		<view class="shopCategoryList">
			<view v-for="item in shopCategoryList" :key='item.id' @click="handleCategory(item)"
				class="shopCategoryList-item">
				<view class="img">
					<img :src="baseUrl + item.imgUrl" alt="" />
				</view>
				<view class="name">
					{{item.name}}
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		baseUrl
	} from '@/api/config'
	export default {
		name: "merchant-category",
		data() {
			return {
				shopCategoryList: [],
				baseUrl
			}
		},
		mounted() {
			this.getShopCategory()
		},
		methods: {
			getShopCategory() {
				this.$u.api.getShopCategory().then(res => {
					this.shopCategoryList = res.data.list
				})
			},
			handleCategory(val) {
				uni.navigateTo({
					url: '/pages-zone/pages/store/merchant/merchant-detail?id=' + val.id + '&title=' + val.name,
				})
			},
		}
	}
</script>

<style scoped lang="scss">
	.shopCategoryList {
		background: #fff;
		margin-top: 30rpx;
		border-radius: 20rpx;
		padding: 30rpx;
		display: grid;
		grid-template-columns: 1fr 1fr 1fr 1fr;
		grid-gap: 30rpx;
		grid-column-gap: 40rpx;
		box-shadow: 0 2px 2px 1px #eee;

		.shopCategoryList-item {
			display: flex;
			flex-direction: column;
			align-items: center;

			.img {
				border-radius: 50% 50% 50% 0;
				overflow: hidden;
				height: 130rpx;
				width: 100%;

				img {
					width: 130rpx;
					height: 130rpx;
				}
			}
		}
	}
</style>