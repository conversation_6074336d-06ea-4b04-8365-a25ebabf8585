<template>
	<view class="page">
		<!-- 一般导航栏 -->
		<Navbar title="帖子详情"></Navbar>
		<view class="inner">
			<!-- 用户 -->
			<CommuntiyAvatar @report="report" @getData="getInfo(detailData)" :data="detailData" :isFollow="true"
				operateType="report"></CommuntiyAvatar>
			<view class="title">{{ detailData.title }}</view>
			<ImgSwiper v-if="detailData.imgs&&detailData.imgs.length>0" :list="detailData.imgs"></ImgSwiper>
			<view style="padding-top:30rpx;">
				<u-parse :html="detailData.content"></u-parse>
			</view>
			<!-- 操作 -->
			<view class="operate">
				<view></view>
				<!-- 喜欢 -->
				<view class="like" :class="detailData.isLiked?'like_btn':''" @click.stop="like">
					<u-icon name="heart" size="22" :color="appThemeColor"></u-icon>
					<text>{{detailData.isLiked?'已点赞':'点赞'}}</text>
				</view>
			</view>
		</view>
		<!-- 评论区 -->
		<TitleOperate :title="'评论（' + detailData.commentCount + '）'" titleSize="32rpx"></TitleOperate>
		<view class="evaluate-list">
			<PostEvaluateCard @report="report" @toMessage="toMessage"
				:border="detailData.commentTree.length - 1 != index" v-for="(item, index) in detailData.commentTree"
				:key="index" :data="item"></PostEvaluateCard>
		</view>
		<!-- 操作 -->
		<view class="input" @click.stop>
			<u-input v-model="ideaText" type="text" :placeholder="codeText" style="width: 65%; height: 100%;"
				:focus="focus" @blur="focus = false" border="true" />
			<view @click="cancelReply" class="cancel-btn">取消回复</view>
			<view @click="upText" class="goods">发送评论</view>
		</view>
		<!-- <view class="fixed-btn">
			<u-field  @focus="delInfo"  id="commentField" :focus="focus" label-width="0" v-model="ideaText" :placeholder="codeText">
				<view slot="right" @confirm="upText" class="goods">发送评论</view>
			</u-field>
		</view> -->
	</view>
</template>

<script>
	const app = getApp();
	import CommuntiyAvatar from '@/pages/community/components/community-avatar';
	import PostEvaluateCard from '@/pages-community/components/post/post-evaluate-card.vue';
	import ImgSwiper from '@/components/img-swiper.vue';
	import TitleOperate from '@/components/title-operate.vue';
	import SelectReason from '@/pages-community/components/select-reason.vue';
	export default {
		components: {
			CommuntiyAvatar,
			ImgSwiper,
			TitleOperate,
			PostEvaluateCard,
			SelectReason
		},
		data() {
			return {
				focus: false,
				codeText: '说点什么~',
				ideaText: '',
				parentId: '',
				// 底部安全高度
				safeAreaHeight: app.globalData.safeAreaHeight,
				// 详情内容
				detailData: null,
				// 举报原因
				currentReasonIndex: 0,
				reasonOps: [{
						label: '广告骚扰',
						value: '0'
					},
					{
						label: '色情低俗',
						value: '1'
					},
					{
						label: '政治宗教',
						value: '2'
					},
					{
						label: '虚假欺骗',
						value: '3'
					},
					{
						label: '侵权(肖像、诽谤、抄袭、冒用)',
						value: '4'
					},
					{
						label: '其他',
						value: '5'
					}
				]
			};
		},
		onLoad(opt) {
			this.getInfo(opt)
		},
		methods: {
			toMessage(data) {
				console.log(data)
				this.codeText = '回复' + data.name
				this.parentId = data.id;
				this.focus = true;
			},
			cancelReply() {
				this.codeText = '说点什么~';
				this.ideaText = '';
				this.parentId = '';
				this.focus = false;
			},
			getInfo(opt) {
				this.$u.api.getDet(opt.id).then(r => {
					this.detailData = r.data
					if (r.data.imgs) {
						this.detailData.imgs = this.detailData.imgs.map((item) => {
							item = this.$imgUrl + item;
							return item;
						});
					}
				})
			},
			like() {
				this.$u.api.addLike({
					userId: this.$store.state.user.userInfo.userId,
					postId: this.detailData.id,
					isLiked: this.detailData.isLiked ? false : true,
				}).then(r => {
					uni.showToast({
						title: r.msg,
						icon: 'none'
					});
					this.getInfo(this.detailData)
				})
			},
			upText() {
				if(this.ideaText==''){
					return uni.showToast({
						title:'说点什么~'
					})
				}
				this.$u.api.addComment({
					userId: this.$store.state.user.userInfo.userId,
					postId: this.detailData.id,
					comments: this.ideaText,
					parentId: this.codeText === '说点什么~' ? '' : this.parentId,
				}).then(r => {
					uni.showToast({
						title: r.msg,
						icon: 'none'
					});
					this.getInfo(this.detailData);
					this.cancelReply();
				});
			},
			// 举报
			report() {
				this.$refs.SelectReason.open(this.currentReasonIndex);
			},
		}
	};
</script>

<style lang="scss" scoped>
	.like {
		padding: 0rpx 24rpx;
		height: 44rpx;
		border-radius: 999rpx;
		border: 1rpx solid #000;
		display: flex;
		align-items: center;
		justify-content: flex-start;
		line-height: 44rpx;
		color: #000;

		text {
			margin-left: 8rpx;
			font-size: 22rpx;
			font-family: PingFangSC-Regular, PingFang SC;
			font-weight: 400;

		}
	}

	.like_btn {
		color: $app-theme-color;
		border: 2rpx solid $app-theme-color;
		background-color: rgba($app-theme-color, 0.1);
	}

	.page {
		background-color: $app-theme-bg-color;
		padding-bottom: 100rpx;
	}

	.inner {
		padding: 30rpx;
		border-bottom: 1px solid $app-theme-border-color;
	}

	.title {
		font-size: 28rpx;
		font-family: PingFangSC-Regular, PingFang SC;
		font-weight: 400;
		color: $app-theme-text-black-color;
		margin-bottom: 24rpx;
	}

	.is-goods {
		margin-top: 24rpx;
		margin-bottom: 16rpx;
		font-size: 24rpx;
		font-family: PingFang-SC-Regular, PingFang-SC;
		font-weight: 400;
		color: $app-theme-text-black-color;
	}

	.operate {
		display: flex;
		align-items: center;
		justify-content: space-between;
		margin-top: 8rpx;
	}

	.fixed-btn {
		position: fixed;
		bottom: 0;
		left: 0;
		z-index: 199;
		width: 100%;
		padding: 10rpx 30rpx;
		// display: flex;
		// align-items: center;
		// justify-content: space-between;
		background-color: $app-theme-bg-color;
		border-top: 1px solid $app-theme-border-color;
		padding-bottom: constant(safe-area-inset-bottom);
		padding-bottom: env(safe-area-inset-bottom);

		.evaluate {
			width: 100%;
			height: 76rpx;
			background: $app-theme-bg-gray-color;
			// padding:0 24rpx;
			box-sizing: border-box;
			margin-bottom: 10rpx;
			display: flex;
			align-items: center;
			justify-content: space-between;

			v-deep .u-input {
				width: 500rpx;
			}
		}


	}

	.evaluate-list {
		padding: 30rpx;
	}



	.input {
		background-color: $app-theme-bg-color;
		position: fixed;
		bottom: 0rpx;
		left: 0;
		border: 1rpx solid #ccc;
		width: 100%;
		height: 100rpx;
		display: flex;
		padding: 10rpx;
	}

	.input>::v-deep input {
		width: 80%;
		height: 100%;
	}

	.goods {
		width: 25%;
		// height: 80rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		background: $app-theme-color;
		font-size: 28rpx;
		font-family: PingFang-SC-Regular, PingFang-SC;
		font-weight: 400;
		color: $app-theme-text-white-color;
	}

	.cancel-btn {
		width: 20%;
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: 28rpx;
		color: #999;
		cursor: pointer;
	}
</style>