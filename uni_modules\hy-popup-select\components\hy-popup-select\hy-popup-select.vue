<template>
	<view>
		<uni-popup ref="popup" :type="type" :animation="animation" :is-mask-click="isMaskClick"
			:background-color="backgroundColor" :mask-background-color="maskBackgroundColor"
			:borderRadius="borderRadius" :safe-area="safeArea">
			<view class="popup-box" :style="{width,height}">
				<view class="popup-header" v-if="isShowTitle">
					<view class="title">{{title}}</view>
					<view class="btn">
						<text @click="cancel" class="cancel">取消</text>
						<text class="done" @click="confirm" :style="confirmTextStyle">确定</text>
					</view>
				</view>
				<view v-if="showFilter">
					<input class="search-input" v-model="filterVal" type="text" placeholder="请输入"
						@input="inputFilter" />
				</view>
				<scroll-view class="popup-content" :scroll-y="true">
					<view v-for="(item,index) in arr" :key="index" class="popup-content-item"
						@click="itemClick(item,index)">
						<slot :item="item" :index="index">
							<text>{{item[labelKey] }}</text>
						</slot>
						<image v-if="!item.disabled" class="check-img" :src="getCheckImg(item)">
						</image>
					</view>
				</scroll-view>
			</view>
		</uni-popup>
	</view>
</template>

<script>
	import props from './props.js';
	export default {
		components: {},
		props,
		emits: ['close', 'save', 'update:modelValue', 'itemClick'],
		data() {
			return {
				filterVal: '',
				oldSelectids: [],
				arr: [],
				selecteds: [], // 选中的项
				disableds: [], // 禁用的项
				allList: []
			};
		},
		watch: {
			list: {
				handler(val) {
					let tempArr = val?.map(o => {
						if (typeof o === "string" || typeof o === "number") {
							return {
								[this.labelKey]: o,
								[this.valueKey]: o
							}
						}
						return o
					}) || []
					this.arr = tempArr
					this.allList = tempArr
				},
				deep: true,
				immediate: true
			},
			// #ifndef VUE3
			id: {
				handler(val) {
					this.initVal(val)
				},
				deep: true,
				immediate: true
			},
			// #endif
			// #ifdef VUE3
			modelValue: {
				handler(val) {
					this.initVal(val)
				},
				deep: true,
				immediate: true
			},
			// #endif

			disabledArr: {
				handler(val = []) {
					this.disableds = val
				},
				deep: true,
				immediate: true
			}
		},
		computed: {
			labelKey() {
				return this?.props?.name ?? 'name'
			},
			valueKey() {
				return this?.props?.id ?? 'id'
			}
		},
		methods: {
			initVal(val) {
				if (val === '' || val === undefined || val === null) {
					this.selecteds = []
					return
				}
				if (Array.isArray(val)) {
					this.selecteds = val
				} else {
					this.selecteds = [val]
				}
			},

			// 选项点击
			itemClick(item, index) {
				let val = item[this.valueKey]
				let selected = this.selecteds.includes(val)
				this.$emit('itemClick', {
					val,
					item,
					index,
					selected: !selected
				})
				// 禁用的项 (如果配置了disabledArr属性,同时list属性传递的数组有选项属性为disabled则都会禁用)
				if (item.disabled || this.disableds.includes(val)) {
					return
				}
				// 选中的选项,并且不允许取消选中
				if (selected && !this.cancelSelectItem) {
					return
				}
				this.multiple ? this.multipleClick(val, selected) : this.singleClick(val, selected)
			},

			// 单选点击
			singleClick(val, selected) {
				this.selecteds = selected ? [] : [val]
			},

			// 多选点击
			multipleClick(val, selected) {
				// 最大数量校验
				if ((this.selecteds.length + 1) > this.maxNum && !selected) {
					uni.showToast({
						icon: 'none',
						title: this.maxNumToast
					})
					return
				}
				if (selected) {
					this.selecteds = this.selecteds.filter(o => {
						return o != val
					}) || []
				} else {
					this.selecteds.push(val)
				}
			},
			// 过滤
			inputFilter() {
				this.arr = this.allList.filter(m => {
					let tempStr = ''
					this.filterKeys.map(n => {
						let str = m?.[n] ?? ''
						tempStr = tempStr + str
					})
					if (this.searchDistinguishCapital) {
						return tempStr.toUpperCase().includes(this.filterVal.toUpperCase())
					} else {
						return tempStr.includes(this.filterVal)
					}
				})
			},
			getCheckImg(item) {
				let imageName = ''
				let bol = this.selecteds.includes(item[this.valueKey])
				// 多选
				if (this.multiple) {
					imageName = bol ? 'checkbox-icon' : 'no-checkbox-icon'
				} else {
					imageName = bol ? 'check-icon' : 'no-check-icon'
				}
				console.log(imageName,'imageName')
				return `/static/icon/${imageName}.png`
			},
			// 打开弹窗
			open() {
				this.oldSelectids = [...this.selecteds]
				this.$refs.popup.open()
			},
			// 关闭
			close() {
				this.$emit('close')
				this.filterVal = ''
				this.inputFilter()
				this.$refs.popup.close()
			},
			// 取消
			cancel() {
				this.close()
				this.selecteds = [...this.oldSelectids]
			},
			//  确定
			confirm() {
				let filterArr = this.arr.filter(o => {
					return this.selecteds.includes(o[this.valueKey])
				})
				let id = this.multiple ? this.selecteds : this.selecteds?.[0]
				let labels = filterArr.map(o => o[this.labelKey]) || []
				// #ifndef VUE3
				this.$emit('input', id)
				// #endif
				// #ifdef VUE3
				this.$emit('update:modelValue', id)
				// #endif
				this.$emit('save', {
					id,
					name: this.multiple ? labels : labels.join(','),
					arr: filterArr
				})
				this.close()
			}
		}
	}
</script>

<style lang="scss" scoped>
	.check-img {
		width: 16px;
		height: 16px;
	}

	.popup-box {

		.popup-header {
			display: flex;
			align-items: center;
			height: 60px;
			position: relative;
			border-bottom: solid 1px #F0F0F0;

			.title,
			.btn {
				position: absolute;
				top: 0;
				left: 0;
				width: 100%;
				height: 100%;
				display: flex;
				align-items: center;
				font-size: 14px;
			}

			.title {
				justify-content: center;
				font-weight: bold;
				color: #000000;
				font-size: 16px;
			}

			.btn {
				justify-content: space-between;

				.cancel {
					margin-left: 16px;
				}

				.done {
					font-weight: 400;
					flex-shrink: 0;
					margin-right: 16px;
				}
			}
		}

		.search-input {
			border: solid 1px #DDDDDD;
			padding: 10px;
			margin: 10px 16px 0 16px;
			border-radius: 5px;
		}

		.popup-content {
			height: calc(100% - 120px);

			.popup-content-item {
				margin: 0 16px;
				padding: 12px 0;
				display: flex;
				align-items: center;
				border-bottom: solid 1px #F0F0F0;
				justify-content: space-between;
			}
		}
	}
</style>