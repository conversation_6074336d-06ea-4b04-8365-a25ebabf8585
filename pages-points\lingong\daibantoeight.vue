<template>
	<view class="page">
		<!-- 顶部导航 -->
		<view class="navbar">
			<Navbar :title="navbarTitle" />
		</view>

		<!-- 内容区，下移 44px 防止遮挡 -->
		<scroll-view class="content" scroll-y>
			<!-- 当前小区 & 地址 -->
			<view class="section">
				<view class="row readonly">
					<text class="label">当前小区</text>
					<text class="value" style="padding-left: 20px;">{{ nowSelectedCity }}</text>
				</view>
			</view>
			<view class="section" v-if="navbarTitle =='需要帮忙'">
				<view class="row readonly" @click="show=true">
					<text class="label">服务项目</text>
					<text class="value" style="padding-left: 20px;">{{ form.serviceType }}</text>
				</view>
			</view>
			<!-- 需求信息 & 上传 -->
			<view class="section">
				<view class="row">
					<text class="label">需求信息</text>
				</view>
				<textarea class="details" v-model="form.requirementText" placeholder="请填写具体要求,描述越详细,接单越快。" />
				<!-- 使用封装好的图片上传组件 -->
				<img-picker :limit="1" @imglist="handleImgList" />
			</view>

			<!-- 数量/重量/优惠券/配送时间/合计/赏金 -->
			<view class="section">
				<view class="row">
					<text class="label">服务地址</text>
					<input class="value input" v-model="form.serviceAddress" placeholder="服务地址(省市区街道门牌等)" />
				</view>

				<!-- 配送时间 -->
				<view class="row link" @click="showCalendar = true">
					<text class="label">配送时间</text>
					<text class="value"
						style="text-align: right; font-size: 12px;">{{ form.serviceTime || '请选择配送时间' }}</text>
					<text class="arrow">›</text>
				</view>
				<!-- 合计 -->
				<view class="row">
					<text class="label">合计</text>
					<text class="value price" style="text-align: right;">¥{{ totalFee }}</text>
				</view>

				<!-- 赏金 -->
				<view class="row">
					<text class="label">赏金</text>
					<input style="color: #ff4a4a;" type="number" min="0" class="value input"
						v-model.number="form.tipAmount" placeholder="请填写赏金金额" />
					<!-- <text class="suffix" style="color: #ff4a4a;;">元</text> -->
				</view>

				<!-- 注意事项 -->
				<view class="row note-row note-style">
					<text class="note">注：若产生其他费用或发生其他情况与平台无关。</text>
				</view>
			</view>
		</scroll-view>

		<!-- 优惠券弹窗 -->
		<u-popup v-model="showCoupon" mode="bottom">
			<view class="popup-list">
				<view v-for="(c, i) in couponList" :key="i" class="popup-item" @click="applyCoupon(i)">{{ c }}</view>
			</view>
		</u-popup>
		<!-- 日期和时间范围选择弹窗 -->
		<u-popup v-model="showCalendar" mode="bottom" length="60%">
			<view class="dt-picker">
				<text class="picker-title">选择配送时间范围</text>
				<uni-datetime-picker v-model="tmpDateTimeRange" type="datetimerange" :start="startDate" :end="endDate"
					range-separator="~" />
				<u-button class="dt-confirm" type="primary" @click="confirmTimeRange">确认</u-button>
			</view>
		</u-popup>
		<!-- 底部固定栏：改为触发 confirm 提示 -->
		<view class="footer">
			<text class="footer-text">合计：<text class="price">¥ {{ totalFee }}</text></text>
			<u-button class="footer-btn" type="error" @click="submitOrder">
				合计：¥ {{ totalFee }}
			</u-button>
		</view>
		<u-select v-model="show" @confirm="confirm" :list="list"></u-select>
	</view>
</template>

<script>
	import uniDatetimePicker from '@dcloudio/uni-ui/lib/uni-datetime-picker/uni-datetime-picker.vue'
	import imgPicker from '@/components/img-picker.vue'
	export default {
		components: {
			uniDatetimePicker,
			imgPicker
		},
		data() {
			return {
				navbarTitle: '宠物喂养', // 默认
				form: {
					status: 0,
					serviceType: '宠物喂养',
					// deliveryAddress: '',
					serviceAddress: '',
					delivery: '',
					requirementText: '',
					orderImageUrls: '',
					quantity: 0,
					totalItems: '1',
					weight: 0,
					couponCode: '',
					serviceTime: '',
					tipAmount: '',
					totalFee: '',
				},
				nowSelectedCity: '',
				pickupAddress2: '福州市',
				showCoupon: false,
				couponList: ['无', '满10减2', '满20减5'],
				showCalendar: false,
				tmpDateTimeRange: [],
				startDate: this.getTodayStart(),
				endDate: '2030-12-31 23:59:59',
				show: false,
				list: [{
						value: '宠物喂养',
						label: '宠物喂养'
					},
					{
						value: '宠物帮遛',
						label: '宠物帮遛'
					},
					{
						value: '衣服干洗',
						label: '衣服干洗'
					},
					{
						value: '家庭快修',
						label: '家庭快修'
					},
					{
						value: "陪护作业",
						label: "陪护作业"
					},
					{
						value: "到家家教",
						label: "到家家教"
					},
					{
						value: "到家家教",
						label: "到家家教"
					},
					{
						value: "其他家事",
						label: "其他家事"
					},
				]
			}
		},
		computed: {
			baseTotal() {
				return this.form.quantity + this.form.weight;
			},
			totalFee() {
				return (this.baseTotal + (parseFloat(this.form.tipAmount) || 0)).toFixed(2);
			}
		},
		onLoad(option) {
			console.log(option.serviceType); // 取外卖 或 取快递
			if (option.serviceType == 101) {
				this.navbarTitle = '需要帮忙';
				this.form.serviceType = '宠物喂养';
				return;
			}
			this.form.serviceType = option.serviceType;
			if (option.serviceType === '宠物喂养') {
				this.navbarTitle = '宠物喂养';
			} else if (option.serviceType === '宠物帮遛') {
				this.navbarTitle = '宠物帮遛';
			} else if (option.serviceType === '衣服干洗') {
				this.navbarTitle = '衣服干洗';
			} else if (option.serviceType === '家庭快修') {
				this.navbarTitle = '家庭快修';
			} else if (option.serviceType === '陪护作业') {
				this.navbarTitle = '陪护作业';
			} else if (option.serviceType === '到家家教') {
				this.navbarTitle = '到家家教';
			} else if (option.serviceType === '带娃煮饭') {
				this.navbarTitle = '带娃煮饭';
			} else if (option.serviceType === '其他家事') {
				this.navbarTitle = '其他家事';
			}
		},
		mounted() {
			const storedCommunity = uni.getStorageSync('community');
			if (storedCommunity) {
				this.nowSelectedCity = storedCommunity;
			} else {
				this.nowSelectedCity = this.$store.state.user.locationCom;
			}
			console.log(this.nowSelectedCity, 'this.nowSelectedCity');
		},
		methods: {
			confirm(e) {
				this.form.serviceType = e[0].value;
			},
			change(field, delta) {
				const next = this.form[field] + delta;
				this.form[field] = next < 0 ? 0 : next;
				// 如果是改数量，就同步 totalItems
				if (field === 'quantity') {
					// this.form.totalItems = this.form.quantity;
				}
			},
			handleImgList(imgs) {
				// 保存原始文件名数组
				this.orderImageFiles = imgs;

				// 拼接成完整 URL 给页面展示（可选）
				this.form.orderImageUrls = imgs.map(fileName => {
					return {
						url: this.$imgUrl + '/uploadPath/' + fileName
					};
				});
			},
			applyCoupon(i) {
				this.form.couponCode = this.couponList[i];
				this.showCoupon = false;
			},
			getTodayStart() {
				const now = new Date();
				const y = now.getFullYear();
				const m = String(now.getMonth() + 1).padStart(2, '0');
				const d = String(now.getDate()).padStart(2, '0');
				return `${y}-${m}-${d} 00:00:00`;
			},
			confirmTimeRange() {
				if (this.tmpDateTimeRange.length < 2) {
					uni.showToast({
						title: '请选择完整的时间范围',
						icon: 'none'
					});
					return;
				}
				this.form.serviceTime = this.tmpDateTimeRange.join(' ~ ');
				this.showCalendar = false;
			},
			// —— 新增：先弹框确认 —— 
		  // 新增：表单校验
		  validateForm() {
		    // 如果是“需要帮忙”模式，校验服务项目
		    if (this.navbarTitle === '需要帮忙' && !this.form.serviceType) {
		      uni.showToast({ title: '请选择服务项目', icon: 'none' });
		      return false;
		    }
		    // 服务地址必填
		    if (!this.form.serviceAddress) {
		      uni.showToast({ title: '请填写服务地址', icon: 'none' });
		      return false;
		    }
		    // 需求信息或上传图片
		    if (!this.form.requirementText && (!this.orderImageFiles || !this.orderImageFiles.length)) {
		      uni.showToast({ title: '请填写需求信息或上传图片', icon: 'none' });
		      return false;
		    }
		    // 服务时间必选
		    if (!this.form.serviceTime) {
		      uni.showToast({ title: '请选择服务时间', icon: 'none' });
		      return false;
		    }
		    // 赏金必填且大于 0
		    if (this.form.tipAmount == null || this.form.tipAmount <= 0) {
		      uni.showToast({ title: '请填写赏金金额', icon: 'none' });
		      return false;
		    }
		    return true;
		  },
		
		  // 修改：提交前先执行校验
		  submitOrder() {
		    if (!this.validateForm()) {
		      return;
		    }
		    uni.showModal({
		      title: '确认提交',
		      content: `您确认要提交订单吗？\n合计：¥${this.totalFee}`,
		      confirmText: '确定',
		      cancelText: '取消',
		      success: (res) => {
		        if (res.confirm) {
		          this._doSubmitOrder();
		        }
		      }
		    });
		  },
			// 提交逻辑 
			async _doSubmitOrder() {
				// 把计算结果写回 form，确保传给后端的是最新的数字
				this.form.totalFee = parseFloat(this.totalFee);

				// 添加空值检查 - 确保 orderImageFiles 是数组
				const files = this.orderImageFiles || [];
				const urls = files.map(fileName => this.$imgUrl + '/uploadPath/' + fileName).join(',');
				this.form.orderImageUrls = urls;

				console.log('订单数据', this.form);
				console.log('最终提交的图片路径：', this.form.orderImageUrls);

				try {
					const res = await this.$u.api.dbladdRequest(this.form);
					if (res.code === 200) {
						uni.showToast({
							title: '提交成功',
							icon: 'success'
						});
						setTimeout(() => {
							uni.navigateBack(-1);
						}, 1000);
					} else {
						uni.showToast({
							title: res.message || '提交失败',
							icon: 'none'
						});
					}
				} catch (err) {
					console.error('请求异常', err);
					uni.showToast({
						title: '网络异常，请稍后再试',
						icon: 'none'
					});
				}
			}
		}
	}
</script>

<style lang="scss" scoped>
	.page {
		display: flex;
		flex-direction: column;
		height: 100%;
		background: #f5f5f5;
	}

	/* 头部 */
	.navbar {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		height: 44px;
		background: #fff;
		z-index: 10;
	}

	/* 主体，下移100px */
	.content {
		margin-top: 110px;
		flex: 1;
	}

	.section {
		margin: 10px;
		background: #fff;
		border-radius: 6px;
		margin-bottom: 12px;
		overflow: hidden;
	}

	.row {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 12px 16px;
		border-bottom: 1px solid #eee;
	}

	.row:last-child {
		border-bottom: none;
	}

	.readonly .label {
		font-weight: bold;
	}

	.icon {
		margin-right: 8px;
		font-size: 18px;
	}

	.label {
		// width: 100px;
		font-size: 14px;
		color: #333;
	}

	.time-picker-row {
		display: flex;
		justify-content: space-between;
		padding: 10rpx 20rpx;
		border-bottom: 1px solid #eee;
	}


	.value {
		flex: 1;
		font-size: 14px;
		color: #666;
	}

	.input {
		flex: 1;
		text-align: right;
		padding: 6px 8px;
		background: #f9f9f9;
		border-radius: 4px;
	}

	.details {
		width: 90%;
		min-height: 80rpx;
		margin: 0 auto;
		padding: 8px;
		background: #f9f9f9;
		border-radius: 4px;
		display: block;
		// font-size: 14px;
		// line-height: 20px;
	}

	.uploader {
		padding-bottom: 12px;
	}

	.stepper {
		display: flex;
		align-items: center;
		border: 1px solid #ddd;
		border-radius: 4px;
		overflow: hidden;
	}

	.btn {
		width: 28px;
		height: 28px;
		line-height: 28px;
		text-align: center;
		font-size: 18px;
		color: #333;
	}

	.btn.disabled {
		color: #ccc;
	}

	.num {
		width: 32px;
		text-align: center;
	}

	.suffix {
		margin-left: 8px;
		color: #333;
	}

	.link {
		cursor: pointer;
	}

	.icon-clsaa {
		width: 22px;
		height: 22px;
	}

	.arrow {
		margin-left: 4px;
		color: #ccc;
	}

	.price {
		color: #ff4a4a;
		font-weight: bold;
	}

	.note-row {
		justify-content: center;
		padding: 8px 16px;
	}

	.note {
		font-size: 12px;
		color: #999;
	}

	.popup-list {
		background: #fff;
	}

	.popup-item {
		padding: 14px 16px;
		border-bottom: 1px solid #eee;
		font-size: 16px;
		color: #333;
	}

	.popup-item:last-child {
		border-bottom: none;
	}

	.dt-picker {
		background: #fff;
		padding: 16px;
	}

	.picker-title {
		margin-top: 12px;
		font-size: 14px;
		color: #333;
		text-align: center;
	}

	.time-range-picker {
		display: flex;
		flex-direction: row;
		align-items: center;
		justify-content: center;
		margin: 8px 0;
	}

	.to {
		margin: 0 8px;
		font-size: 18px;
		color: #333;
	}

	.picker-item {
		padding: 12px 0;
		text-align: center;
		font-size: 16px;
		border-bottom: 1px solid #eee;
	}

	.dt-confirm {
		margin-top: 16px;
		width: 100%;
	}

	.footer {
		position: fixed;
		bottom: 0;
		left: 0;
		right: 0;
		height: 50px;
		background: #fff;
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 0 16px;
		border-top: 1px solid #eee;
	}

	.footer-text {
		font-size: 16px;
	}

	.footer-btn {
		// font-size: 28px;
		height: 36px;
		line-height: 36px;
		// padding: 0 24px;
		border-radius: 30px;
		font-weight: 600;
	}

	::v-deep .u-size-default {
		font-size: 36rpx !important;
		border-radius: 30px;
	}

	.note-style {
		// padding: 30px 10px 15px 10px;
		margin: 40px auto;
	}

	::v-deep .uni-date {
		margin: 30px auto;
	}
</style>