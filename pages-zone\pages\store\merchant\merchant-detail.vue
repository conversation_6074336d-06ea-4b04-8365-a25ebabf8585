<template>
	<view class="shop-merchant">
		<navbar :title="title"></navbar>
		<merchant-swiper/>
		<merchant-list :id="id" :search="false" :shopList="shopList"/>
	</view>
</template>

<script>
	import MerchantList from './components/merchant-list.vue';
	import MerchantSwiper from './components/merchant-swiper.vue';
	import NavbarCity from '@/components/navbar/navbar-city.vue';
	export default {
		data(){
			return{
				shopList:[],
				title:'',
			}
		},
		components:{
			NavbarCity,
			MerchantList,
			MerchantSwiper
		},
		onLoad(options) {
			this.title = options.title
			this.$u.api.getShopAndStoreByCategory(options.id,0).then(res=>{
				this.shopList = res.data.list
			})
		},
	}
</script>

<style scoped lang="scss">
.shop-merchant{
	padding: 30rpx;
	
	.shopCategoryList{
		background: #fff;
		margin-top: 30rpx;
		border-radius: 20rpx;
		padding: 30rpx;
		display: grid;
		grid-template-columns: 1fr 1fr 1fr 1fr;
		grid-gap: 30rpx;
		grid-column-gap: 40rpx;
		box-shadow: 0 2px 2px 1px #eee;
		
		.shopCategoryList-item{
			display: flex;
			flex-direction: column;
			align-items: center;
					
			.img{
				border-radius: 50% 50% 50% 0;
				overflow: hidden;
				background: #e6f2ff;
				height: 130rpx;
				width: 100%;
				
				img{
					width: 100%;
					height: 160rpx;
				}
			}
		}
	}
	
	.title{
		font-size: 54rpx;
		font-weight: 700;
		margin-top: 20rpx;
	}
	
	.search{
		margin-top: 20rpx;
		font-size: 36rpx;
		
		.text{
			margin-right: 40rpx;
		
			&.active{
				color: #f39800;
			}
		}
	}

	.shop-list{
		.shop-list-item{
			background: #fff;
			margin: 20rpx 0;
			border-radius: 30rpx;
			padding: 28rpx;
			display: grid;
			grid-template-columns: 30% 70%;
			
			.img{
				width: 100%;
				height: 200rpx;	
			}
			
			.detail{
				padding: 12rpx;
				
				.name{
					font-size: 40rpx;
					font-weight: 700;
					margin-bottom: 20rpx;
				}
				
				.star{
					margin-bottom: 20rpx;
					
					.star{
						color: #f4780d;
						font-weight: 700;
					}
				}
				
			}
		}
	}
}
</style>
