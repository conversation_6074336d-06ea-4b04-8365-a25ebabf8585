<template>
	<view class="shop-detial">
		<!-- <navbar></navbar> -->
		<Navbar title="商品"></Navbar>
		<shop-detail-top :shop="shop"></shop-detail-top>
		<shop-detail-main :shop="shop"></shop-detail-main>
		<shop-detail-button @submit='submit' buttonTitle="去预约" :shop="shop"></shop-detail-button>
		<!-- <shop-detail-button @submit='submit' buttonTitle="去结算" :shop="shop"></shop-detail-button> -->
	</view>
</template>

<script>
	import ShopDetailTop from './components/shop-top.vue';
	import ShopDetailMain from './components/shop-main.vue';
	import ShopDetailButton from './components/shop-button.vue';
	export default {
		data() {
			return {
				shop: {
					// id: 1,
					// name: '7天超市',
					// address: '福建省上街镇学府南路16-1号',
					// star: 5.00,
					// qiSong: 30,
					// peiSong: 1,
					// sellCount: 136,
					// contacts:'',
				},
			}
		},
		components: {
			ShopDetailTop,
			ShopDetailMain,
			ShopDetailButton
		},
		onLoad({
			id
		}) {
			this.$u.api.getShopDetail(id).then(res => {
				this.shop = res.data
				this.$store.commit('shop/SET_SHOP', this.shop);
			})
		},
		onShow() {
			this.getCartCount()
		},
		methods: {
			submit() {
				uni.navigateTo({
					url: '/pages-zone/pages/store/cart/cart'
				})
			},
			getCartCount() {
				if (this.$store.state.user.token) {
					this.$u.api.getCartCount().then(res => {
						this.$store.commit('cart/SET_COUNT', res.data)
					})
				}

			},
		},
	}
</script>

<style>

</style>