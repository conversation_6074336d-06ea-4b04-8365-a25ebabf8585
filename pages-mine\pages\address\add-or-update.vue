<template>
	<view class="page">
		<Navbar :title="title"></Navbar>
		<view class="form">
			<u-form :model="form" ref="formRef" :rules="rules" label-width="180rpx">
				<u-form-item label="收货人" prop="consignee" required><u-input v-model="form.consignee" placeholder="请输入收货人姓名" /></u-form-item>
				<u-form-item label="性别"> 
					<u-radio-group v-model="form.sex" active-color="#f2ca8a">
						<u-radio name="1">先生</u-radio>
						<u-radio name="0">女士</u-radio>
					</u-radio-group>
				</u-form-item>
				<u-form-item label="联系方式" prop="mobile" required><u-input v-model="form.mobile" placeholder="请输入联系方式" /></u-form-item>
				<u-form-item label="所在地区" prop="region" required><u-input v-model="cityPickerLabel" type="select" placeholder="请选择所在地区" @click="showCityPicker = true" /></u-form-item>
				<u-form-item label="详细地址" prop="address" required><u-input v-model="form.address" placeholder="请输入详细地址" /></u-form-item>
				<u-form-item :border-bottom="false" label="设为默认地址"><u-switch slot="right" v-model="form.isMian" :active-color="appThemeColor"></u-switch></u-form-item>
			</u-form>
		</view>
		<view class="btn">
			<u-button type="primary" shape="circle" @click="submit">
				<text>保存</text>
			</u-button>
		</view>
		<!-- 省市区选择器 -->
		<CityPicker :defaultRegion="defaultRegion" v-model="showCityPicker" @city-change="cityChange"></CityPicker>
	</view>
</template>

<script>
import CityPicker from '@/pages-mine/components/city-picker.vue';
export default {
	components: {
		CityPicker
	},
	data() {
		return {
			// 标题
			title: '收货地址',
			appThemeColor: this.$appTheme.appThemeColor,
			// 表单
			form: {
				isMian: false,
				consignee:"",
				sex:1,
				mobile:'',
				address:'',
			},
			defaultRegion:[],
			rules:{
				consignee: [{
					required: true,
					message: '请输入收货人',
					trigger: ['blur', 'change']
				}],
				mobile: [{
					required: true,
					message: '请输入收货人',
					trigger: ['blur', 'change']
				}],
				address: [{
					required: true,
					message: '请输入收货人',
					trigger: ['blur', 'change']
				}],
			},
			// 省市区
			showCityPicker: false,
			cityPickerLabel: ''
		};
	},
	onLoad(ops) {
		if (ops.type == 'add') {
			this.title = '新建地址';
		} else if (ops.type == 'update') {
			this.title = '修改地址';
		}
		if(ops.id){
			this.$u.api.detailAddress(ops.id).then(res=>{
				this.form = res.data
				this.cityPickerLabel = res.data.region
				let e = res.data.region.split('-').slice(1)
				this.defaultRegion = e
			})
		}
	},
	methods: {
		// 省市区选择回调
		cityChange(e) {
			this.cityPickerLabel = '上虞区-' +  e.province.label + '-' + e.city.label;
			// this.form.province = e.province.label;
			// this.form.city = e.city.label;
			// this.form.area = e.area.label;
			this.form.region = this.cityPickerLabel
		},
		// 提交表单
		submit() {
			this.$refs.formRef.validate(valid => {
				if(valid){
					if(!this.form.address || !this.form.mobile || !this.form.consignee || !this.form.region){
						uni.$u.toast('信息未填写全')
						return
					}
					this.form.userId = this.$store.state.user.userInfo.userId
					this.form.isMian = this.form.isMian ? 1 : 0
					if(this.title == '新建地址'){
						this.$u.api.addAddress(this.form).then(res=>{
							uni.navigateBack()
						})
					}else if (this.title == '修改地址'){
						this.$u.api.updateAddress(this.form).then(res=>{
							uni.navigateBack()
						})
					}
				}
			})
		}
	}
};
</script>

<style lang="scss" scoped>
.form {
	background-color: $app-theme-bg-color;
	padding: 0 30rpx;
}
.btn {
	padding: 60rpx 30rpx;
	
	& ::v-deep .u-btn{
		background: #f2cb8a;
	}
	
	& ::v-deep .u-primary-hover{
		background: #f2cb8a !important;
	}
}
</style>
