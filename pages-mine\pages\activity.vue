<template>
	<view class="slot">
		<Navbar title="参与活动"></Navbar>
		<PostCard v-for="(item, index) in circleList" :key="index" :data="item"
			:border="index != circleList.length - 1"></PostCard>
	</view>
</template>

<script>
	import PostCard from '@/pages/community/components/post-card';
	export default {
		components: {
			PostCard
		},
		data() {
			return {
				circleList: []
			};
		},
		onShow() {
			this.$u.api.getActivityPosts({
				userId: this.$store.state.user.userInfo.userId
			}).then(r => {
				this.circleList = r.data
			})
		}
	};
</script>

<style lang="scss" scoped>
	.hot-nav {
		padding: 0rpx 0rpx 30rpx 0rpx;
		background-color: $app-theme-bg-color;
	}
</style>