<template>
	<view class="page">
		<!-- 导航栏 -->
		<Navbar title="个人中心"></Navbar>

		<!-- 表单 -->
		<view class="form">
			<u-form label-position="left" :model="formData" ref="formRef" label-width="160rpx">
				<!-- 头像上传 -->
				<u-form-item label="头像">
					<view class="avatar-wrap" @click="handleUploadAvatar" v-if="!isAvatarChecking">
						<u-avatar size="120" :src="avatarUrl" :loading="isAvatarLoading"></u-avatar>
					</view>
					<view class="checking-wrap" v-if="isAvatarChecking">
						<u-loading mode="circle" size="24"></u-loading>
						<text class="checking-text">安全检测中...</text>
					</view>
				</u-form-item>

				<!-- 昵称 -->
				<u-form-item label="昵称" :required="true">
					<u-input v-model="formData.nickName" placeholder="请输入昵称" />
				</u-form-item>

				<!-- 手机号码 -->
				<u-form-item label="手机号码" :required="true">
					<u-input v-model="formData.phonenumber" placeholder="请输入手机号码" type="number" />
				</u-form-item>

				<!-- 邮箱 -->
				<u-form-item label="邮箱">
					<u-input v-model="formData.email" placeholder="请输入邮箱" />
				</u-form-item>

				<!-- 性别 -->
				<u-form-item label="性别">
					<u-radio-group v-model="formData.sex">
						<u-radio v-for="(item, index) in sexOptions" :key="index" :name="item.value"
							:style="{ marginRight: '40rpx' }">
							{{ item.label }}
						</u-radio>
					</u-radio-group>
				</u-form-item>
			</u-form>
		</view>

		<!-- 操作按钮 -->
		<view class="btn-group">
			<u-button type="success" shape="circle" @click="handleModify" :loading="isSubmitting">
				修改
			</u-button>
			<view style="margin: 40rpx 0;"></view>
			<u-button type="warning" shape="circle" @click="handleLogout">
				退出登录
			</u-button>
		</view>
	</view>
</template>

<script>
	import {
		HTTP_URL_PROD
	} from '@/api/config';

	export default {
		data() {
			return {
				// 表单数据
				formData: {
					nickName: '',
					phonenumber: '',
					email: '',
					sex: '0' // 0-男，1-女
				},

				// 头像相关
				avatarUrl: '', // 头像地址
				isAvatarLoading: false, // 头像上传加载状态
				isAvatarChecking: false, // 头像安全检测状态

				// 提交状态
				isSubmitting: false,

				// 性别选项
				sexOptions: [{
						label: '男',
						value: '0'
					},
					{
						label: '女',
						value: '1'
					}
				],

				// 表单验证规则
				formRules: {
					nickName: [{
							required: true,
							message: '请输入昵称',
							trigger: 'blur'
						},
						{
							min: 2,
							max: 10,
							message: '昵称长度在2-10个字符之间',
							trigger: 'blur'
						}
					],
					phonenumber: [{
							required: true,
							message: '请输入手机号码',
							trigger: 'blur'
						},
						{
							pattern: /^1[3-9]\d{9}$/,
							message: '请输入正确的手机号码',
							trigger: 'blur'
						}
					],
					email: [{
						pattern: /^\S+@\S+\.\S+$/,
						message: '请输入正确的邮箱格式',
						trigger: 'blur'
					}]
				}
			};
		},

		computed: {
			// 从store获取用户信息
			userInfo() {
				return this.$store.state.user.userInfo || {};
			},
			// 获取token
			token() {
				return uni.getStorageSync("App-Token") || '';
			}
		},

		onLoad() {
			this.initUserInfo(); // 初始化用户信息
		},

		methods: {
			/** 初始化用户信息 */
			initUserInfo() {
				this.formData = {
					nickName: this.userInfo.nickName || '',
					phonenumber: this.userInfo.phonenumber || '',
					email: this.userInfo.email || '',
					sex: this.userInfo.sex?.toString() || '0' // 确保是字符串类型
				};
				// 处理头像地址（容错处理）
				this.avatarUrl = this.userInfo.avatar ?
					(this.userInfo.avatar.startsWith('http') ? this.userInfo.avatar : HTTP_URL_PROD + this.userInfo
						.avatar) :
					'';
			},

			/** 处理头像上传（含安全检测） */
			handleUploadAvatar() {
				// 1. 选择图片
				uni.chooseImage({
					count: 1,
					sizeType: ['compressed'], // 压缩图片
					sourceType: ['album', 'camera'],
					success: (res) => {
						const tempFilePath = res.tempFilePaths[0];
						this.startAvatarSecurityCheck(tempFilePath); // 开始安全检测
					},
					fail: () => {
						uni.showToast({
							title: '选择图片失败',
							icon: 'none'
						});
					}
				});
			},

			/** 头像安全检测（核心） */
			startAvatarSecurityCheck(tempFilePath) {
				// this.isAvatarChecking = true;
				this.uploadAvatarToServer(tempFilePath);
				// 2. 将图片转为base64（用于安全检测）
				// uni.getFileSystemManager().readFile({
				// 	filePath: tempFilePath,
				// 	encoding: 'base64',
				// 	success: (base64Res) => {
				// 		const base64Data = base64Res.data;

				// 		// 3. 调用后端安全检测接口
				// 		this.$u.api.checkAvatarSecurity({
				// 			content: base64Data // 传递base64内容
				// 		}).then(securityRes => {
				// 			this.isAvatarChecking = false;

				// 			if (securityRes.code === 200 && securityRes.data.safe) {
				// 				// 检测通过，上传头像到服务器
				// 				this.uploadAvatarToServer(tempFilePath);
				// 			} else {
				// 				// 检测违规
				// 				uni.showToast({
				// 					title: '所发布内容含违规信息',
				// 					icon: 'none'
				// 				});
				// 			}
				// 		}).catch(() => {
				// 			this.isAvatarChecking = false;
				// 			uni.showToast({
				// 				title: '安全检测失败，请重试',
				// 				icon: 'none'
				// 			});
				// 		});
				// 	},
				// 	fail: () => {
				// 		this.isAvatarChecking = false;
				// 		uni.showToast({
				// 			title: '图片处理失败',
				// 			icon: 'none'
				// 		});
				// 	}
				// });
			},

			/** 上传头像到服务器 */
			uploadAvatarToServer(tempFilePath) {
				this.isAvatarLoading = true;

				uni.uploadFile({
					url: `${HTTP_URL_PROD}/system/user/profile/avatar`,
					filePath: tempFilePath,
					name: 'avatarfile',
					header: {
						Authorization: this.token
					},
					success: (uploadRes) => {
						this.isAvatarLoading = false;
						const data = uploadRes.data ? JSON.parse(uploadRes.data) : {};

						if (data.code === 200 && data.imgUrl) {
							// 上传成功，更新头像并刷新用户信息
							this.avatarUrl = HTTP_URL_PROD + data.imgUrl;
							this.$store.dispatch('user/GetInfo').then(() => {
								uni.showToast({
									title: '头像更新成功',
									icon: 'success'
								});
								uni.$emit('updateInfo', {
									msg: '头像已更新'
								});
							});
						} else {
							uni.showToast({
								title: '头像上传失败',
								icon: 'none'
							});
						}
					},
					fail: () => {
						this.isAvatarLoading = false;
						uni.showToast({
							title: '上传失败，请重试',
							icon: 'none'
						});
					}
				});
			},

			/** 提交修改 */
			handleModify() {
				// 1. 手动校验表单
				const {
					nickName,
					phonenumber,
					email
				} = this.formData;

				// 校验昵称
				if (!nickName.trim()) {
					this.$u.toast('请输入昵称');
					return;
				}
				if (nickName.length < 2 || nickName.length > 10) {
					this.$u.toast('昵称长度需在2-10个字符之间');
					return;
				}

				// 校验手机号
				if (!phonenumber.trim()) {
					this.$u.toast('请输入手机号码');
					return;
				}
				const phoneReg = /^1[3-9]\d{9}$/;
				if (!phoneReg.test(phonenumber)) {
					this.$u.toast('请输入正确的手机号码');
					return;
				}

				// 校验邮箱（非必填，有值才校验）
				if (email.trim() && !/\S+@\S+\.\S+/.test(email)) {
					this.$u.toast('请输入正确的邮箱格式');
					return;
				}

				// 2. 所有校验通过，调用接口
				this.isSubmitting = true;
				this.$u.api.putUserModify(this.formData)
					.then(res => {
						this.isSubmitting = false;
						if (res.code === 200) {
							this.$store.dispatch('user/GetInfo').then(() => {
								this.$u.toast('修改成功');
								uni.$emit('updateInfo', {
									msg: '个人信息已更新'
								});
								setTimeout(() => uni.navigateBack(), 1500);
							});
						} else {
							this.$u.toast(res.msg || '修改失败');
						}
					})
					.catch(() => {
						this.isSubmitting = false;
						this.$u.toast('网络错误，请重试');
					});
			},

			/** 退出登录 */
			handleLogout() {
				uni.showModal({
					title: '提示',
					content: '确定要退出登录吗？',
					confirmColor: this.$appTheme.appThemeColor,
					success: (res) => {
						if (res.confirm) {
							this.$store.dispatch('user/LogOut').then(() => {
								uni.navigateBack({
									delta: 1
								});
								uni.$emit('order-status-changed', {
									orderId: 0,
									newStatus: 0
								});
							});
						}
					}
				});
			}
		}
	};
</script>

<style lang="scss" scoped>
	.page {
		padding: 24rpx 30rpx;
		background-color: $app-theme-bg-color;

		.checking-wrap {
			display: flex;
			align-items: center;
			justify-content: flex-end;
			padding: 40rpx 0;

			.checking-text {
				margin-left: 10rpx;
				font-size: 28rpx;
				color: #666;
			}
		}


		.form {
			background-color: #fff;
			border-radius: 16rpx;
			padding: 30rpx 0;
			margin-bottom: 40rpx;
		}

		.avatar-wrap {
			display: flex;
			align-items: center;
			justify-content: flex-end;
		}
	}

	.btn-group {
		padding: 60rpx 30rpx;
	}
</style>