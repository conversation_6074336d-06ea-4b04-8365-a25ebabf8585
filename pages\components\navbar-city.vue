<template>
	<view class="slot">
		<view class="navbar" :style="[{ minHeight: navBarHeight + 'px' }]">
			<view class="inner" :style="[
				{ minHeight: menuHeight + 'px' },
				{ lineHeight: menuHeight + 'px' },
				{ paddingLeft: menuRight * 2 + 'px' },
				{ paddingRight: menuRight * 2 + 'px' },
				{ paddingTop: navBarHeight - menuHeight - menuTop + 'px' },
				{ paddingBottom: '20rpx' }
			]">
				<u-icon v-if="isBack" @click="goBack" :size="40" color="#171717" name="arrow-left"></u-icon>
				<view class="loaction-slot">
					<view class="loaction-title" @click="goCitySelectPage">
						<u-icon style="margin-right: 8rpx;" :size="32" color="#171717" name="map-fill"></u-icon>
						<text>{{ cityListSelected }}</text>
					</view>
					<view v-if="title" class="title">{{ title }}</view>
				</view>
			</view>
		</view>
		<view class="slot-height" :style="[{ height: navBarHeight + 11 + 'px' }]"></view>
	</view>
</template>

<script>
	const app = getApp();
	import UIcon from '@/pages-zone/uview-ui/components/u-icon/u-icon.vue'
	export default {
		name: 'navbar-city',
		components: {
			UIcon
		},
		props: {
			// 标题
			title: {
				type: String,
				default: ''
			},
			// 占位内容
			placeholder: {
				type: String,
				default: '请输入'
			},
			// 已经选择的城市
			cityListSelected: {
				type: String,
				default: ''
			},
			isFind: {
				type: Boolean,
				default: false
			},
			isBack: {
				type: Boolean,
				default: false
			}
		},
		data() {
			return {
				// 导航栏高度
				menuTop: app.globalData.menuTop,
				navBarHeight: app.globalData.navBarHeight,
				menuRight: app.globalData.menuRight,
				menuBotton: app.globalData.menuBotton,
				menuHeight: app.globalData.menuHeight,
				statusBarHeight: app.globalData.statusBarHeight,
				// 定位数据
				locationData: {},
				// 当前城市
				locationCity: '',
				// 获取地理位置状态，1正在获取，2获取成功，3失败
				locationStatus: 1
			};
		},
		mounted() {},
		methods: {
			// 跳转城市选择页面
			goCitySelectPage() {
				uni.navigateTo({
					url: '/pages/home/<USER>'
				});
			},

			// 去搜索页面
			goSearchPage() {
				uni.navigateTo({
					url: '/pages/search'
				});
			},
			// 回退
			goBack() {
				uni.navigateBack()
			}
		}
	};
</script>

<style lang="scss" scoped>
	.slot {
		width: 100vw;
	}

	.navbar {
		width: 100%;
		position: fixed;
		top: 0;
		left: 0;
		z-index: 899;
		overflow: hidden;
	}

	.inner {
		width: 100%;
		height: 100%;
		background: linear-gradient(to bottom, #ffd9a4, #ffdcac);
		display: flex;
		flex-direction: row;
		align-items: center;
	}

	.loaction-slot {
		display: flex;
		align-items: center;
		position: relative;
		/* 确保子元素绝对定位时相对于父容器 */
		width: 100%;
	}

	.loaction-title {
		position: absolute;
		left: 0;
		/* 靠左对齐 */
		display: flex;
		align-items: center;

		text {
			font-size: 28rpx;
			font-family: PingFang SC;
			font-weight: 400;
			color: $app-theme-text-black-color;
		}
	}

	.title {
		margin: 0 auto;
		/* 水平居中 */
		font-size: 32rpx;
		font-weight: bold;
		color: $app-theme-text-black-color;
	}

	.search-slot {}
</style>