export const useJGQApi = (Vue, vm) => {
	return {
		// 金刚区
		getJGQlistData: (query) => vm.$u.get('/store/shopCls/list', query),
		// 列表
		getlistgoods: (query) => vm.$u.get('/communityService/communityService/list', query),
		// 详情
		getlistindetail: (id) => vm.$u.get(`/communityService/communityService/get/${id}`),
		// 订单评价
		getevaluatelist: (query) => vm.$u.get('/communityService/serviceOrderEvaluate/list', query),
		// 创建订单
		posorder: (data) => vm.$u.post('/communityService/technicianInformationApply', data),
		// 生成订单
		posyOrderGenerated: (data) => vm.$u.post('/communityService/serviceOrder', data),
		// 取消订单
		postcancellationoforder: (id) => vm.$u.post('/communityService/serviceOrder/close?id=' + id),
		// 获取支付签名
		postyrequestPayment: (data) => vm.$u.post('/system/wechat/pay/createPrepay', data),
		// 订单支付
		posyPayment: (id) => vm.$u.post('/communityService/serviceOrder/pay?id=' + id),
		// 订单列表
		getorderlist: (query) => vm.$u.get('/communityService/serviceOrder/list', query),
		// 商家入住
		postMerchantcheckin: (data) => vm.$u.post('/communityService/technicianInformationApply', data),
		// 意向行业
		getIntendedIndustry: (id) => vm.$u.get(`/store/shopCls/list?pageNum=1&pageSize=999&type=2`),
		//用户评价
		postevaluate: (data) => vm.$u.post('/communityService/serviceOrder/userEvaluate', data),
		//用户确认
		postconfirm: (id) => vm.$u.post('/communityService/serviceOrder/userConfirmation?id=' + id),
		// 分配技师
		postconfirmation: (id) => vm.$u.post('/communityService/serviceOrder/assignTechnicians?id=' + id),
		// 删除订单
		deleteOrder: (id) => vm.$u.delete('/communityService/serviceOrder/' + id),
		// 个人信息修改
		putUserModify: (data) => vm.$u.put('/system/user/profile', data),
	}
}