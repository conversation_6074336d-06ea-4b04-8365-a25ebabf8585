<template>
	<view>
		<home v-show="curType=='home'" :curPage="0"></home>
		<find v-show="curType=='find'" :curPage="1"></find>
		<neighbor v-show="curType=='neighbor'" :curPage="2"></neighbor>
		<user v-show="curType=='user'" :curPage="3"></user>
		<view class="tabbar" :style="{'padding-bottom': paddingBottomHeight + 'upx'}">
			<view class="tabbar-operate-bg" :class="isPost?'show':''" @tap="isPost=false"></view>
			<!-- <view class="tabbar-operate" :class="isPost?'show':''"
				:style="{'padding-bottom': paddingBottomHeight + 'upx'}">
				<view class="tabbar-operate-main grid col-3">
					<view class="index-sort-box">
						<view class="index-sort-main" @tap="toLink('/pages-community/pages/post/add')">
							<view class="index-sort-i" style="background: rgba(30, 134, 231, 0.4);">
								<u-icon name="edit-pen-fill" color="#1e86e7;"></u-icon>
							</view>
							<view class="index-sort-text">
								发布动态
							</view>
						</view>
					</view>
					<view class="index-sort-box">
						<view class="index-sort-main" @tap="toLink('/pages-points/lingong/daibantoeight?serviceType=101')">
							<view class="index-sort-i" style="background: rgba(21, 159, 44, 0.4);">
								<u-icon name="bell-fill" color="#159f2c;"></u-icon>
							</view>
							<view class="index-sort-text">
								需要帮忙
							</view>
						</view>
					</view>
					<view class="index-sort-box">
						<view class="index-sort-main" @tap="toLink('/pages-community/pages/post/addActivity')">
							<view class="index-sort-i" style="background: rgba(255, 0, 127, 0.4);">
								<u-icon name="level" color="#ff007f;"></u-icon>
							</view>
							<view class="index-sort-text">
								组织活动
							</view>
						</view>
					</view>
				</view>
			</view> -->
			<block v-for="(item, index) in list" :key="index">
				<view class="tabbar-item" @tap="tabbarChange(item.type,index)">
					<image class="item-img" :src="item.icon_a+'.png'" v-if="curType == item.type"></image>
					<image class="item-img" :src="item.icon" v-else></image>
					<view class="item-name" :class="{'tabbarActive': curType == item.type}" v-if="item.text">
						{{item.text}}
					</view>
				</view>
				<!-- <view class="tabbar-item addPost" v-if="index==1" @tap="isPost=!isPost" :class="isPost?'postShow':''">
					<view class="addPost-main">
						<view class="addPost-i">
							<u-icon name="plus"></u-icon>
						</view>
					</view>
				</view> -->
			</block>
		</view>
	</view>

</template>

<script>
	import {
		localStorage
	} from '@/js_sdk/mp-storage/mp-storage/index.js'
	export default {
		data() {
			return {
				isPost: false,
				paddingBottomHeight: 0, //苹果X以上手机底部适配高度
				list: [{
					text: '首页',
					icon: '/static/tabbar/home.png', //未选中图标1
					icon_a: '/static/tabbar/home_active', //选中图片
					type: "home",
				}, 
				// {
				// 	text: '社区',
				// 	icon: '/static/tabbar/community.png',
				// 	icon_a: '/static/tabbar/community_active',
				// 	type: "find",
				// }, 
				{
					text: '邻工',
					icon: '/static/tabbar/mall.png',
					icon_a: '/static/tabbar/mall_active',
					type: 'neighbor',
				}, {
					text: '我的',
					icon: '/static/tabbar/user.png',
					icon_a: '/static/tabbar/user_active',
					type: "user",
				}, ],
				curType: "home",
				curStyle: "#222222",
				curPage: 0,
				pageShow: false,
			};
		},
		onHide() {
			var that = this;
			that.pageShow = false;
		},
		onUnload() {
			var that = this;
			that.pageShow = false;
		},

		onPullDownRefresh() {
			var that = this;
			uni.$emit('onPullDownRefresh', that.curPage);
			var timer = setTimeout(function() {
				uni.stopPullDownRefresh();
			}, 1000);
		},
		onShow() {
			var that = this;
			that.pageShow = true;
			setTimeout(function() {
				uni.$emit('onShow', that.curPage);
			}, 200);
		},
		onReachBottom() {
			var that = this;
			uni.$emit('onReachBottom', that.curPage);
		},
		onLoad() {
			let that = this;
			that.pageShow = true;
			let platform = uni.getSystemInfoSync().platform;
			localStorage.setItem('app_platform', platform);
			uni.getSystemInfo({
				success: function(res) {
					let model = ['X', 'XR', 'XS', '11', '12', '13', '14', '15'];
					console.log("当前设备型号：" + res.model)
					model.forEach(item => {
						if (res.model.indexOf(item) != -1 && res.model.indexOf('iPhone') != -1) {
							that.paddingBottomHeight = 40;
						}
					})
				}
			});
			// uni.$on('goUser', function(data) {
			// 	that.tabbarChange("user", 3)
			// });
		},
		onReady() {
			setTimeout(function() {
				uni.$emit('onShow', 0);
			}, 200);
		},
		methods: {
			tabbarChange(type, index) {
				var that = this;
				that.isPost = false;
				that.curPage = index;
				that.curType = type;
				setTimeout(function() {
					uni.$emit('onShow', index);
				}, 200);

			},
			toLink(text) {
				var that = this;
				that.isPost = false;
				uni.navigateTo({
					url: text
				});
			},
		}
	};
</script>

<style scoped>


</style>