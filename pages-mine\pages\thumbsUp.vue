<template>
	<view class="page">
		<Navbar title="我的点赞"></Navbar>
		<!-- <u-tabs :list="list" :is-scroll="false" :current="current" @change="change"></u-tabs> -->
		<PostCard v-for="(item, index) in circleList" :key="index" :data="item"
			:border="index != circleList.length - 1"></PostCard>
	</view>
</template>

<script>
	import PostCard from '@/pages/community/components/post-card';
	export default {
		components: {
			PostCard
		},
		data() {
			return {
				list: [{
					name: '活动'
				}, {
					name: '帖子'
				}],
				current: 0,
				// userList,
				circleList: []
			};
		},
		onShow() {
			this.$u.api.myLikedPosts({
				userId: this.$store.state.user.userInfo.userId
			}).then(r => {
				this.circleList = r.data
			})
		},
		methods: {
			change(index) {
				this.current = index;
			},
		},
	}
</script>

<style lang="scss" scoped>
	.page {
		background-color: $app-theme-bg-color;
	}
</style>