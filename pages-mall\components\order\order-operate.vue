<template>
	<view class="slot">
		<view class="inner">
			<view class="price">
				<text>￥</text>
				<text>{{ data.price }}</text>
			</view>
			<view class="right">
				<text @click="orshow=true" class="textqx" v-if="data.status == '1'">取消订单</text>
				<u-button shape="circle" size="small" type="primary" @click="goPay1"
					v-if="data.status == '1'">立即付款</u-button>
				<u-button shape="circle" size="small" type="primary" @click="pushDelivery"
					v-if="data.status == '2'">提醒发货</u-button>
				<!-- <u-button shape="circle" size="small" type="primary" @click="confirmReceipt" v-if="data.status == '3'">确认收货</u-button> -->
			</view>
		</view>
		<u-modal v-model="orshow" :show-cancel-button="true" :content="content" @confirm="confirm"></u-modal>
		<u-toast ref="uToast" />
	</view>
</template>

<script>
	import {
		orderOperate
	} from '@/pages-mall/mixins/order-operate.js';
	export default {
		// 工单操作方法混入
		mixins: [orderOperate],
		props: {
			// 数据源
			data: {
				type: Object,
				default: () => {
					return {};
				}
			}
		},
		data() {
			return {
				orshow: false,
				content: '确认取消当前订单?'
			}
		},
		methods: {
			async goPay1() {
				try {
					this.isPayLoading = true;

					// 1. 获取微信登录code
					const code = await this.getWxCode();
					if (!code) throw new Error('获取支付凭证失败');

					// 2. 获取支付参数
					const payParams = await this.getPayParams(code, this.data.id);
					// 3. 发起微信支付
					const payResult = await this.requestPayment(payParams);

					// 4. 支付成功处理
					if (payResult.errMsg === 'requestPayment:ok') {
						this.$refs.uToast.show({
							title: '支付成功',
							type: 'success'
						});
						this.$emit('change', 1); // 通知父组件更新列表
						setTimeout(() => this.goOrderInfo1(), 1500);
					}

				} catch (err) {
					// 忽略用户主动取消的情况
					if (!err.message.includes('取消')) {
						this.$refs.uToast.show({
							title: err.message || '支付失败',
							type: 'error'
						});
					}
				} finally {
					this.isPayLoading = false;
				}
			},
			/** 获取微信登录code */
			getWxCode() {
				return new Promise((resolve, reject) => {
					wx.login({
						success: (res) => {
							res.code ? resolve(res.code) : reject(new Error('获取登录凭证失败'));
						},
						fail: () => reject(new Error('微信登录接口调用失败'))
					});
				});
			},
			
			/** 获取支付参数 */
			getPayParams(code, orderId) {
				return new Promise((resolve, reject) => {
					uni.request({
						url: HTTP_URL_PROD + '/system/wechat/pay/createPrepay',
						method: 'POST',
						header: {
							'Content-Type': 'application/x-www-form-urlencoded',
							'Authorization': this.$store.state.user.token
						},
						data: {
							code,
							orderId
						},
						success: (res) => {
							if (res.data?.code === 200 && res.data?.data) {
								resolve(res.data.data);
							} else {
								reject(new Error(res.data?.msg || '获取支付参数失败'));
							}
						},
						fail: () => reject(new Error('支付接口调用失败'))
					});
				});
			},
			
			/** 调用微信支付 */
			requestPayment(payParams) {
				return new Promise((resolve, reject) => {
					wx.requestPayment({
						timeStamp: payParams.timeStamp.toString(),
						nonceStr: payParams.nonceStr,
						package: payParams.package,
						signType: payParams.signType || 'MD5',
						paySign: payParams.paySign,
						success: (res) => resolve(res),
						fail: (err) => reject(new Error(`支付失败: ${err.errMsg}`))
					});
				});
			},
			goOrderInfo1(){
				uni.navigateBack()
			},
			// 取消订单
			confirm() {
				this.$u.api.postcancellationoforder(this.data.id).then(res => {
					if (res.code === 200) {
						this.$refs.uToast.show({
							title: '已取消',
							type: 'success',
						})
						this.$emit('change', this.data.status);
					}
				});
			},
		}
	};
</script>

<style lang="scss" scoped>
	.slot {
		position: fixed;
		bottom: 0;
		left: 0;
		width: 100%;
		background-color: $app-theme-bg-color;
		min-height: 100rpx;
		box-shadow: $app-theme-shadow;
		border-top: 1px solid $app-theme-border-color;
		padding: 10rpx 0;
		padding-bottom: constant(safe-area-inset-bottom);
		padding-bottom: env(safe-area-inset-bottom);

		.inner {
			display: flex;
			justify-content: space-between;
			align-items: center;
			height: 100%;

			.price {
				padding-left: 30rpx;

				text:nth-child(1) {
					font-size: 24rpx;
					font-family: PingFangSC-Medium, PingFang SC;
					font-weight: 500;
					color: $app-theme-text-money-color;
				}

				text:nth-child(2) {
					font-size: 36rpx;
					font-family: PingFangSC-Regular, PingFang SC;
					font-weight: 400;
					color: $app-theme-text-money-color;
				}
			}

			.right {
				display: flex;
				align-items: center;
				padding-right: 30rpx;

				.textqx {
					margin-right: 32rpx;
					color: #ff4e51;
				}
			}
		}
	}
</style>