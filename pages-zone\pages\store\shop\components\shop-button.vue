<template>
	<view class="shop-detail-button">
		<view class="collect">
<!-- 			<u-icon size='44' name="star"></u-icon>
			<u-icon size='40' color="#f39800" name="star-fill"></u-icon>
			收藏 -->
		</view>
		<view @click="toCart" class="cart">
			<u-icon size='44' name="shopping-cart"></u-icon>
			购物车
			<view class="count">
				{{cartCount}}
			</view>
		</view>
		<view class="settlement">
			<u-button class='button' type="warning" shape="circle"
				@click="submit"><text>{{buttonTitle}}</text></u-button>
		</view>
	</view>
</template>

<script>
	export default {
		name: "shop-detail-button",
		data() {
			return {};
		},
		props: ['buttonTitle'],
		computed: {
			cartCount() {
				return this.$store.state.cart.count
			}
		},
		methods: {
			toCart() {
				if (this.$store.state.user.token) {
					uni.navigateTo({
						url: '/pages-zone/pages/store/cart/cart'
					})
				} else {
					this.tokenShowModal()
				}

			},
			submit() {
				if (this.$store.state.user.token) {
					this.$emit('submit')
				} else {
					this.tokenShowModal()
				}
			},
			tokenShowModal() {
				uni.showModal({
					title: "提示",
					content: '您还未登录，登陆后可体验完整服务',
					cancelText: "取消",
					confirmText: "确定",
					success: function(res) {
						if (res.confirm) {
							uni.reLaunch({
								url: "/pages/login/index",
							});
						}
					},
				});
			},
		}
	}
</script>

<style scoped lang="scss">
	.shop-detail-button {
		position: fixed;
		bottom: 0;
		width: 100%;
		background: #fff;
		height: 60px;
		padding-top: 20rpx;
		display: grid;
		grid-template-columns: 15% 15% 70%;
	}

	.collect,
	.cart {
		display: flex;
		flex-direction: column;
		align-items: center;
	}

	.cart {
		position: relative;

		.count {
			position: absolute;
			width: 30rpx;
			height: 30rpx;
			background: red;
			color: #fff;
			border-radius: 50%;
			text-align: center;
			line-height: 30rpx;
			right: 20rpx;
			top: -8rpx;
		}
	}

	.settlement {
		width: 100%;

		.button {
			background: #ffba4d;

			text {
				color: #fff;
				font-size: 40rpx
			}

			&:hover {
				background: #ffba4d;
			}
		}
	}
</style>