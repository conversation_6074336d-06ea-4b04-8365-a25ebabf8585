<template>
	<u-popup v-model="show" mask mode="bottom" mask-close-able closeable border-radius="16" safe-area-inset-bottom>
		<view class="info">
			<view class="pic"><u-image :src="https+cationsdata.picture" width="190rpx" height="190rpx"></u-image></view>
			<view class="desc">
				<view class="price">
					<text>￥</text>
					<text>{{ cationsdata.amount }}</text>
				</view>
				<view class="select">
					<text>已选</text>
					<text>{{ selectSkuLabel }}</text>
				</view>
			</view>
		</view>
		<view class="sku">
			<view class="title">规格</view>
			<view class="list">
				<view class="item" v-for="(item, index) in data.communityServiceSpecifications" :key="index"
					:class="{ select: item.id == selectSkuValue }" @click="selectSku(item, index)">
					{{ item.title }}
				</view>
			</view>
		</view>
		<view class="num">
			<view class="title">数量</view>
			<u-number-box v-model="num" @change="changeNum"></u-number-box>
		</view>
		<view class="operate">
			<u-button v-if="ishideShoppingCart" style="width: 50%;" type="dark" @click="addCartHandle"
				throttleTime='50'>加入购物车</u-button>
						<u-button :style="!ishideShoppingCart?'width: 100%;':'width: 50%;'" type="primary" @click="bugNowHandle"
				throttleTime='50'>立即购买</u-button>
		<!-- 	<u-button :style="!ishideShoppingCart?'width: 100%;':'width: 50%;'" type="primary" @click="bugNowHandle"
				throttleTime='50'>立即预约</u-button> -->
		</view>
	</u-popup>
</template>

<script>
	import {
		HTTP_URL_PROD
	} from '@/api/config';
	export default {
		data() {
			return {
				https: HTTP_URL_PROD,
				show: false,
				data: null,
				// 已选择的sku
				selectSkuLabel: '',
				selectSkuValue: '0',
				// 数量
				num: 1,
				// 隐藏购物车
				ishideShoppingCart: true,
				strishideShoppingCart: null,
				cationsdata: null,
				Address: {},
			};
		},
		methods: {
			// 打开popup
			open(data, selectSku, val, Address) {
				if (val === 'hideShoppingCart') { //隐藏购物车
					this.ishideShoppingCart = false
					this.strishideShoppingCart = val
				}
				this.Address = Address
				this.data = data
				this.cationsdata = data.communityServiceSpecifications[0]
				this.selectSkuLabel = selectSku.label;
				this.selectSkuValue = selectSku.value;
				this.show = true;
			},

			// 切换sku
			selectSku(item, index) {
				console.log(item, 'item')
				this.cationsdata = item
				this.selectSkuValue = item.id;
				this.selectSkuLabel = item.title;
				this.$emit('change', item);
			},

			// 数量改变时
			changeNum(e) {},

			// 关闭
			close() {},

			// 加入购物车
			addCartHandle() {
				uni.showToast({
					title: '添加购物车成功',
					icon: 'none'
				});
			},

			// 立即购买
			bugNowHandle() {
				if (this.$store.state.user.token) {
					uni.navigateTo({
						url: '/pages-mall/pages/order/submit?ishideShoppingCart=' + this.strishideShoppingCart +
							'&num=' + this.num + '&id=' + this.data.id + '&Address=' + encodeURIComponent(JSON
								.stringify(this.Address)) + '&coreform=' +
							encodeURIComponent(JSON.stringify(this.cationsdata))
					})
				} else {
					uni.showModal({
						title: "提示",
						content: "您还未登录，请先登录",
						cancelText: "取消",
						confirmText: "确定",
						success: function(res) {
							if (res.confirm) {
								uni.reLaunch({
									url: "/pages/login/index",
								});
							}
						},
					});
				}


			}
		}
	};
</script>

<style lang="scss" scoped>
	.info {
		padding: 30rpx 30rpx 0 30rpx;
		display: flex;
		align-items: flex-end;

		.pic {
			margin-right: 30rpx;
		}

		.desc {
			.price {
				margin-bottom: 20rpx;

				text:nth-child(1) {
					font-size: 24rpx;
					color: $app-theme-text-money-color;
					vertical-align: bottom;
				}

				text:nth-child(2) {
					font-size: 36rpx;
					color: $app-theme-text-money-color;
					vertical-align: bottom;
				}
			}

			.select {
				text:nth-child(1) {
					font-size: 24rpx;
					color: $app-theme-card-gray-deep-color;
					margin-right: 20rpx;
				}

				text:nth-child(2) {
					font-size: 24rpx;
					color: $app-theme-text-black-color;
				}
			}
		}
	}

	.sku {
		padding: 30rpx 30rpx 0 30rpx;

		.title {
			font-size: 28rpx;
			color: $app-theme-text-black-color;
			margin-bottom: 16rpx;
		}

		.list {
			display: flex;
			justify-content: flex-start;
			align-items: center;
			flex-wrap: wrap;

			.item {
				padding: 16rpx 32rpx 14rpx 32rpx;
				margin-bottom: 24rpx;
				margin-right: 20rpx;
				background-color: $app-theme-sku-gray-color;
				font-size: 24rpx;
				color: $app-theme-text-black-color;
				border: 1rpx solid $app-theme-sku-gray-color;

				&.select {
					color: $app-theme-color;
					border: 1rpx solid $app-theme-color;
					background-color: rgba($app-theme-color, 0.08);
				}
			}
		}
	}

	.num {
		padding: 30rpx;
		display: flex;
		justify-content: space-between;
		align-items: center;

		.title {
			font-size: 28rpx;
			color: $app-theme-text-black-color;
		}
	}

	.operate {
		display: flex;
		align-items: center;

		button {
			height: 100rpx;
			line-height: 100rpx;
			border-radius: 0 !important;

			&::after {
				border: initial;
			}
		}
	}
</style>