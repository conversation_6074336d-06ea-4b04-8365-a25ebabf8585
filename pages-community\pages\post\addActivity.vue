<template>
	<view class="container" style="padding-bottom: 40rpx;">
		<Navbar title="发布活动"></Navbar>
		<view class="page-body">
			<view>
				<view style="padding: 20rpx 0;">主图</view>
				<view style="background-color: #fff;">
					<img-picker @imglist='getImg' :limit="3" mediatype="all" :types='1'></img-picker>
				</view>
				<view class="address df_jub">
					<view style="width: 150rpx;">活动标题</view>
					<u-input placeholder="请填写" style="width:460rpx;" v-model="form.title"></u-input>
				</view>
				<view class="address df_jub">
					<view style="width: 150rpx;">开始时间</view>
					<uni-datetime-picker type="datetime" v-model="form.startTime" />
				</view>
				<view class="address df_jub">
					<view style="width: 150rpx;">结束时间</view>
					<uni-datetime-picker type="datetime" v-model="form.endTime" />
				</view>
				<view class='toolbar' @tap="format" style="height: 40px;overflow-y: auto;display: flex;">
					<view class="iconfont icon-charutupian" @tap="insertImage"></view>
					<view class="iconfont icon-fengexian" @tap="insertDivider"></view>
					<view :class="formats.header === 1 ? 'ql-active' : ''" class="iconfont icon-format-header-1"
						data-name="header" :data-value="1"></view>
					<view :class="formats.header === 2 ? 'ql-active' : ''" class="iconfont icon-format-header-2"
						data-name="header" :data-value="2"></view>
					<view :class="formats.header === 3 ? 'ql-active' : ''" class="iconfont icon-format-header-3"
						data-name="header" :data-value="3"></view>
					<view :class="formats.bold ? 'ql-active' : ''" class="iconfont icon-zitijiacu" data-name="bold">
					</view>
					<view :class="formats.italic ? 'ql-active' : ''" class="iconfont icon-zitixieti" data-name="italic">
					</view>
					<view :class="formats.underline ? 'ql-active' : ''" class="iconfont icon-zitixiahuaxian"
						data-name="underline"></view>
					<view class="iconfont icon--checklist" data-name="list" data-value="check"></view>
					<view :class="formats.list === 'ordered' ? 'ql-active' : ''" class="iconfont icon-youxupailie"
						data-name="list" data-value="ordered"></view>
					<view :class="formats.list === 'bullet' ? 'ql-active' : ''" class="iconfont icon-wuxupailie"
						data-name="list" data-value="bullet"></view>
					<!-- #ifndef MP-BAIDU -->
					<view :class="formats.align === 'left' ? 'ql-active' : ''" class="iconfont icon-zuoduiqi"
						data-name="align" data-value="left"></view>
					<!-- #endif -->
					<view :class="formats.align === 'center' ? 'ql-active' : ''" class="iconfont icon-juzhongduiqi"
						data-name="align" data-value="center"></view>
					<view :class="formats.align === 'right' ? 'ql-active' : ''" class="iconfont icon-youduiqi"
						data-name="align" data-value="right"></view>
					<view :class="formats.align === 'justify' ? 'ql-active' : ''" class="iconfont icon-zuoyouduiqi"
						data-name="align" data-value="justify"></view>
					<view class="iconfont icon-undo" @tap="undo"></view>
					<view class="iconfont icon-redo" @tap="redo"></view>
					<view class="iconfont icon-clearedformat" @tap="removeFormat"></view>
					<view class="iconfont icon-shanchu" @tap="clear"></view>
				</view>
				<view class="editor-wrapper">
					<editor id="editor" class="ql-container" placeholder="输入内容..." show-img-size show-img-toolbar
						show-img-resize @statuschange="onStatusChange" :read-only="readOnly" @ready="onEditorReady">
					</editor>
				</view>
			</view>
			<view class="df df_acen df_jcen" @click="add"
				style="height: 80rpx;width: 90vw;background-color: #e4393c;color: #fff;margin:30rpx auto;"
				hover-class="release-hover">
				发布
			</view>
		</view>
	</view>
</template>

<script>
	import {
		HTTP_URL_DEV
	} from '@/api/config'
	import imgPicker from '@/components/img-picker.vue'
	export default {
		components: {
			imgPicker
		},
		data() {
			return {
				readOnly: false,
				formats: {},
				upForm: {},
				editorCtx: null,
				form: {
					imgs: [],
					title: '',
					content: '',
					postType: 'activity',
					// user_id:''
					startTime: '',
					endTime: '',
				},

			}
		},
		onLoad() {
			// console.log(this.userInfo)
		},
		methods: {
			getImg(imgs) {
				this.form.imgs = imgs
			},
			add() {
				let that = this
				// if (this.form.title != '') {
				// 	return uni.showToast({
				// 		title: '请填写标题'
				// 	})
				// }
				// if (this.form.content != '') {
				// 	return uni.showToast({
				// 		title: '请填写发帖内容'
				// 	})
				// }
				// if (this.form.startTime != '') {
				// 	return uni.showToast({
				// 		title: '请选择开始时间'
				// 	})
				// }
				// if (this.form.endTime != '') {
				// 	return uni.showToast({
				// 		title: '请选择结束时间'
				// 	})
				// }
				this.editorCtx.getContents({
					success: function(res) {
						that.form.content = res.html;
						that.$u.api.addPost(that.form).then(ress => {
							if (ress.code === 200) {
								uni.showToast({
									icon: 'success',
									title: '发布成功'
								})
								that.editorCtx.clear()
								setTimeout(() => {
									uni.navigateBack({
										delta: 1
									});
								}, 1500)
							} else {
								uni.showToast({
									icon: 'none',
									title: ress.message
								})
							}
						}).catch((error) => {
							// console.log(error);
							uni.showToast({
								icon: 'none',
								title: error
							})
						});
					},
					fail: function(res) {
						console.log('fail', res);
					}
				})
			},
			readOnlyChange() {
				this.readOnly = !this.readOnly
			},
			onEditorReady() {
				// #ifdef MP-BAIDU
				this.editorCtx = requireDynamicLib('editorLib').createEditorContext('editor');
				// #endif

				// #ifdef APP-PLUS || MP-WEIXIN || H5
				uni.createSelectorQuery().select('#editor').context((res) => {
					this.editorCtx = res.context
				}).exec()
				// #endif
			},
			undo() {
				this.editorCtx.undo()
			},
			redo() {
				this.editorCtx.redo()
			},
			format(e) {
				let {
					name,
					value
				} = e.target.dataset
				if (!name) return
				// console.log('format', name, value)
				this.editorCtx.format(name, value)
			},
			onStatusChange(e) {
				const formats = e.detail
				this.formats = formats
			},
			insertDivider() {
				this.editorCtx.insertDivider({
					success: function() {
						console.log('insert divider success')
					}
				})
			},
			clear() {
				uni.showModal({
					title: '清空编辑器',
					content: '确定清空编辑器全部内容？',
					success: res => {
						if (res.confirm) {
							this.editorCtx.clear({
								success: function(res) {
									console.log("clear success")
								}
							})
						}
					}
				})
			},
			removeFormat() {
				this.editorCtx.removeFormat()
			},
			insertImage: function(type = 0) {
				let that = this;
				uni.chooseImage({
					count: 1,
					sizeType: ['original', 'compressed'],
					sourceType: ['album', 'camera'],
					success: function(res) {
						uni.showLoading({
							title: '图片上传中',
						});
						let localPath = res.tempFilePaths[0];
						uni.uploadFile({
							url: HTTP_URL_DEV + '/common/upload',
							filePath: localPath,
							name: 'file',
							header: {
								"Content-Type": "multipart/form-data",
								"Authorization": uni.getStorageSync("App-Token"),
							},
							success: function(res) {
								uni.hideLoading();
								let data = res.data ? JSON.parse(res.data) : {};
								that.editorCtx.insertImage({
									src: HTTP_URL_DEV + data.fileName,
									alt: '图像',
									success: function() {}
								})

							},
							fail: function(res) {
								console.log(res, 'err')
								uni.hideLoading();
								uni.showToast({
									title: '上传图片失败'
								})
							}
						})
					}
				})
			},
		}
	}
</script>

<style scoped>
	@import "../../../static/iconfont.css";

	::v-deep .uni-date-x--border {
		border: none;
	}

	page {
		background-color: #f5f5f5 !important;
	}

	.pad_4 {
		width: 100%;
		min-height: 415rpx;
		box-sizing: border-box;
		padding: 10rpx;
		background: #F4F5F7;

	}

	.address {
		width: 100%;
		padding: 0 4%;
		height: 99rpx;
		margin: 0 auto;
		background-color: #fff;
		box-sizing: border-box;
		line-height: 80rpx;
		font-size: 30rpx;
		font-family: Adobe Heiti Std;
		font-weight: normal;
		color: #333333;
		border-bottom: 2rpx solid #F6F6F6;
		display: flex;
		align-items: center;
	}

	.release-hover {
		background-color: #06c;
		color: #fff;
	}

	.page-body {
		height: calc(100vh - var(--window-top) - var(--status-bar-height));

		padding: 0 4%;
	}

	.wrapper {
		height: 100%;
	}

	.editor-wrapper {
		height: 40vh;
		/* height: calc(100vh - var(--window-top) - var(--status-bar-height) - 50px - 350rpx); */
		background: #fff;
	}

	.iconfont {
		display: inline-block;
		padding: 8px 8px;
		width: 24px;
		height: 24px;
		cursor: pointer;
		font-size: 20px;
	}

	.toolbar {
		box-sizing: border-box;
		border-bottom: 1rpx solid #eee;
		font-family: 'Helvetica Neue', 'Helvetica', 'Arial', sans-serif;
		/* background-color: #eee; */
	}

	.ql-container {
		box-sizing: border-box;
		padding: 12px 15px;
		width: 100%;
		min-height: 30vh;
		height: 100%;
		font-size: 16px;
		line-height: 1.5;
	}

	.ql-active {
		color: #06c;
	}

	.df {
		display: flex;

	}

	.df_jub {
		justify-content: space-between;
	}

	.df_jua {
		justify-content: space-around;
	}

	.df_jcen {

		justify-content: center;
	}

	.df_acen {
		align-items: center;

	}

	.df_column {
		flex-direction: column;
	}

	.df_jubs {
		justify-content: flex-start;
	}
</style>