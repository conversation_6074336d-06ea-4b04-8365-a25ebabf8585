<template>
	<view class="page">
		<Navbar title="选择小区"></Navbar>
		<view class="xfx-sinput">
			<view class="search-card">
				<view class="bubble-card" v-show="false">
				</view>
				<view style="font-size: 26rpx;">
					<u-icon size='30' style="margin-right: 10upx;" name="map-fill"></u-icon>
					<text>{{ truncatedLocation }}</text>
					<u-icon size='20' name="play-right-fill" color="#bfbfbf"
						style="transform: rotate(90deg);margin-left: 10upx;"></u-icon>
				</view>
				<view>
					<text style="color: #d7d7d7;padding-bottom: 8rpx;">|</text>
					<input v-model="searchValue" @confirm='confirm' placeholder="请输入小区名称..." type="text" name="" id="">
					<icon v-if="searchValue.length > 0" @click="searchValue = ''" type="clear" size="16" color="" />
				</view>
			</view>
		</view>

		<u-card :border="false" :foot-border-top="false" padding="20" margin="20rpx 30rpx 0 30rpx">
			<view slot="head" style="font-weight: 600;">
				绑定地点
			</view>
			<view slot="body">
				<view class="location-list">
					<view v-for="(item, index) in locations" :key="index" class="location-item"
						:class="{ selected: locationName === item.name }" @click="selectLocation(item)">
						<view class="tags">
							<text>{{ item.name }}</text>
							<text class="tag">小区</text>
							<text v-if="locationName === item.name" class="current-tag">当前选择</text>
						</view>
						<view class="btn-del" v-if="locationName !== item.name" @click.stop="delBind(item)">
							解绑
						</view>
					</view>
				</view>
				<view v-if="locations.length == 0" style="text-align: center; padding: 20rpx 0;">
					暂无地点
				</view>
			</view>
		</u-card>

		<u-card :border="false" :foot-border-top="false" padding="20" v-if="streetList.length > 0">
			<view slot="head" style="font-weight: 600;">
				请选择所在地区
			</view>
			<view slot="body">
				<view style="width: 100%;">
					<u-tabs item-width="50%" :list="list" :is-scroll="false" :current="current" @change="change"
						active-color="#e4393c"></u-tabs>
				</view>
				<scroll-view class="scroll-Y" scroll-y="true">
					<view v-for="(item, index) in getDatalist()" :key="index" @click="setStreet(item, index)"
						class="location-item" style="border:none;">
						<view class="tags" style="width: 100%;justify-content: space-between;">
							<view>{{ item.name }}</view>
							<view class="btn-del" style="background-color: #e4393c;" v-if="item.level == 5"
								@click="showModal(item)">
								绑定
							</view>
						</view>
					</view>
				</scroll-view>
			</view>
		</u-card>
		<u-modal v-model="show" @confirm="binding" :title="`已绑${locations.length || 0}个小区`" content="每个账号可绑定3个小区"
			cancel-text="重新选择" confirm-color="#e4393c" show-cancel-button ref="uModal"></u-modal>
	</view>
</template>

<script>
	// 主题
	import appTheme from '@/theme.scss';
	// 数据源
	import streetList from '@/static/street-list.json';
	// 组件
	// import Tag from '@/components/tag.vue';
	export default {
		components: {
			// Tag
		},
		data() {
			return {
				show: false,
				locations: [],
				locationName: '暂无',
				searchList: [],
				title: '筛选',
				showBubble: false,
				searchValue: '',
				nowCity: '',
				streetList: [],
				list: [{
					name: '请选择'
				}],
				current: 0,
				currentIndex: 0,
				code: '',
			};
		},
		computed: {
			truncatedLocation() {
				if (!this.locationName) return '';
				const arr = Array.from(this.locationName);
				return arr.length > 4 ?
					arr.slice(0, 4).join('') + '...' :
					this.locationName;
			}
		},
		onLoad(ops) {
			// this.streetList = streetList;
			this.nowCity = ops.nowCity;
			this.getList()
			// this.cityList = [cityList.hotcity, ...cityList.city];
		},
		onPageScroll(e) {
			this.scrollTop = e.scrollTop;
		},
		methods: {
			getList() {
				this.$u.api.getRegionList('').then(res => {
					this.streetList = res.data;
				})
				this.$u.api.getBindRegion().then(res => {
					this.locations = res.data;
					let obj = this.locations.find(item => item.enabled == true)
					if (obj) {
						this.locationName = obj.name;
					} else {
						this.locationName = '暂无';
					}
				})
			},
			showModal(item) {
				if (this.locations.length == 3) {
					uni.showToast({
						title: '最多只能绑定3个小区',
						icon: 'none',
					})
					return
				}
				let index = this.locations.findIndex(i => {
					return i.name == item.name
				})
				if (index === -1) {
					this.show = true;
					this.code = item.code;
				} else {
					uni.showToast({
						title: '该社区已绑定',
						icon: 'none',
					})
				}
			},
			binding() {
				this.$u.api.bindRegion(this.code).then(res => {
					uni.showToast({
						title: '绑定成功',
						icon: 'none',
					})
					if (this.locations.length == 0) {
						this.selectLocation({
							code: this.code
						})
					}
					this.getList()
				})
			},
			delBind(item) {
				let that = this;
				uni.showModal({
					title: "提示",
					content: `是否确认取消绑定${item.name}？`,
					cancelText: "取消",
					confirmText: "确定",
					success: function(res) {
						if (res.confirm) {
							that.$u.api.delBindRegion(item.code).then(res => {
								uni.showToast({
									title: '解绑成功',
									icon: 'none',
								})
								that.getList()
							})
						}

					},
				});

			},
			setStreet(item, index) {
				if (this.current == 1) return;
				let list = this.current == 1 ? this.streetList : this.streetList[index]?.children
				if (list.length == 0) {
					uni.showToast({
						title: '当前街道暂无开通小区',
						icon: 'none',
					})
					return
				}
				this.currentIndex = index;
				this.list = [{
						name: item.name
					},
					{
						name: '请选择'
					},
				];
				this.current = 1;
			},
			getDatalist() {
				let list = this.current == 0 ? this.streetList : this.streetList[this.currentIndex]?.children
				if (list.length > 0) {
					return list
				} else {
					this.list = [{
						name: '请选择'
					}, ];
					uni.showToast({
						title: '当前街道暂无开通小区',
						icon: 'none',
					})
					return this.streetList
				}

			},
			change(index) {
				this.current = index;
			},
			selectLocation(item) {
				this.$u.api.setDefaultRegion(item.code).then(res => {
					// this.locationName = location; // 切换选中的小区
					uni.showToast({
						title: '设置默认小区成功',
						icon: 'none',
					})
					this.$store.commit('user/SET_COM', item.name);
					this.getList()
				})

			},
			confirm(e) {
				this.$u.api.getRegionList(this.searchValue).then(res => {
					this.list = [{
						name: '请选择'
					}, ];
					this.searchValue = '';
					this.streetList = res.data;
				})
			},
		}
	};
</script>

<style lang="scss" scoped>
	.page {
		// padding: 24rpx;
		background-color: #f5f5f5 !important;
	}

	::v-deep .u-tab-item {
		flex: none !important;
	}

	::v-deep .u-scroll-box {
		background-color: #f3f5f7;
	}

	.scroll-Y {
		height: calc(100vh - 410px);
	}

	.xfx-sinput {
		width: 100%;
		padding: 0 30rpx 14rpx 30rpx;
		margin: 0 auto;
		box-sizing: border-box;
		display: flex;
		position: relative;
		background-color: #fff;

		.search-card {
			flex: 1;
			padding: 0 10rpx;
			background: #f3f5f7;
			border: 1px solid #e2e2e2;
			border-radius: 40rpx;
			display: flex;
			align-items: center;

			.bubble-card {
				position: absolute;
				z-index: 10;
				left: 50rpx;
				top: 110%;
				border: 1px solid #e2e2e2;
				background-color: #ffffff;
				padding: 25rpx;
				border-radius: 5rpx;

				&>view {
					margin-bottom: 20rpx;
				}

				&>view:nth-last-child(1) {
					margin-bottom: 0;
				}
			}

			.bubble-card::after {
				content: '';
				display: block;
				border: 15rpx solid #ffffff;
				border-left: 15rpx solid transparent;
				border-right: 15rpx solid transparent;
				border-top: 15rpx solid transparent;
				position: absolute;
				top: -28rpx;
				left: 45rpx
			}

			&>view:nth-child(2) {
				display: flex;
				align-items: center;
				padding: 0 15rpx;
				position: relative;

				text {}

				image {
					margin-left: 15rpx;
					transition: all 0.3s;
					width: 25rpx;
					height: 25rpx;
				}
			}

			&>view:nth-child(3) {
				flex: 1;
				padding-left: 10rpx;
				padding-right: 15rpx;
				display: flex;
				align-items: center;

				input {
					padding: 4rpx 10rpx;
					font-size: 26rpx;
				}
			}
		}

		.search-text {
			padding-left: 25rpx;
			display: flex;
			align-items: center;
			font-size: 36rpx;
			line-height: 1;
		}
	}

	.location-item {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 20rpx;
		border-bottom: 1px solid #e5e5e5;
		cursor: pointer;
	}

	.location-item:last-child {
		border-bottom: none;
		/* 移除最后一个的边框 */
	}

	.location-item.selected {
		background-color: #f5f5f5;
	}

	.tags {
		display: flex;
		align-items: center;
	}

	.tag {
		background-color: #ffd666;
		color: #fff;
		padding: 5rpx 10rpx;
		border-radius: 50rpx;
		margin-left: 10rpx;
		font-size: 20rpx;
	}

	.current-tag {
		background-color: #ff4d4f;
		color: #fff;
		padding: 5rpx 10rpx;
		border-radius: 50rpx;
		margin-left: 10rpx;
		font-size: 20rpx;
	}

	.btn-del {
		font-size: 25rpx;
		font-weight: 600;
		background: #d7d7d7;
		border-radius: 50rpx;
		color: #fff;
		padding: 2rpx 12rpx;
	}
</style>