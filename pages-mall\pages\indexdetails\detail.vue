<template>
	<view class="page">
		<!-- 一般导航栏 -->
		<Navbar title="商品详情"></Navbar>
		<!-- 商品图片 -->
		<ImgSwiper :list="goodsDetail.picture"></ImgSwiper>
		<!-- 商品信息 -->
		<GoodsInfo :data="goodsDetail" :goodsType="goodsType"></GoodsInfo>
		<!-- 选择项 -->
		<GoodsSelect :selectedSku="selectedSku" :selectedAddress="selectedAddress" @openSku="openSkuPopup">
		</GoodsSelect>
		<!-- 评价 -->
		<TitleOperate :title="'商品评价（' + evaluateData.length + '条）'" showMore moreLabel=" " :backgroundColor="bgColor"
			titleSize="32rpx" align="center" @clickMore="evMore">
		</TitleOperate>
		<view class="evaluate-list">
			<EvaluateCard :data="evaluate" v-for="(evaluate, evaluateIndex) in evaluateData" :key="evaluateIndex"
				:showBorderBottom="evaluateIndex != evaluateData.length - 1"></EvaluateCard>
		</view>

		<view class="details-html" v-html="goodsDetail.serviceIntroduction">

		</view>
		<!-- 底部操作按钮 -->
		<GoodsOperate :data="goodsDetail" @addShoppingCart="addShoppingCart" @buyNow="buyNow"
			hideShoppingCart='hideShoppingCart'></GoodsOperate>
		<!-- 选择sku -->
		<GoodsSelectSku ref="GoodsSelectSku" @addShoppingCart="addShoppingCart" @buyNow="buyNow" @change="changeSku">
		</GoodsSelectSku>
	</view>
</template>

<script>
	import ImgSwiper from '@/components/img-swiper.vue';
	import GoodsInfo from '@/pages-mall/components/goods/goods-info.vue';
	import GoodsSelect from '@/pages-mall/components/goods/goods-select.vue';
	import GoodsOperate from '@/pages-mall/components/goods/goods-operate.vue';
	import GoodsSelectSku from '@/pages-mall/components/goods/goods-select-sku.vue';
	import EvaluateCard from '@/pages-mall/components/evaluate-card.vue';
	import PostCardSimple from '@/pages/community/components/post-card-simple.vue';
	import CardGoods from '@/pages/mall/components/card.vue';
	import TitleOperate from '@/components/title-operate.vue';
	import {
		topicList,
		goodsList
	} from '@/static/test-data.js';
	import {
		HTTP_URL_PROD
	} from '@/api/config';
	const token = uni.getStorageSync("App-Token");
	export default {
		components: {
			ImgSwiper,
			GoodsInfo,
			GoodsSelect,
			GoodsOperate,
			GoodsSelectSku,
			EvaluateCard,
			PostCardSimple,
			CardGoods,
			TitleOperate
		},
		data() {
			return {
				https: HTTP_URL_PROD,
				// 商品类型，normal、points
				goodsType: 'normal',
				goodsDetail: {
					picture: [],
				},
				// 商品评价
				evaluateData: [],
				// 精选晒单
				topicData: topicList,
				// 相关商品
				goodsData: goodsList,
				// 背景色
				bgColor: this.$appTheme.appThemeBgColor,
				// 已选择sku
				selectedSku: {
					label: '',
					value: ''
				},
				// 已选择地址
				selectedAddress: '',
				// 地址总列表
				dizhi: [],
				// 地址数据
				ojbselectedAddress: {},
			};
		},
		onLoad(options) {
			let userInfo = JSON.parse(decodeURIComponent(options.item));
			console.log(userInfo, 'userInfo');

			this.getlistindetails(userInfo.id);
			this.getevaluatelists(userInfo.id);
			if (token) {
				this.AddressList()
			}
		},
		methods: {
			getAddressId(address) {
				console.log('收到地址数据页面1：', address);
				console.log('地址列表：', this.dizhi);
				const mainAddress = this.dizhi.find(item => item.id === address);
				this.selectedAddress = mainAddress.region
				this.ojbselectedAddress = mainAddress
			},
			AddressList() {
				let data = {
					pageSize: 99999,
					pageNum: 1
				}
				// isMian 1默认地址
				this.$u.api.getAddressList(data).then(res => {
					this.dizhi = res.rows
					const mainAddress = res.rows.find(item => item.isMian === 1);
					if (mainAddress) {
						this.selectedAddress = mainAddress.region
						this.ojbselectedAddress = mainAddress
					}
				})
			},
			// 详情
			getlistindetails(id) {
				this.$u.api.getlistindetail(id).then(res => {
					this.goodsDetail = res.data;

					if (typeof this.goodsDetail.picture === 'string') {
						this.goodsDetail.picture = this.goodsDetail.picture.split(',').map(imgUrl => {
							return this.https + imgUrl.trim();
						});
					}
					console.log(this.goodsDetail, '详情');
					this.getGoodsDetail();
				});
			},
			// 查询商品详情
			getGoodsDetail() {
				this.selectedSku.label = this.goodsDetail.communityServiceSpecifications[0].title;
				this.selectedSku.value = this.goodsDetail.communityServiceSpecifications[0].id;
			},
			// 评价
			getevaluatelists(id) {
				let data = {
					communityServiceId: id
				}
				this.$u.api.getevaluatelist(data).then(res => {
					this.evaluateData = res.rows
				})
			},
			evMore() {
				console.log('点击')
				uni.navigateTo({
					url: '/pages-mall/pages/evaluate/list?item=' + encodeURIComponent(JSON.stringify(this
						.evaluateData))
				})
			},
			// 打开选择sku的弹窗
			openSkuPopup() {
				this.$refs.GoodsSelectSku.open(this.goodsDetail, this.selectedSku, 'hideShoppingCart', this
					.ojbselectedAddress);
			},

			// 切换sku
			changeSku(e) {
				this.selectedSku.label = e.label;
				this.selectedSku.value = e.value;
			},

			// 加入购物车
			addShoppingCart() {
				this.openSkuPopup();
			},

			// 立即购买
			buyNow() {
				this.openSkuPopup();
			}
		}
	};
</script>

<style lang="scss" scoped>
	.evaluate-list,
	.topci-list,
	.goods-list {
		background-color: $app-theme-bg-color;
		padding: 0 30rpx 30rpx 30rpx;
		margin-bottom: 16rpx;
	}

	.details-html {
		background-color: $app-theme-bg-color;
		padding: 0 30rpx 166rpx 30rpx;
		margin-bottom: 100rpx;
	}

	.view-more {
		display: flex;
		justify-content: center;
		align-items: center;
		padding-top: 24rpx;
	}
</style>