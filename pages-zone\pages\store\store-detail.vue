<template>
	<view class="store-detail">
		<img style="width: 100%;height: 800rpx;" :src="baseUrl + store.img" alt="" />
		<view class="detail">
			<view class="text">
				<view class="price">
					免费预约
				</view>
				<view class="name">
					{{store.name}}
				</view>
				<view class="category">
					{{formatCategory(store.storeStoreClsId)}}
				</view>
			</view>
			<view class="sell">
				月售{{store.order}}件
			</view>
		</view>
		<shop-detail-button @submit='submit' buttonTitle='加入购物车'></shop-detail-button>
		<shop-detail-cart :isShow="isShow" :store='store' @isShow='isShow=!isShow'></shop-detail-cart>
	</view>
</template>

<script>
	import { baseUrl } from '@/api/config'
	import ShopDetailButton from './shop/components/shop-button.vue';
	import ShopDetailCart from './shop/components/shop-cart.vue';
	export default {
		data() {
			return {
				store:{},
				baseUrl,
				storeCategoryDict:[],
				isShow:false,
			}
		},
		components: {
			ShopDetailButton,
			ShopDetailCart,
		},
		methods: {
			getStoreCategory(){
				return this.$u.api.listData({pageSize:99999,pageNum:1,dictType:'shop_store_category'}).then(res=>{
					this.storeCategoryDict = res.rows
				})
			},
			formatCategory(str){
				let id =str?.split(',')[1]
				return this.storeCategoryDict.find(i=>i.dictCode == id)?.dictLabel
			},
			submit(){
				this.isShow = true
			},
		},
		onLoad({id}) {
			this.getStoreCategory().then(()=>{
				this.$u.api.getStoreDetail(id).then(res=>{
					this.store = res.data
				})
			})
		}
	}
</script>

<style scoped lang="scss">
.detail{
	display: flex;
	justify-content: space-between;
	background: #fff;
	padding: 30rpx 20rpx;
	margin-top: 20rpx;
	
	.price{
		color: #ff2e38;
		font-size: 50rpx;
		font-weight: 700;
	}
	
	.name{
		font-size: 50rpx;
		color: #000;
		font-weight: 700;
	}
	
	.category{
		color: #ac8f8f;
		font-size: 30rpx;
	}
	
	.sell{
		color: #ff0000;
	}
}
</style>
