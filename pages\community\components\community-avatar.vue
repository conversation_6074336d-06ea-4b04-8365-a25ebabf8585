<template>
	<view class="slot">
		<view class="left">
			<u-avatar :src="data.avatarUrl" mode="circle" size="80" ></u-avatar>
			<view class="info">
				<view>
					<text class="username">{{ data.nickName||'微信用户' }}</text>
					<!-- <view class="tag" v-if="showTag">
						<u-tag v-if="data.typeOfficial === 1" text="官网" size="mini" bg-color="#9091EB" color="#F1F7FF" border-color="#9091EB" />
						<u-tag v-if="data.Overhead === 2" text="顶置" size="mini" bg-color="#FC7D6D" color="#F1F7FF" border-color="#FC7D6D" />
					</view> -->
				</view>
				<view class="time">{{ data.creatTime ||'' }}</view>
			</view>
		</view>
		<view v-if="isFollow&&data.userId!=userId" @click="follow"  :class="data.isFollowed?'follow':''"   class="followBtn">{{ data.isFollowed?'取消关注':'关注' }}</view>
		<!-- <view v-if="isFollow" class="followBtn"></view> -->
		<view v-if="data.pageType == 'myT'"  @click.stop="more"><u-icon size="30" name="more-dot-fill" :color="iconColor"></u-icon></view>
		<!-- <view v-if="operateType == 'report'" @click.stop="report"><u-icon size="30" name="warning" :color="iconColor"></u-icon></view> -->
	</view>
</template>

<script>
export default {
	props: {
		data: {
			type: Object,
			default: {}
		},
		showTag: {
			type: Boolean,
			default: false
		},
		operateType: {
			type: String,
			default: 'more'
		},
		isFollow: {
			type: Boolean,
			default: false
		}
	},
	data() {
		return {
			iconColor: this.$appTheme.appThemeTextGrayColor,
			userId:this.$store.state.user.userInfo.userId,
		};
	},
	methods: {
		follow() {
			this.$u.api.addFollow({
				userId: this.$store.state.user.userInfo.userId,
				postId: this.data.id,
				followType:"post",
				followId: this.data.userId
				// id: this.detailData.id
			}).then(r => {
				uni.showToast({
					title: r.msg,
					icon: 'none'
				});
				this.$emit('getData');
			})
		},
		// 举报
		report() {
			this.$emit('report');
		},
		// 更多
		more(){
			this.$emit('more')
		}
	}
};
</script>

<style lang="scss" scoped>
.slot {
	display: flex;
	justify-content: space-between;
}
.left {
	display: flex;
	.info {
		margin-left: 16rpx;
		.username {
			font-size: 28rpx;
			font-family: PingFangSC-Regular, PingFang SC;
			font-weight: 400;
			color: $app-theme-text-black-color;
			// margin-bottom: 8rpx;
	
		}
		.tag {
			font-size: 24rpx;
			font-family: PingFangSC-Regular, PingFang SC;
			font-weight: 400;
			color: $app-theme-text-gray-color;
		}
	}
	.time {
		font-size: 24rpx;
		font-weight: 400;	
		color: $app-theme-text-gray-color;
		line-height: 34rpx;
		margin-top: 8rpx;
	}
}

.followBtn{
	text-align: center;
	width: 100rpx;
	height: 50rpx;
	line-height: 50rpx;
	border: 1px solid $app-theme-text-black-color;
	border-radius: 20rpx;
}
.follow{
	width: 150rpx;
	color: $app-theme-color;
	border: 2rpx solid $app-theme-color;
	background-color: rgba($app-theme-color, 0.1);
}
</style>
