<template>
	<view class="page">
		<Navbar title="我的关注"></Navbar>
		<!-- <u-tabs :list="list"  :current="current" @change="change"></u-tabs> -->
		<!-- <view v-if="current==0"> -->
		<UserCardOperate v-if="item.isFollowed" @getData="getData" :data="item" v-for="(item, index) in userList" :key="index" :border="index != userList.length - 1"></UserCardOperate>
		<!-- </view> -->
		<!-- <view v-else>
			<PostCard v-for="(item, index) in circleList" :key="index" :data="item" :border="index != circleList.length - 1"></PostCard>
		</view> -->
		<!-- ces1 -->
	</view>
</template>
<script>
	import UserCardOperate from '@/pages-mine/components/user-card-operate.vue';
	export default {
		components: {
			UserCardOperate,
		},
		data() {
			return {
				list: [{
					name: '用户'
				}, {
					name: '帖子'
				}],
				current: 0,
				userList:[],
			};
		},
		onShow() {
			this.getData()
		},
		methods: {
			getData() {
				this.$u.api.getFollowList({
					userId: this.$store.state.user.userInfo.userId
				}).then(r => {
					this.userList = r.data
				})
			},
			change(index) {
				this.current = index;
			},
		},
	}
</script>

<style lang="scss" scoped>
	.page {
		background-color: $app-theme-bg-color;
		padding: 0 30rpx;
	}
</style>