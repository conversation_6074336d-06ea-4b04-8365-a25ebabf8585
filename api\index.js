// 用户模块api
import {
	useWxApi
} from './modules/user.js'

// 基础模块api
import {
	useFileApi
} from './modules/base.js'

// 商城模块api
import {
	useMallApi
} from './modules/mall.js'

// 社区模块api
import {
	useCommunityApi
} from './modules/community.js'
// 金刚区-xk
import {
	useJGQApi
} from './modules/jingangqu.js'

import {
	useShopApi
} from './modules/shop.js'

import {
	useDictApi
} from './modules/dict.js'

import {
	useCouponApi
} from './modules/coupon.js'

import {
	useAddressApi
} from './modules/address.js'


// 导出api全局安装方法
export const installApiModules = (Vue, vm) => {
	vm.$u.api = Object.assign(useFileApi(Vue, vm),
		useWxApi(Vue, vm),
		useMallApi(Vue, vm),
		useCommunityApi(Vue, vm),
		useShopApi(Vue, vm),
		useDictApi(Vue, vm),
		useJGQApi(Vue, vm),
		useCouponApi(Vue, vm),
		useAddressApi(Vue, vm),
	)
}