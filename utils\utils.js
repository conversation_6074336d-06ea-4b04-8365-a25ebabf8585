import {
	HTTP_URL_DEV
} from '@/api/config'
export default {

	uploadImageOne: function(opt, successCallback, errorCallback) {
		let that = this;
		uni.chooseImage({
			count: 1,
			sizeType: ['original', 'compressed'],
			sourceType: ['album', 'camera'],
			success: function(res) {
				//启动上传等待中...  
				uni.showLoading({
					title: '图片上传中',
				});
				let localPath = res.tempFilePaths[0];
				console.log(localPath, 'localPath')
				uni.uploadFile({
					url: HTTP_URL_DEV + '/common/upload',
					filePath: localPath,
					name: 'file',
					header: {
						// #ifdef MP
						"Content-Type": "multipart/form-data",
						// #endif
						"Authorization": uni.getStorageSync("App-Token"),
					},
					success: function(res) {
						uni.hideLoading();
						if (res.statusCode == 403) {
			
						} else {
							let data = res.data ? JSON.parse(res.data) : {};
							if (data.code == 200) {
								// data.data.localPath = localPath;
								successCallback && successCallback(data)
							} else {
								errorCallback && errorCallback(data);

							}
						}
					},
					fail: function(res) {
						console.log(res, 'err')
						uni.hideLoading();
						uni.showToast({
							title: '上传图片失败'
						})
					}
				})
				// pathToBase64(res.tempFilePaths[0])
				// 	.then(imgBase64 => {
				// 		console.log(imgBase64);

				// 	})
				// 	.catch(error => {
				// 		console.error(error)
				// 	})
			}
		})
	}

}