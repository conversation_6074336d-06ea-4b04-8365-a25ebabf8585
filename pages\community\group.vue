<template>
	<view class="slot">
		
	</view>
</template>

<script>
import PostC<PERSON> from '@/pages/community/components/post-card';
import { dialNavListconmmunity, scrollcommunity, circleList } from '@/static/test-data.js';
export default {
	components: {
		PostCard
	},
	data() {
		return {
			// 金刚区
			current: 0,
			dialNavList: dialNavListconmmunity,
			// 圈子用户
			circleList: []
		};
	},
	mounted() {
		// this.getList()
	},
	methods: {
		getList(){
			this.$u.api.getMyPosts({postType:'topic'}).then(r=>{
				console.log(r,'执行22')
				this.circleList = r.data
			})
		}
	}
};
</script>

<style lang="scss" scoped>
.hot-nav {
	padding: 0rpx 0rpx 30rpx 0rpx;
	background-color: $app-theme-bg-color;
}
</style>
