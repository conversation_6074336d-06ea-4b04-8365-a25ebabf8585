<template>
	<view class="page">
		<Navbar title="发布帖子"></Navbar>
		<u-input placeholder="请填写标题" v-model="form.title" type="text" />
		<view style="width: 100%;height: 1px;background-color: #ccc;"></view>
		<u-input placeholder="请填写发帖内容" v-model="form.content" type="textarea" height="300" :auto-height="true" />
		<view style="width: 100%;height: 1px;background-color: #ccc;"></view>
		<img-picker @imglist='getImg' :limit="3" mediatype="all" :types='1'></img-picker>
		<view class="btn">
			<u-button type="primary" shape="circle" @click="addUp"><text>提交</text></u-button>
		</view>
	</view>
</template>

<script>
	import imgPicker from '@/components/img-picker.vue'

	export default {
		components: {
			imgPicker
		},
		data() {
			return {
				edit: null,
				showGoodsSelect: true,
				// 主题色
				appThemeColor: this.$appTheme.appThemeColor,
				appThemeTextGrayColor: this.$appTheme.appThemeTextGrayColor,
				// 表单
				form: {
					imgs: [],
					title: '',
					content: '',
					postType: 'topic',
					// user_id:''
				},
				// 上传地址
				uploadUrl: '',
				// 附属信息
				selectedCommunity: '',
				selectedGoods: '',
				selectedLocation: '',
				// 已选择的收藏商品列表
				selecedCollections: [],
				// 已选择的圈子
				selecedCommunity: {}
			};
		},
		onLoad(options) {
			if (options.showGoodsSelect == 'false') {
				this.showGoodsSelect = false;
			}
			// 清除选择圈子的缓存
			uni.setStorageSync('TEMP_SELECTED_COMMUNITY', {});
			// 清除选择商品的缓存
			uni.setStorageSync('TEMP_SELECED_COLLECTIONS', []);
			// this.form.user_id = this.$store.state.user.userInfo.userId
		},
		onShow() {
			// 回显选择的圈子
			this.selecedCommunity = uni.getStorageSync('TEMP_SELECTED_COMMUNITY');
			this.selectedCommunity = this.selecedCommunity.cateName;
			// 回显选择的商品
			this.selecedCollections = uni.getStorageSync('TEMP_SELECED_COLLECTIONS');
			this.selecedCollections.map(item => {
				this.selectedGoods += item.title + ',';
			});
			this.selectedGoods = this.selectedGoods.substr(0, this.selectedGoods.length - 1);
		},
		methods: {
			getImg(imgs) {
				this.form.imgs = imgs
			},
			addUp() {
				// if (this.form.title != '') {
				// 	return uni.showToast({
				// 		title: '请填写标题'
				// 	})
				// }
				// if (this.form.content != '') {
				// 	return uni.showToast({
				// 		title: '请填写发帖内容'
				// 	})
				// }	
				this.$u.api.addPost(this.form).then(r => {
					uni.showToast({
						title: '发布成功'
					})
					setTimeout(() => {
						uni.navigateBack({
							delta: 1
						});
					}, 2000)

				})
			},
			// 上传图片成功
			uploadPicSuccess(data, index, lists, name) {
				let obj = {
					src: data.data.src,
					name: name || '未命名图片.png',
					type: 0
				};
				this.form.pics.push(obj);
			}

		}
	};
</script>

<style lang="scss" scoped>
	.page {
		padding: 24rpx 30rpx;
		background-color: $app-theme-bg-color;
	}

	.card {
		.form {}

		.pics {
			padding-bottom: 30rpx;
			border-bottom: 1rpx solid $app-theme-border-color;
			// 视觉对齐
			margin-left: -12rpx;
		}
	}

	.satellite {
		margin-top: 24rpx;

		.item {
			border-top: 1px solid $app-theme-border-color;
			padding: 24rpx 0;
			display: flex;
			align-items: center;
			justify-content: space-between;

			.left {
				display: flex;
				align-items: center;

				view {
					padding-left: 24rpx;
					font-size: 28rpx;
					font-family: PingFangSC-Regular, PingFang SC;
					font-weight: 400;
					color: $app-theme-text-gray-color;
					overflow: hidden;
					white-space: nowrap;
					text-overflow: ellipsis;
					width: 80vw;
				}
			}
		}
	}

	.btn {
		position: absolute;
		bottom: 20rpx;
		width: 90vw;
		padding: 60rpx 0rpx;
	}
</style>