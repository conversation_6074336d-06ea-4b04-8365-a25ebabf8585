<template>
	<view class="container">
		<view class="goods-list">
			<view class="goods-item" v-for="(item,index) in data" :key="index" @click="chandetail(item)">
				<view class="pic">
					<u-image width="100%" height="320rpx" mode="aspectFill" :src="https+item.cover"></u-image>
				</view>
				<view class="content">
					<view class="title line-clamp-2">{{ item.title }}</view>
					<view class="price-area">
						<view class="current-price">
							<text class="symbol">￥</text>
							<text class="number">{{ item.amount }}</text>
						</view>
						<view class="sales">月销{{ item.sold || 0 }}</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		HTTP_URL_PROD
	} from '@/api/config';
	export default {
		props: {
			// 数据源
			data: {
				type: Array,
				default: () => {
					return {};
				}
			},
		},
		data() {
			return {
				https: HTTP_URL_PROD,
			}
		},
		methods: {
			chandetail(item) {
				uni.navigateTo({
					url: '/pages-mall/pages/indexdetails/detail?item=' + encodeURIComponent(JSON.stringify(item))
				})
			},
		}
	};
</script>

<style lang="scss" scoped>
	.container {
		padding: 20rpx 16rpx;
	}

	.goods-list {
		display: flex;
		flex-wrap: wrap;
		justify-content: space-between;
	}

	.goods-item {
		width: 48%;
		background: #fff;
		border-radius: 12rpx;
		overflow: hidden;
		margin-bottom: 20rpx;
		box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.04);

		.pic {
			background: #f5f5f5;
		}

		.content {
			padding: 16rpx 12rpx 24rpx;
		}

		.title {
			font-size: 26rpx;
			color: #333;
			line-height: 1.4;
			min-height: 72rpx;
		}

		.price-area {
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin-top: 16rpx;

			.current-price {
				color: #ff4444;
				display: flex;
				align-items: baseline;

				.symbol {
					font-size: 24rpx;
					margin-right: 4rpx;
				}

				.number {
					font-size: 32rpx;
					font-weight: 600;
				}
			}

			.sales {
				font-size: 24rpx;
				color: #999;
			}
		}
	}

	.line-clamp-2 {
		display: -webkit-box;
		-webkit-box-orient: vertical;
		-webkit-line-clamp: 2;
		overflow: hidden;
	}
</style>