export const useCommunityApi = (Vue, vm) => {
	return {
		// 轮播图
		listBanner: (query) => vm.$u.get('/banner/show-list', query),
		// 邻工提交接口
		laddRequest: (data) => vm.$u.post('/service/request/add', data),
		// 上传图片接口
		uploadImage: (filePath) => vm.$u.upload('/common/upload', {
			filePath: filePath,
			name: 'file'
		}),
		// 获取接单大厅列表
		getDeliveryOrders: (params) => vm.$u.get('/api/deliveryOrders/pollPending', params),
		// 获取接单状态列表
		getOrderslist: (params) => vm.$u.get('/api/deliveryOrders/list', params),
		// 代办
		dbladdRequest: (data) => vm.$u.post('/system/universalRequest/add', data),
		// 首页邻工入驻接口
		neighborWorker: (data) => vm.$u.post('/system/neighborWorker', data),

		grabOrder: (orderId) => vm.$u.post(`/api/deliveryOrders/grab/${orderId}`),

		ListupdateOrder: (data) => vm.$u.put('/api/deliveryOrders/update', data),
		// 优惠券接口
		getAvailableCoupons: (params) => vm.$u.get('/coupon/available-list', params),

	}
}