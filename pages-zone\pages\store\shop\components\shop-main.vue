<template>
	<view class="shop-detail-main">
		<Tabs @change='handleTabs' :fontSize='36' activeColor='#f39800' backgroundColor="#fff" :listData="scrollData">
		</Tabs>

		<view class="main">
			<!-- 商品列表区域 -->
			<view class="store" :style="'height:' + (height - 360) + 'px'" v-show="scroll === 0">
				<view style="display: grid;grid-template-columns: 30% 70%;height: 100%;">
					<!-- 左侧分类栏 -->
					<view class="sidebar">
						<view class="sidebar-item" :class="{active:active==item.dictId}"
							v-for="item in storeCategoryList" :key='item.dictId' @click="handleSidebar(item.dictId)">
							{{formatCategoryDict(item.dictId)}}
						</view>
						<!-- 分类为空时显示 -->
						<view v-if="storeCategoryList.length === 0" class="empty-tip">
							暂无分类数据
						</view>
					</view>

					<!-- 右侧商品列表 -->
					<view class="store-list">
						<view class="store-item" v-for="store in storeList" :key="store.id"
							@click="handleStore(store.id)">
							<img :src="baseUrl + store.img" alt="商品图片" />
							<view class="detail">
								<view class="name">{{store.name}}</view>
								<view class="sell">月售{{store.order ? store.order : 0}}</view>
							</view>
							<view @click.stop="addCart(store.id)" class="cart">
								<u-icon color='#f39800' size='80' name="plus-circle-fill"></u-icon>
							</view>
						</view>
						<!-- 商品为空时显示 -->
						<u-empty :show="storeList.length <= 0" mode="list" text='暂无商品'></u-empty>
					</view>
				</view>
			</view>

			<!-- 评价区域 -->
			<view class="evaluate" v-show="scroll === 1">
				<view class="star">
					<view class="shop-star">
						<view class="count">
							<view class="count">{{shop.shopStar ? shop.shopStar : 0}}</view>
							<view class="title">商家评分</view>
						</view>
						<view class="star_">
							<u-rate size="50" disabled allowHalf active-color="#f39800" inactive-color="#f39800"
								:count="5" :value="Math.round(shop.shopStar || 0)"></u-rate>
						</view>
					</view>
					<view class="peiSong-star">
						<view class="count">
							<view class="count">{{shop.deliveryStar ? shop.deliveryStar : 0}}</view>
							<view class="title">配送评分</view>
						</view>
					</view>
				</view>

				<view class="evaluate-list">
					<view class="category">
						<view class="category-item" :class="{active:commentActive===null}" @click="commentActive=null">
							<view class="title">全部({{commentCount.allCount || 0}})</view>
						</view>
						<view class="category-item" :class="{active:commentActive===1}" @click="commentActive=1">
							<view class="title">好评({{commentCount.goodCount || 0}})</view>
						</view>
						<view class="category-item" :class="{active:commentActive===2}" @click="commentActive=2">
							<view class="title">中评({{commentCount.normalCount || 0}})</view>
						</view>
						<view class="category-item" :class="{active:commentActive===3}" @click="commentActive=3">
							<view class="title">差评({{commentCount.badCount || 0}})</view>
						</view>
					</view>

					<view class="evaluate-item" v-for="item in evaluateList" :key="item.id">
						<view class="name">{{item.userName || '匿名用户'}}</view>
						<view class="_star">
							<text style="color: #b6b6b6;margin-right: 20rpx;">商家评分</text>
							<u-rate disabled allowHalf active-color="#f39800" inactive-color="#f39800" :count="5"
								:value="Math.round(item.shopStar || 0)"></u-rate>
							<text style="color: red;">{{item.shopStar || 0}}分</text>
						</view>
						<view class="comment">{{item.comment || '无评价内容'}}</view>
						<view class="imgs" v-if="item.img && item.img !== ''">
							<img @click="openImg(img)" :src="baseUrl + img" alt="评价图片"
								v-for="(img, index) in item.img.split(',')" :key='index' />
						</view>
					</view>

					<!-- 评价为空时显示 -->
					<u-empty v-if="evaluateList.length === 0" mode="comment" text='暂无评价'></u-empty>
				</view>
			</view>

			<!-- 店铺信息区域 -->
			<view class="about" v-show="scroll === 2">
				<view class="notice">
					<view class="head">
						<view class="icon">
							<u-icon name="volume-fill" color='#fff' size='44'></u-icon>
						</view>
						<view class="title">公告:</view>
					</view>
					<view class="about-content">{{shop.notice ? shop.notice : '暂无公告'}}</view>
				</view>

				<view class="detail">
					<view class="detail-item">
						<view class="title">手机号</view>
						<view class="data">{{shop.phone || '未填写'}}</view>
					</view>
					<view class="detail-item">
						<view class="title">联系人</view>
						<view class="data">{{shop.contacts || '未填写'}}</view>
					</view>
					<view class="detail-item">
						<view class="title">店铺名称</view>
						<view class="data">{{shop.name || '未填写'}}</view>
					</view>
					<view class="detail-item">
						<view class="title">店铺类目</view>
						<view class="data">{{formatCategory(shop.storeShopClsId)}}</view>
					</view>
				</view>

				<view class="img">
					<view class="img-item">
						<view class="title">门店LOGO</view>
						<img :src="baseUrl + shop.logoImg" style="width: 100%;" alt="门店LOGO" />
					</view>
					<view class="img-item">
						<view class="title">门店背景</view>
						<img :src="baseUrl + shop.backImg" style="width: 100%;" alt="门店背景" />
					</view>
					<view class="img-item">
						<view class="title">营业执照</view>
						<img :src="baseUrl + shop.licenseImg" style="width: 100%;" alt="营业执照" />
					</view>
				</view>
			</view>
		</view>

		<!-- 购物车组件 -->
		<shop-detail-cart :isShow="isShow" :store='store' @isShow='isShow=!isShow'></shop-detail-cart>

		<!-- 底部留白 -->
		<view style="height: 130rpx;"></view>
	</view>
</template>

<script>
	import Tabs from '@/components/tabs.vue';
	import {
		baseUrl
	} from '@/api/config';
	import ShopDetailCart from './shop-cart.vue';

	export default {
		name: "shop-detail-main",
		data() {
			return {
				scroll: 0, // 当前激活的标签页索引
				scrollData: [{
						text: "商品"
					},
					{
						text: "评价"
					},
					{
						text: "关于店铺"
					}
				],
				height: 0, // 屏幕高度
				active: null, // 当前选中的分类ID
				shopCategoryList: [], // 店铺类目列表
				storeCategoryList: [], // 商品分类列表
				storeList: [], // 商品列表
				baseUrl, // 图片基础路径
				isShow: false, // 购物车弹窗显示状态
				store: {}, // 当前操作的商品
				commentActive: null, // 评价筛选状态
				commentCount: {}, // 评价统计
				evaluateList: [], // 评价列表
				storeCategoryDict: [] // 分类字典数据
			};
		},
		components: {
			Tabs,
			ShopDetailCart
		},
		watch: {
			// 评价筛选条件变化时重新加载评价
			commentActive: {
				handler() {
					this.getCommentList();
				}
			}
		},
		props: ['shop'], // 接收父组件传入的店铺信息
		mounted() {
			// 初始化数据
			this.initData();
		},
		methods: {
			/**
			 * 初始化所有数据
			 */
			async initData() {
				try {
					// 1. 获取屏幕高度
					const [err, res] = await uni.getSystemInfo();
					if (!err) this.height = res.windowHeight;

					// 2. 按依赖顺序加载数据
					await this.getStoreCategoryDict(); // 先加载分类字典
					await this.getStoreCategory(); // 再加载商品分类

					// 3. 加载其他数据
					this.getShopCategory(); // 店铺类目
					this.getCommentCount(); // 评价统计
					this.getCommentList(); // 评价列表

					// 4. 有分类数据时设置默认选中项
					if (this.storeCategoryList.length > 0) {
						this.active = this.storeCategoryList[0].dictId;
						this.getStore(); // 加载默认分类商品
					}
				} catch (error) {
					console.error("初始化数据失败：", error);
				}
			},

			/**
			 * 打开图片预览
			 */
			openImg(img) {
				uni.previewImage({
					urls: [baseUrl + img.img]
				});
			},

			/**
			 * 切换标签页
			 */
			handleTabs(index) {
				this.scroll = index;
			},

			/**
			 * 切换商品分类
			 */
			handleSidebar(id) {
				this.active = id;
				this.getStore(); // 加载对应分类的商品
			},

			/**
			 * 查看商品详情
			 */
			handleStore(id) {
				uni.navigateTo({
					url: '/pages-zone/pages/shop/store-detail?id=' + id
				});
			},

			/**
			 * 添加商品到购物车
			 */
			addCart(id) {
				this.isShow = true;
				this.store = this.storeList.find(i => i.id === id) || {};
			},

			/**
			 * 获取店铺类目列表
			 */
			getShopCategory() {
				this.$u.api.getShopCategory().then(res => {
					this.shopCategoryList = res.data?.list || [];
				}).catch(err => {
					console.error("获取店铺类目失败：", err);
					this.shopCategoryList = [];
				});
			},

			/**
			 * 获取商品分类列表
			 */
			getStoreCategory() {
				return new Promise((resolve) => {
					this.$u.api.getStoreCategory(this.shop.id).then(res => {
						this.storeCategoryList = res.data?.list || [];
						resolve();
					}).catch(err => {
						console.error("获取商品分类失败：", err);
						this.storeCategoryList = [];
						resolve();
					});
				});
			},

			/**
			 * 获取分类字典数据
			 */
			getStoreCategoryDict() {
				return new Promise((resolve) => {
					this.$u.api.listData({
						pageSize: 99999,
						pageNum: 1,
						dictType: 'shop_store_category'
					}).then(res => {
						this.storeCategoryDict = res.rows || [];
						resolve();
					}).catch(err => {
						console.error("获取分类字典失败：", err);
						this.storeCategoryDict = [];
						resolve();
					});
				});
			},

			/**
			 * 获取商品列表
			 */
			getStore() {
				if (!this.active) return;

				this.$u.api.getStore(this.shop.id, this.active).then(res => {
					this.storeList = res.data?.list || [];
				}).catch(err => {
					console.error("获取商品列表失败：", err);
					this.storeList = [];
				});
			},

			/**
			 * 获取评价统计
			 */
			getCommentCount() {
				this.$u.api.commentCount(this.shop.id).then(res => {
					this.commentCount = res.data || {};
				}).catch(err => {
					console.error("获取评价统计失败：", err);
					this.commentCount = {};
				});
			},

			/**
			 * 获取评价列表
			 */
			getCommentList() {
				this.$u.api.commentList(this.shop.id, this.commentActive).then(res => {
					this.evaluateList = res.data?.list || [];
				}).catch(err => {
					console.error("获取评价列表失败：", err);
					this.evaluateList = [];
				});
			},

			/**
			 * 格式化分类名称
			 */
			formatCategoryDict(id) {
				const item = this.storeCategoryDict.find(item => item.dictCode === id);
				return item ? item.dictLabel : "未知分类";
			},

			/**
			 * 格式化店铺类目
			 */
			formatCategory(id) {
				const item = this.shopCategoryList.find(item => item.id === id);
				return item ? item.name : "未知类目";
			}
		}
	}
</script>

<style scoped lang="scss">
	// 基础样式
	.tabs {
		& ::v-deep .u-tabs {
			background-color: #fff;
			border-bottom: 1px solid #f5f5f5;
		}
	}

	.main {
		margin-top: 16px;
	}

	// 商品区域样式
	.store {
		.sidebar {
			background-color: #f5f5f5;
			display: flex;
			flex-direction: column;
			align-items: center;

			.sidebar-item {
				width: 100%;
				text-align: center;
				height: 100rpx;
				line-height: 100rpx;
				font-size: 36rpx;
				border-bottom: 1px solid #e5e5e5;

				&.active {
					background-color: #fff;
					color: #f39800;
					font-weight: 500;
				}
			}

			.empty-tip {
				text-align: center;
				padding: 30rpx;
				color: #999;
				font-size: 28rpx;
				margin-top: 20rpx;
			}
		}

		.store-list {
			overflow: auto;
			background-color: #fff;

			.store-item {
				display: flex;
				padding: 20rpx;
				border-bottom: 1px solid #f5f5f5;

				img {
					width: 36%;
					height: 180rpx;
					object-fit: cover;
					border-radius: 10rpx;
				}

				.detail {
					width: 44%;
					padding: 10rpx;
					display: flex;
					flex-direction: column;
					justify-content: center;

					.name {
						font-size: 40rpx;
						margin-bottom: 10rpx;
						white-space: nowrap;
						overflow: hidden;
						text-overflow: ellipsis;
					}

					.sell {
						color: #959595;
						font-size: 28rpx;
					}
				}

				.cart {
					width: 20%;
					display: flex;
					align-items: flex-end;
					justify-content: center;
					padding-bottom: 20rpx;
				}
			}
		}
	}

	// 评价区域样式
	.evaluate {
		.star {
			display: grid;
			grid-template-columns: 70% 30%;
			height: 200rpx;
			padding: 10px;
			background: #fff;
			margin-bottom: 20rpx;
			border-bottom: 1px solid #f5f5f5;

			.shop-star,
			.peiSong-star {
				display: flex;
				justify-content: space-around;
				align-items: center;
				border-right: 1px solid #d9d9d9;

				.count {
					display: flex;
					flex-direction: column;
					align-items: center;

					.count {
						font-size: 56rpx;
						font-weight: 800;
						color: #f7b754;
					}

					.title {
						font-size: 32rpx;
						color: #a8a8a8;
					}
				}

				.star_ {
					display: flex;
					flex-direction: column;
					align-items: center;
				}
			}

			.peiSong-star {
				border: none;

				.count {
					.count {
						font-size: 56rpx;
						font-weight: 800;
						color: #737373;
					}
				}
			}
		}

		.evaluate-list {
			padding: 20rpx 30rpx;
			background: #fff;

			.category {
				display: flex;
				flex-wrap: wrap;
				margin-bottom: 30rpx;

				.category-item {
					border: #abacac 1rpx solid;
					background: #fbfdfd;
					margin-right: 20rpx;
					margin-bottom: 15rpx;
					padding: 10rpx 10rpx;
					font-size: 32rpx;

					&.active {
						background: #fdf2e2;
						border: #f5d3a0 1rpx solid;
						color: #f39800;
					}
				}
			}

			.evaluate-item {
				padding: 30rpx 0;
				border-bottom: 1px solid #f5f5f5;

				&:last-child {
					border-bottom: none;
				}

				.name {
					font-size: 40rpx;
					margin-bottom: 30rpx;
					color: #333;
					font-weight: 500;
				}

				._star {
					margin-bottom: 30rpx;
					display: flex;
					align-items: center;
				}

				.comment {
					font-size: 36rpx;
					margin-bottom: 30rpx;
					line-height: 1.5;
					color: #666;
				}

				.imgs {
					display: grid;
					grid-template-columns: 1fr 1fr 1fr;
					gap: 10rpx;

					img {
						border-radius: 20rpx;
						width: 100%;
						height: 200rpx;
						object-fit: cover;
					}
				}
			}
		}
	}

	// 店铺信息区域样式
	.about {

		.notice,
		.detail {
			background-color: #fff;
			border-radius: 10rpx;
			width: 90%;
			margin: 0 auto;
			margin-bottom: 20rpx;
			box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
		}

		.notice {
			padding: 40rpx 20rpx;

			.head {
				display: flex;
				align-items: center;
				padding-bottom: 10rpx;
				border-bottom: 1px solid #e5e5e5;
				margin-bottom: 20rpx;

				.icon {
					background: #f39800;
					border-radius: 50%;
					width: 60rpx;
					height: 60rpx;
					display: flex;
					justify-content: center;
					align-items: center;
					margin-right: 20rpx;
				}

				.title {
					font-size: 50rpx;
					font-weight: 800;
				}
			}

			.about-content {
				color: #a3a3a3;
				font-size: 36rpx;
				line-height: 1.5;
				padding: 10rpx;
			}
		}

		.detail {
			.detail-item {
				display: flex;
				justify-content: space-between;
				align-items: center;
				height: 80rpx;
				font-size: 34rpx;
				padding: 0 30rpx;
				border-bottom: 1px solid #f8f8f8;

				&:last-child {
					border-bottom: none;
				}

				.title {
					color: #999;
				}

				.data {
					color: #333;
				}
			}
		}

		.img {
			width: 90%;
			margin: 0 auto;
			display: grid;
			grid-template-columns: 1fr 1fr;
			gap: 20rpx;

			.img-item {
				background: #fff;
				border-radius: 10rpx;
				overflow: hidden;
				box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);

				.title {
					padding: 20rpx;
					font-size: 34rpx;
					color: #666;
					border-bottom: 1px solid #f5f5f5;
				}

				img {
					width: 100%;
					height: 300rpx;
					object-fit: cover;
				}
			}
		}
	}
</style>