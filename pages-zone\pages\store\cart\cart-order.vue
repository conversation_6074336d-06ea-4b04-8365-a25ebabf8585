<template>
	<view class='shop-cart-order'>
		<navbar title="确认下单"></navbar>
		<view class="detail">
			<view class="tabs">
				<view class="jiashi" :class='{active:active==="jiashi"}' @click='active="jiashi"'>
					家事快送
				</view>
				<view class="ziti" :class='{active:active==="ziti"}' @click='active="ziti"'>
					门店自提
				</view>
			</view>
			<view class="jiashi" v-show="active=='jiashi'">
				<u-form-item label-width="130rpx" required prop='region' label="社区地址">
					<u-input disabled @click="toAddress" type='select' v-model="form.region" placeholder="请选择社区地址" />
				</u-form-item>
				<u-form-item label-width="130rpx" required prop='address' label="详情地址">
					<u-input disabled v-model="form.address" placeholder="请填写详情地址" />
				</u-form-item>
				<u-form-item label-width="130rpx" required prop='consignee' label="联系人">
					<u-input class="consignee" disabled v-model="form.consignee" placeholder="请填写联系人姓名" />
					<u-radio-group placement="row" class="sex" v-model="form.sex" active-color="#f2ca8a">
						<u-radio name="1">先生</u-radio>
						<u-radio name="0">女士</u-radio>
					</u-radio-group>
				</u-form-item>
				<u-form-item label-width="130rpx" required prop='mobile' label="联系电话">
					<u-input disabled v-model="form.mobile" placeholder="请填写联系电话" />
				</u-form-item>
			</view>
			<view class="ziti" v-show="active=='ziti'">
				<view class="xx">
					<view class="address">
						{{shop.address}}{{shop.name}}
					</view>
					<view class="img">
						<img style="width: 160rpx;height: 160rpx;" :src="baseUrl + orderList[0].img" alt="" />
					</view>
				</view>
				<view class="phone">
					<u-form-item label-width="180rpx" required prop='phone' label="联系电话">
						<u-input v-model="form.phoneNum" placeholder="请填写联系电话" />
					</u-form-item>
				</view>
			</view>
		</view>
		<view class="orderList">
			<view class="order-item" v-for="item in orderList" :key="item.id">
				<view class="img">
					<img style="width: 100%;height: 120rpx;" :src="baseUrl + item.img" alt="" />
				</view>
				<view class="detail">
					<view class="name">
						{{item.name}}
					</view>
					<view class="price">
						￥{{item.price}}
					</view>
				</view>
				<view class="input">
					<view class='jian' type="warning" shape="circle" @click="handleJian(item)">-</view>
					<u-input :clearable="false" class="number" type='number' v-model="item.count"
						placeholder=""></u-input>
					<view class='add' type="warning" shape="circle" @click="handleAdd(item)">+</view>
				</view>
			</view>
		</view>
		<view class="detail">
			<view class="detail-item">
				<view class="title">商品总价</view>
				<view class="data">￥{{total}}</view>
			</view>
			<!-- <view class="detail-item">
				<view class="title">商品打包费</view>
				<view class="data">￥0.00</view>
			</view> -->
			<!-- <view class="detail-item">
				<view class="title">商家打包费</view>
				<view class="data">￥0</view>
			</view> -->
			<!-- <view class="detail-item">
				<view class="title">合计打包费</view>
				<view class="data">￥0.00</view>
			</view> -->
			<view class="detail-item" v-if="active=='jiashi'">
				<view class="title">商家配送费</view>
				<view class="data">￥{{shop.peiSong}}</view>
			</view>
		</view>
		<view class="detail" v-if="active=='jiashi'">
			<view class="detail-item">
				<view class="title">商家起送价</view>
				<view class="data">￥{{shop.qiSong}}</view>
			</view>
		</view>
		<view class="detail">
			<view class="detail-item time">
				<view class="title">{{ active=='jiashi' ? '期望送达时间' : '预计取货时间'}}</view>
				<uni-datetime-picker placeholder="请选择>" type="datetime" v-model="form.reservationTime" />
			</view>
		</view>
		<view class="detail">
			<view class="detail-item">
				<view class="title">优惠券</view>
				<view @click="couponActive = true" class="data">
					{{couponId ? "已选优惠券" : "请选择优惠券 >"}}
					<view class="close" @click.stop="couponId = null" v-if="couponId">x</view>
				</view>
			</view>
		</view>
		<cart-coupon @select="selectCoupon" :isShow="couponActive" @isShow='couponActive=!couponActive' />
		<view class="detail">
			<u-input v-model="form.remark" :clearable='false' :custom-style="{padding: '20rpx'}" :height='200'
				type="textarea" placeholder='如有特殊要求,请给商家带句话' />
		</view>
		<view v-if="couponActive" class="coupon">

		</view>
		<view style="height: 240rpx;">

		</view>
		<view class="payInfo">
			<view class="total">
				合计：<text>￥{{totalAll}}</text>
			</view>
			<!-- 						<view class="pay" @click="pay">
				立刻支付
			</view> -->
			<view class="pay" @click="pay">
				立刻预约
			</view>
		</view>
	</view>
</template>

<script>
	import CartCoupon from './cart-coupon.vue';
	import {
		baseUrl
	} from '@/api/config';
	export default {
		data() {
			return {
				orderList: [],
				baseUrl: baseUrl,
				form: {
					remark: '',
					region: "",
					address: "",
					consignee: "",
					sex: "",
					mobile: "",
					phoneNum: this.$store.state.user.userInfo.phonenumber
				},
				active: 'jiashi',
				couponActive: false,
				couponId: null,
				couponAmount: 0,
				shop: {}
			}
		},
		components: {
			CartCoupon
		},
		onLoad({
			list,
			shopId
		}) {
			this.orderList = JSON.parse(decodeURIComponent(list))
			this.$u.api.getShopDetail(shopId).then(res => {
				this.shop = res.data
			})
		},
		computed: {
			total() {
				let sum = 0
				this.orderList.forEach(item => {
					sum += item.price * 1.0 * item.count
				})
				return sum.toFixed(2)
			},
			totalAll() {
				if (this.couponId) {
					return this.total - this.couponAmount + (this.active === "ziti" ? 0 : this.shop.peiSong)
				}
				return parseFloat(this.total) + parseInt(this.active === "ziti" ? 0 : this.shop.peiSong)
			},
			// shop() {
			// 	return this.$store.state.shop.shop
			// }
		},
		methods: {
			selectCoupon({
				instanceId,
				minUseAmount,
				amount
			}) {
				if (minUseAmount <= this.total) {
					this.couponActive = false
					this.couponId = instanceId
					this.couponAmount = amount
				} else uni.$u.toast('金额未达到优惠券门槛')
			},
			pay() {
				if (this.active == 'jiashi') {
					this.form.deliveryFlag = 0
					if (!this.form.addressId) {
						uni.$u.toast('请选择收货地址')
						return
					}
				} else if (this.active == 'ziti') {
					this.form.shopId = this.shop.id
					this.form.region = null
					this.form.address = null
					this.form.consignee = null
					this.form.sex = null
					this.form.addressId = null
					this.form.deliveryFlag = 1
				}
				// if(!this.form.reservationTime){
				// 	uni.$u.toast('请选择送达时间')
				// 	return
				// }
				if (this.total < this.shop.qiSong) {
					uni.$u.toast('商品金额未到达起送价')
					return
				}
				let obj = {}
				this.orderList.forEach(item => {
					obj[item.id] = item.count
				})
				this.form.couponId = this.couponId
				this.form.shoppingCartIdsAndCounts = obj
				this.form.totalAmount = this.totalAll
				console.log(this.totalAll, 'this.totalAll')
				console.log(this.form, 'this.form')
				// store/order/add
				this.$u.api.readyOrder({
					...this.form,
					storeShopId: this.$store.state.shop.shop.id
				}).then(res => {
					// console.log(res.data.data,'订单orderId')
					// uni.$u.toast('预约成功')
					// setTimeout(() => {
					// 	uni.navigateBack()
					// }, 1500)
					// store/order/pay
					this.$u.api.submitOrder(res.data.data).then(() => {
						uni.$u.toast('预约成功')
						setTimeout(() => {
							uni.redirectTo({
								url: '/pages-zone/pages/store/merchant/merchant?navbarIndex=2'
							})
						}, 1500)

					})
				})
			},
			toAddress() {
				uni.navigateTo({
					url: '/pages-mine/pages/address/list?isSelect=' + true
				})
			},
			getAddressId(id) {
				this.$u.api.detailAddress(id).then(res => {
					this.form.region = res.data.region
					this.form.address = res.data.address
					this.form.consignee = res.data.consignee
					this.form.sex = res.data.sex
					this.form.mobile = res.data.mobile
					this.form.addressId = id
				})
			},
			handleAdd(item) {
				item.count++
			},
			handleJian(item) {
				if (item.count > 1) item.count--
			}
		}
	}
</script>

<style scoped lang="scss">
	.orderList {
		width: 90%;
		margin: 0 auto;
		background: #ffffff;
		border-radius: 30rpx;
		margin-top: 20rpx;

		.order-item {
			display: grid;
			padding: 30rpx;
			grid-template-columns: 20% 40% 40%;
			border-bottom: 1px solid #dcdcdc;

			&:last-child {
				border-bottom: none;
			}

			.img {
				display: flex;
				align-items: center;
			}

			.detail {
				display: flex;
				flex-direction: column;
				justify-content: space-evenly;

				.price {
					color: red;
				}
			}

			.input {
				display: flex;
				align-items: flex-end;

				.number {
					width: 160rpx;
					background: #ebecee;
					margin: 0 10rpx;
					height: 50rpx;
					display: flex;
					align-items: center;

					& ::v-deep input {
						text-align: center;
						font-size: 40rpx;
					}
				}

				.add,
				.jian {
					background: #ebecee;
					border-radius: 10rpx;
					font-size: 50rpx;
					width: 60rpx;
					height: 50rpx;
					display: flex;
					align-items: center;
					justify-content: center;
				}
			}
		}
	}

	.detail {
		width: 90%;
		margin: 0 auto;
		background: #ffffff;
		border-radius: 20rpx;
		margin-top: 20rpx;

		.tabs {
			display: flex;
			justify-content: space-around;
			align-items: center;
			height: 100rpx;
			font-size: 40rpx;
			font-weight: 700;
			color: #999999;

			.jiashi,
			.ziti {
				background: #ededed;
				width: 100%;
				height: 100%;
				display: flex;
				align-items: center;
				justify-content: center;

				&.active {
					background: #fff;
				}
			}

			.jiashi {
				border-radius: 30rpx 0 30rpx 0;
			}

			.ziti {
				border-radius: 0 30rpx 0 30rpx;


			}
		}

		.jiashi {
			padding: 40rpx;

			.consignee {
				display: inline-block;
				width: 50%;

			}

			.sex {
				position: absolute;
				right: -10px;
				top: 15px;
			}
		}

		.ziti {
			padding: 40rpx;
			font-size: 40rpx;
			font-weight: 700;

			.xx {
				display: flex;
				justify-content: space-between;
			}
		}

		.detail-item {
			display: flex;
			justify-content: space-between;
			align-items: center;
			height: 80rpx;
			font-size: 34rpx;
			padding: 0 30rpx;
			border-bottom: 1px solid #f8f8f8;

			&:last-child {
				border-bottom: none;
			}

			&.time ::v-deep .uni-date-x--border {
				border: none;
			}

			&.time ::v-deep .uni-date__x-input {
				text-align: end;
			}

			&.time ::v-deep .uni-calendar__content {
				z-index: 10001;
			}

			.close {
				display: inline-block;
				width: 40rpx;
				height: 40rpx;
				background: #c0c4cc;
				border-radius: 50%;
				text-align: center;
				line-height: 40rpx;
				color: #fff;
			}
		}
	}

	.payInfo {
		position: fixed;
		bottom: 0;
		width: 100%;
		background: #ffffff;
		height: 240rpx;
		display: flex;
		justify-content: space-between;
		padding: 30rpx 20rpx;
		z-index: 1000;

		.total {
			font-size: 40rpx;

			text {
				font-size: 60rpx;
				color: red;
			}
		}

		.pay {
			height: 100rpx;
			width: 200rpx;
			background: #f9ae3d;
			border-radius: 10rpx;
			line-height: 100rpx;
			font-size: 36rpx;
			text-align: center;
			color: #fff;
			margin-top: 10rpx;
		}
	}
</style>