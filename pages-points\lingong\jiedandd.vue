<template>
	<view class="page">
		<!-- 自定义头部 -->
		<Navbar title="产品订单" />

		<!-- Tab 导航 -->
		<view class="tabs">
			<view v-for="(tab, idx) in tabOps" :key="idx" :class="['tab', { active: currentTabIndex === idx }]"
				@click="changeTab(idx)">
				{{ tab.label }}
			</view>
		</view>

		<!-- 订单列表 -->
		<scroll-view class="list" scroll-y>
			<view v-if="filteredList.length === 0" class="no-data">暂无订单</view>

			<!-- 必须 v-for 遍历订单 -->
			<view v-else>
				<view v-for="order in filteredList" :key="order.id" class="order-item">

					<!-- 第一套模板（orderType = 1、2、3，对应 deliveryAddress 和 deliveryTime 不为空） -->
					<template v-if="order.orderType !== 0">
						<!-- 顶部：类型 + 状态 -->
						<view class="order-header">
							<text class="order-type">{{ orderTypeText(order.orderType) }}</text>
							<text class="order-status">{{ statusText(order.status) }}</text>
						</view>

						<!-- 地址信息 -->
						<view class="address-section">
							<view class="address-row">
								<view class="address-left">
									<image class="icon icon-clsaa" :src="quicon" mode="widthFix" />
									<text class="address-text">{{ order.pickupAddress || '取件地址未知' }}</text>
									<!-- <button class="btn btn-sty" @click="listcancelOrder(order)">取消订单</button> -->
								</view>
								<text v-if="order.status === 'PENDING_PAYMENT'" class="amount">¥{{ order.price }}</text>
							</view>
							<view class="address-row-bot">
								<image class="icon icon-clsaa" :src="songicon" mode="widthFix" />
								<text class="address-text">{{ order.deliveryAddress || '配送地址未知' }}</text>
							</view>
						</view>

						<!-- 时间 -->
						<view class="order-footer">
							<text class="address-text ">服务时间：<text
									style="font-size: 19rpx;">{{ order.serviceTime || '未指定配送时间' }}</text></text>
						</view>
					</template>

					<!-- 第二套模板（orderType = 0，服务类） -->
					<template v-else>
						<!-- 顶部：类型 + 状态 -->
						<view class="order-header">
							<text class="order-type">{{ orderTypeText(order.orderType) }}</text>
							<text class="order-status">{{ statusText(order.status) }}</text>
						</view>

						<!-- 地址信息 -->
						<view class="address-section">
							<view class="address-row">
								<view class="address-left">
									<text class="address-text">订单类型：{{ order.serviceType || '无' }}</text>
									<!-- <button class="btn btn-sty" @click="listcancelOrder(order)">取消订单</button> -->
								</view>
								<text v-if="order.status === 'WAITING'" class="amount">¥{{ order.price }}</text>

							</view>
							<view class="address-row-bot">
								<!-- <image class="icon icon-clsaa" src="@/static/img/songicon.png" mode="widthFix" /> -->
								<text class="address-text">服务地址：{{ order.serviceAddress || '无' }}</text>
							</view>
							<view class="address-row-bot">
								<!-- <image class="icon icon-clsaa" src="@/static/img/songicon.png" mode="widthFix" /> -->
								<text class="address-text">服务说明：{{ order.requirementText || '无' }}</text>
							</view>
						</view>

						<!-- 时间 -->
						<view class="order-footer">
							<text class="address-text">服务时间：<text
									style="font-size: 19rpx;">{{ order.serviceTime || '未指定配送时间' }}</text></text>
						</view>
					</template>

					<!-- 公共按钮组（待支付） -->
					<!--      <view v-if="order.status === 'PENDING_PAYMENT'" class="btn-group">
         <button class="btn btn-cancel" @click="cancelOrder(order)">取消订单</button>
         <button class="btn btn-pay" @click="payOrder(order)">去支付</button>
       </view> -->
					<view class="btn-group">
						<button v-if="order.status !== 'COMPLETED' && order.status !== 'CANCELLED'" class="btn btn-pay"
							@click="listcancelOrder(order)">完成订单</button>
<!-- 						<button v-if="order.status === 'COMPLETED'" class="btn btn-pay"
							@click="payOrder(order)">去支付</button> -->
							<button v-if="order.status === 'COMPLETED'" class="btn btn-pay"
								@click="payOrder(order)">去预约</button>
					</view>

				</view>
			</view>
		</scroll-view>

	</view>
</template>

<script>
	import {
		HTTP_URL_PROD
	} from '@/api/config';
	export default {
		data() {
			return {
				songicon: HTTP_URL_PROD +
					"/profile/upload/2025/05/22/2K1jiPIQUCuH8837fa419f9eecb545d1cc0bc331fbf7_20250522153902A077.png",
				quicon: HTTP_URL_PROD +
					"/profile/upload/2025/05/22/WmFTI2YMTHvqeb5de89c4b8c8d29437bdd70e3a24059_20250522153846A076.png",
				userId: '',
				tabOps: [{
						label: '新任务',
						value: ''
					},
					{
						label: '待取货',
						value: 'ACCEPTED'
					},
					{
						label: '服务中',
						value: 'IN_SERVICE'
					},
					{
						label: '已完成',
						value: 'COMPLETED'
					},
					{
						label: '已取消',
						value: 'CANCELLED'
					},
				],
				currentTabIndex: 0,
				pickupIcon: '/static/icons/pickup.png', // 换成你项目里的路径
				deliveryIcon: '/static/icons/delivery.png', // 换成你项目里的路径
				pickupAddress: '',
				deliveryAddress: '',
				serviceTime: '',
				serviceAddress: "",
				serviceType: '',
				allOrders: []
			};
		},
		computed: {
			filteredList() {
				const key = this.tabOps[this.currentTabIndex].value;
				return key ? this.allOrders.filter(o => o.status === key) : this.allOrders;
			},

		},
		methods: {
			// 订单类型文本
			orderTypeText(orderType) {
				const map = {
					0: '代办',
					1: '代取',
					2: '代买商品',
					3: '代送'
				};
				return map[orderType] || '未知类型';
			},
			changeTab(idx) {
				this.currentTabIndex = idx;
			},
			statusText(status) {
				const map = {
					ACCEPTED: '待取货',
					IN_SERVICE: '服务中',
					COMPLETED: '已完成',
					CANCELLED: '已取消',
				};
				return map[status] || '未知';
			},
			cancelOrder(order) {
				console.log('取消订单', order);
			},
			payOrder(order) {
				// uni.showModal({
				// 	title: '提示',
				// 	content: '是否支付订单？',
				// 	cancelText: '取消',
				// 	confirmText: '是',
				// 	success: res => {
				// 		if (res.confirm) {
				// 			uni.showToast({
				// 				title: '支付成功'
				// 			});
				// 			setTimeout(() => {
				// 				uni.navigateBack(-1)
				// 			}, 1000);
				// 		} else if (res.cancel) {
				// 			uni.showToast({
				// 				title: '已取消支付',
				// 				icon: 'none'
				// 			});
				// 			setTimeout(() => {
				// 				uni.navigateBack(-1)
				// 			}, 1000);
				// 		}
				// 	}
				// });
				uni.showModal({
					title: '提示',
					content: '是否预约订单？',
					cancelText: '取消',
					confirmText: '是',
					success: res => {
						if (res.confirm) {
							uni.showToast({
								title: '预约成功'
							});
							setTimeout(() => {
								uni.navigateBack(-1)
							}, 1000);
						} else if (res.cancel) {
							uni.showToast({
								title: '已取消预约',
								icon: 'none'
							});
							setTimeout(() => {
								uni.navigateBack(-1)
							}, 1000);
						}
					}
				});
			},
			fetchOrders() {
				if (!this.userId) {
					console.warn('用户ID未获取到');
					return;
				}
				const status = this.tabOps[this.currentTabIndex].value;
				const params = {
					deliveryUserId: this.userId,
					status: status
				};
				this.$u.api.getOrderslist(params)
					.then(res => {
						console.log('接口返回:', res);
						this.allOrders = res.data || [];
					})
					.catch(err => {
						console.error('获取订单列表失败:', err);
					});
			},
			// 取消订单
			listcancelOrder(order) {
				// const current = order.status;
				//    console.log('当前 order.status =', current);
				uni.showModal({
					title: '确认',
					content: '是否完成订单？',
					success: res => {
						if (!res.confirm) return;

						const payload = {
							id: order.id,
							orderType: order.orderType,
							status: 'COMPLETED'
						};

						console.log('👉 取消订单 payload:', payload);

						this.$u.api.ListupdateOrder(payload)
							.then(() => {
								uni.showToast({
									title: '已取消'
								});
								this.fetchOrders();
							})
							.catch(err => {
								uni.showToast({
									title: '取消失败',
									icon: 'none'
								});
								console.error('取消订单失败', err);
							});
					}
				});
			},

			// 原有的 fetchOrders、statusText、orderTypeText 等方法…

		},
		mounted() {
			const userId = this.$store.state.user.userInfo.userId;
			this.userId = userId; // 这里一定要赋值！
			// const userId = uni.getStorageSync('userInfo').userId
			console.log('当前用户的ID1234:', userId);
			this.fetchOrders()
		}

	};
</script>

<style lang="scss" scoped>
	.page {
		display: flex;
		flex-direction: column;
		height: 100%;
		background: #f5f5f5;
	}

	.tabs {
		justify-content: space-between;
		display: flex;
		flex-wrap: nowrap;
		/* 不换行 */
		overflow-x: auto;
		/* 横向可滚动 */
		background: #fff;
		border-bottom: 1px solid #eee;
		/* 隐藏滚动条（如果你想要更干净） */
		-webkit-overflow-scrolling: touch;
		/* 滑动流畅 */
	}

	.tabs::-webkit-scrollbar {
		display: none;
		/* 隐藏滚动条（可选） */
	}

	.tab {
		flex-shrink: 0;
		/* 防止 tab 被压缩 */
		padding: 0 20rpx;
		line-height: 80rpx;
		font-size: 28rpx;
		color: #666;
	}

	.tab.active {
		color: #f00;
		font-weight: bold;
		border-bottom: 4rpx solid #f00;
	}


	.list {
		flex: 1;
		// padding: 20rpx;
	}

	.order-item {
		margin: 10px;
		margin-top: 15px;
		position: relative; // <-- 加这一行！
		background: #fff;
		border-radius: 16rpx;
		padding: 20rpx;
		margin-bottom: 30rpx;
	}


	.order-header {
		display: flex;
		justify-content: space-between;
		font-size: 30rpx;
		margin-bottom: 20rpx;
		font-weight: bold;
	}

	.icon-clsaa {
		width: 22px;
		height: 22px;
	}

	.order-type {
		color: #333;
	}

	.order-status {
		color: #ead219;
	}

	.address-section {
		margin-bottom: 20rpx;
	}

	.address-row {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 20rpx;
	}

	.address-row-bot {
		display: flex;
		// justify-content: space-between;
		align-items: center;
		margin-bottom: 20rpx;
	}

	.address-left {
		display: flex;
		justify-content: space-between;
		align-items: center;
	}

	.icon {
		width: 32rpx;
		height: 32rpx;
		margin-right: 10rpx;
	}

	.address-text {
		font-size: 28rpx;
		color: #333;
	}

	.amount {
		font-size: 32rpx;
		color: #f44; // 红色金额
		margin-left: 20rpx;
		white-space: nowrap;
		font-weight: 700;
	}

	.btn-group {
		display: flex;
		justify-content: flex-end;
		margin-top: 20rpx;
		position: absolute;
		right: 20rpx;
		bottom: 20rpx;
	}

	.btn {
		padding: 10rpx 20rpx;
		margin-left: 20rpx;
		border-radius: 30rpx;
		font-size: 26rpx;
		line-height: 40rpx;
		white-space: nowrap;
	}

	.btn-cancel {
		background: #f5f5f5;
		color: #333;
	}

	.btn-pay {
		background: #f00;
		color: #fff;
	}

	.no-data {
		text-align: center;
		margin-top: 100rpx;
		color: #999;
	}

	.icon {
		margin-right: 10px;
		font-size: 12px;
	}

	.time {
		font-size: 12px;
		color: #999;

	}

	.btn-sty {
		background-color: #f00;
		color: #eee;
	}
</style>