<template>
	<view v-show="isShow" class="shop-detail-cart">
		<view class="back" @click="handleShow"></view>
		<view class="cart" v-if="store">
			<view class="img">
				<img style="width: 150rpx;height: 180rpx;" :src="baseUrl + store.img" alt="" />
			</view>
			<view class="detail">
				<view class="price">
					￥{{store.price}}/份
				</view>
				<view class="name">
					已选 {{store.name}}
				</view>
			</view>
		</view>
		<view class="count">
			<view class="title">购买数量</view>
			<view class="input">
				<view class='jian' type="warning" shape="circle" @click="()=>{if(number>1) number--}">-</view>
				<u-input :clearable="false" class="number" type='number' v-model="number" placeholder=" "></u-input>
				<view class='add' type="warning" shape="circle" @click="number++">+</view>
			</view>
		</view>
		<view class="button">
			<u-button class='button' type="warning" shape="circle" @click="submit"><text>加入购物车</text></u-button>
		</view>
		<view @click="handleShow" class="close_">
			<u-icon name="close"></u-icon>
		</view>
	</view>
</template>

<script>
	import {
		baseUrl
	} from '@/api/config'
	import store from '@/store'
	export default {
		data() {
			return {
				number: 1,
				baseUrl,
				cartList: []
			}
		},
		props: {
			isShow: {
				type: Boolean,
			},
			store: {
				type: Object,
			}
		},
		computed: {
			shop() {
				return this.$store.state.shop.shop
			}
		},
		methods: {
			handleShow() {
				this.$emit('isShow')
			},
			submit() {
				if(this.number < 0 || !this.number){
					uni.$u.toast('购买数量需大于0')
					return
				}
				let cart = this.cartList.find(i => i.shopName == this.shop.name)?.cartList.find(i => i.storeId == this
					.store.id)
				if (cart) {
					this.$u.api.updateCart({
						count: this.number + cart.count,
						id: cart.id
					}).then(res => {
						this.$emit('isShow')
						this.getCart()
					})
				} else {
					this.$u.api.addCart({
						count: this.number,
						storeStoreId: this.store.id
					}).then(res => {
						this.$emit('isShow')
						this.getCart()
					})
				}
			},
			getCartCount() {
				this.$u.api.getCartCount().then(res => {
					this.$store.commit('cart/SET_COUNT', res.data)
				})
			},
			getCart() {
				if (this.$store.state.user.token) {
					this.$u.api.getCart().then(res => {
						res.data.forEach(item => {
							item.cartList = item.cartList.map(i => ({
								...i,
								check: true
							}))
						})
						this.cartList = res.data
						this.getCartCount()
					})
				}

			}
		},
		mounted() {
			this.getCart()
		}
	}
</script>

<style scoped lang="scss">
	.shop-detail-cart {
		position: fixed;
		z-index: 1001;
		bottom: 0;
		width: 100%;
		background: #fff;
		height: 430rpx;
		padding-top: 20rpx;

		.back {
			position: fixed;
			top: 0%;
			width: 100%;
			background: rgba(0, 0, 0, .5);
			height: calc(100vh - 400rpx);
			z-index: 100;
		}

		.cart {
			display: flex;

			.img {
				margin: 20rpx;
			}

			.detail {
				display: flex;
				flex-direction: column;
				justify-content: space-evenly;

				.price {
					color: #ff2194;
				}

				.name {
					color: #afb4c5;
				}
			}
		}

		.count {
			display: flex;
			align-items: center;
			height: 80rpx;
			padding: 0 30rpx;
			margin-bottom: 20rpx;
			font-size: 40rpx;
			justify-content: space-between;

			.input {
				display: flex;
				align-items: flex-start;

				.number {
					width: 160rpx;
					background: #ebecee;
					margin: 0 10rpx;
					height: 80rpx;
					display: flex;
					align-items: center;

					& ::v-deep input {
						text-align: center;
						font-size: 40rpx;
					}
				}

				.add,
				.jian {
					background: #ebecee;
					border-radius: 10rpx;
					font-size: 80rpx;
					width: 80rpx;
					height: 80rpx;
					display: flex;
					align-items: center;
					justify-content: center;
				}
			}
		}

		.button {
			height: 80rpx;
			background: #ff976a;
			border-radius: 80rpx;
			width: 94%;
			margin: 0 auto;
			font-size: 36rpx;
		}

		.close_ {
			position: absolute;
			top: 30rpx;
			right: 20rpx;
			font-size: 40rpx;
			color: #909399;
			width: 30rpx;
			height: 30rpx;
		}

	}
</style>