<template>
	<view class="page" style="padding-top: 18vh;">
		<!-- 带tab、search的导航栏 -->
		<NavbarTabSearch :Tid="Tid" ref="NavbarTabSearch" title="服务订单" :tabOps="tabOps" @change="changeTab"
			placeholder="搜索全部订单">
		</NavbarTabSearch>
		<!-- 列表 -->
		<view class="list">
			<OrderCard :Tid="Tid" v-for="(item, index) in orderList" :key="index" :data="item" @change="changeList">
			</OrderCard>
			<!-- <NoData height="60vh" type="order" v-if="orderList.length == 0"></NoData> -->
			<view class="empty-container" v-if="orderList.length === 0">
				<u-empty mode="list" icon="http://cdn.uviewui.com/uview/empty/car.png"></u-empty>
			</view>
		</view>
	</view>
</template>

<script>
	// 组件
	import NavbarTabSearch from '@/pages-mall/components/navbar-tab-search.vue';
	import OrderCard from '@/pages-mall/components/order/order-card.vue';
	// 假数据
	// import {
	// 	orderList
	// } from '@/static/test-data.js';
	export default {
		components: {
			NavbarTabSearch,
			OrderCard
		},
		data() {
			return {
				orderList: [],
				tabOps: null,
				Tid: null,
				// 当前tab
				// tabIndex: 0
			};
		},
		onLoad(ops) {
			console.log(ops, 'ops')
			this.Tid = ops.Tid
			this.tabList(ops)
			// if (ops.tabIndex) {
			// 	this.tabIndex = ops.tabIndex;
			// this.$nextTick(() => {
			// 	this.$refs.NavbarTabSearch.changeTab(Number(ops.tabIndex));
			// });
			// }
		},
		mounted() {
			this.$eventBus.$on('order-status-changed', this.handleOrderChange);
		},
		beforeDestroy() {
			this.$eventBus.$off('order-status-changed', this.handleOrderChange);
		},
		methods: {
			handleOrderChange(payload) {
				console.log('订单状态变化:', payload);
				this.funlist(this.currentTabValue);
			},
			tabList(val) {
				// Tid 1订单 2技师订单
				if (val.Tid == 1) {
					this.tabOps = [{
							name: '待支付',
							value: '1',
						},
						// {
						// 	name: '待预约',
						// 	value: '1',
						// },
						{
							name: '待服务',
							value: '2'
						},
						{
							name: '服务中',
							value: '4'
						},
						{
							name: '已服务',
							value: '5'
						},
						{
							name: '服务完成',
							value: '6'
						},
						{
							name: '用户评价',
							value: '7'
						},
						{
							name: '已取消',
							value: '3'
						}
					]
					this.funlist(1)
				} else if (val.Tid == 2) {
					this.tabOps = [{
							name: '服务中',
							value: '4'
						},
						{
							name: '已服务',
							value: '5'
						},
					]
					this.funlist(4)

				} else {
					this.tabOps = [{
							name: '全部',
							value: ''
						},
						{
							name: '待支付',
							value: '0'
						},
						{
							name: '待发货',
							value: '1'
						},
						{
							name: '待收货',
							value: '2'
						},
						{
							name: '待评价',
							value: '3'
						},
						{
							name: '退款/售后',
							value: '4'
						}
					]
				}

			},
			// 列表数据
			funlist(id) {
				let data = {
					status: id,
					pageSize: 999,
					pageNum: 1
				}
				if (this.Tid == 1) { //服务订单
					data.userId = this.$store.state.user.userInfo.userId
				} else if (this.Tid == 2) { //师傅接单
					data.technicianInformationId = this.$store.state.user.userInfo.userId
				}
				this.$u.api.getorderlist(data).then(res => {
					this.orderList = res.rows;
				})
			},
			// 列表取消
			changeList(e) {
				console.log(e, '取消id')
				this.funlist(e)
			},
			// 切换tab的回调
			changeTab(e) {
				this.funlist(e.mappedIndex)
				// this.tabIndex = index;
				// let arr = [];
				// for (var i = 0; i < orderList.length; i++) {
				// 	if (tab.value == '') {
				// 		arr.push(orderList[i]);
				// 		continue;
				// 	}
				// 	if (orderList[i].status == tab.value) {
				// 		arr.push(orderList[i]);
				// 	}
				// }
				// this.orderList = arr;
			}
		}
	};
</script>

<style lang="scss" scoped>
	.page {
		height: 100vh;
	}

	.list {
		padding: 74rpx 30rpx 30rpx;
	}

	.empty-container {
		display: flex;
		justify-content: center;
		align-items: center;
		height: 70vh;
		/* 设置足够的高度确保垂直居中 */
		padding: 40rpx 0;
	}
</style>