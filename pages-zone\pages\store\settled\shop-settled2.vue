<template>
	<view class="shop-settled">
		<navbar title="邻工入驻"></navbar>
		<!-- 表单 -->
		<view class="form">
			<u-form label-position="left" :model="form" :rules="rules" ref="formRef" label-width="180rpx">
				<view class="form-block">

					<u-form-item required prop='mobile' label="手机号码">
						<u-input v-model="form.mobile" placeholder="请输入手机号码" />
					</u-form-item>
					<u-form-item required prop='name' label="姓名">
						<u-input v-model="form.name" placeholder="请输入姓名" />
					</u-form-item>
					<!-- 性别 -->
					<u-form-item required prop="gender" label="性别">
						<u-radio-group v-model="form.gender">
							<u-radio name="男">男</u-radio>
							<u-radio name="女">女</u-radio>
						</u-radio-group>
					</u-form-item>
					<u-form-item required prop="idCardNumber" label="身份证号">
						<u-input v-model="form.idCardNumber" placeholder="请输入身份证号" />
					</u-form-item>
					<u-form-item required prop='residenceAddress' label="居住地址">
						<u-input v-model="form.residenceAddress" placeholder="请输入居住地址" />
					</u-form-item>
				</view>
				<view class="form-block">
					<u-form-item label="证件照片" :border-bottom="false" labelPosition='top' required>
						<!-- 图片上传 -->
						<view class="upload">
							<!-- 身份证人像面 -->
							<view class="picker-block">
								<text class="picker-label">身份证人像面</text>
								<!-- <img-picker :limit="1" @imglist="imgs => handleImgList('front', imgs)" class="picker" /> -->
								<u-upload ref="uUpload" :header="header" :action="action" :file-list="lifeImages"
									:max-count="1" @on-change="upChangeBack" upload-text="点击上传" />
							</view>
							<!-- 身份证国徽面 -->
							<view class="picker-block">
								<text class="picker-label">身份证国徽面</text>
								<!-- <img-picker :limit="1" @imglist="imgs => handleImgList('back', imgs)" class="picker" /> -->
								<u-upload ref="uUpload" :header="header" :action="action" :file-list="lifeImagesRX"
									:max-count="1" @on-change="upChangeBackRX" upload-text="点击上传" />
							</view>
						</view>
					</u-form-item>
				</view>
			</u-form>
		</view>
		<!-- 按钮 -->
		<view class="btn">
			<u-button class='button' type="warning" shape="circle" @click="submit"><text>提交资料</text></u-button>
		</view>
		<view class="agreement">
			<u-checkbox size='30' v-model='agreement' activeColor='#ffba4d' inactiveColor='#ffba4d'
				@click="agreement = !agreement">
				已阅读并同意<text style="color: #ffba4d;">《家事速配邻工入驻协议》</text>
			</u-checkbox>
		</view>
	</view>
</template>

<script>
	import {
		HTTP_URL_PROD
	} from '@/api/config';
	const token = uni.getStorageSync("App-Token");
	// import { token } from '@/api/modules/shop';
	import imgPicker from '@/components/img-picker.vue'
	export default {
		components: {
			imgPicker
		},
		data() {
			return {
				themeColor: this.$appTheme.appThemeColor,
				// 类型选择
				categoryPicker: false,
				agreement: false,
				// headers: {
				//   token: this.$store.state.user.token
				// },
				action: HTTP_URL_PROD + '/common/upload',
				header: {
					Authorization: token
				},
				lifeImages: [], // 人像
				lifeImagesRX: [], //国徽
				nowSelectedCity: "",
				// 表单
				form: {
					userId: '',
					idCardNumber: '',
					gender: '',
					mobile: '',
					name: '',
					residenceAddress: '',
					idCardFrontUrl: '', // 身份证人像面
					idCardBackUrl: '', // 身份证国徽面
					area: "",

				},
				rules: {
					name: [{
						required: true,
						message: '请输入联系人',
						trigger: ['blur', 'change']
					}],
					mobile: [{
						required: true,
						message: '请输入联系电话',
						trigger: ['blur', 'change']
					}],
					gender: [{
						required: true,
						message: '请选择性别'
					}],
					residenceAddress: [{
						required: true,
						message: '请输入商家地址',
						trigger: ['blur', 'change']
					}],
					idCardNumber: [{
						required: true,
						message: '请输入身份证号'
					}],
				},
				// 上传地址
				// uploadUrl: baseUrl + '/common/upload'
			};
		},
		onReady() {
			this.$refs.formRef.setRules(this.rules)
		},
		methods: {
			// 国徽面上传
			upChangeBack(res, index, lists) {
				this.idCardBackImages = lists
				this.form.idCardFrontUrl = lists[0].response.fileName
				console.log(this.form)
			},
			upChangeBackRX(res, index, lists) {
				this.lifeImagesRX = lists
				this.form.idCardBackUrl = lists[0].response.fileName
				console.log(this.form)
			},

			handleImgList(type, imgs) {
				console.log('接收到的图片列表:', imgs);
				if (!Array.isArray(imgs) || imgs.length === 0) {
					uni.$u.toast('未获取到图片列表');
					return;
				}
				imgs.forEach(fileInfo => {
					const fileName = typeof fileInfo === 'string' ?
						fileInfo :
						(fileInfo.fileName || fileInfo.url || '');
					const imageUrl = this.$imgUrl + fileName;
					if (type === 'front') {
						this.form.idCardFrontUrl = imageUrl;
					} else {
						this.form.idCardBackUrl = imageUrl;
					}
				});
			},
			uploadError(err) {
				console.error('上传失败:', err);
				uni.$u.toast('图片上传失败');
			},
			async submit() {
				// 1. 同意协议校验
				if (!this.agreement) {
					uni.$u.toast('请先同意协议');
					return;
				}

				// 2. 表单校验 + 身份证照片校验
				this.$refs.formRef.validate(valid => {
					if (!valid) return;
					if (!this.form.idCardFrontUrl || !this.form.idCardBackUrl) {
						uni.$u.toast('请上传身份证正反面照片');
						return;
					}

					// 3. 填充 userId 并发请求
					this.form.userId = this.$store.state.user.userInfo.userId;
					this.$u.api.neighborWorker(this.form)
						.then(res => {
							console.log('this.form 返回：', this.form);

							// 4. 如果后端提示“已提交过申请”，只弹窗，不跳转
							if (res.code === 500 && res.msg === '该用户已提交过申请') {
								uni.showModal({
									title: '提示',
									content: res.msg,
									showCancel: false,
									confirmText: '我知道了',
									success: (modalRes) => {
										// console.log(11)
										// uni.switchTab({
										//   url: '/pages/home/<USER>',
										// });
										uni.navigateBack(-1)

									}
								});
								return;
							}

							// 5. 提交成功
							if (res.code === 200) {
								uni.showModal({
									title: '提交成功',
									content: '您的申请已提交，等待审核。',
									showCancel: false,
									confirmText: '确定',
									success: (modalRes) => {
										uni.navigateBack(-1)
									}
								});
								return;
							}

							// 6. 其他异常
							uni.showModal({
								title: '错误',
								content: res.msg || '提交失败，请稍后重试',
								showCancel: false,
								confirmText: '我知道了'
							});
						})
						.catch(err => {
							console.error('neighborWorker 接口错误：', err);
							uni.showModal({
								title: '网络错误',
								content: '提交失败，请检查网络后重试',
								showCancel: false,
								confirmText: '我知道了'
							});
						});
				});
			},


		},
		mounted() {
			this.$nextTick(() => {
				const userId = this.$store.state.user.userInfo.userId;
				const token = this.$store.state.user.token;
				console.log('LG当前用户:', token, userId);
			});
			const storedCommunity = uni.getStorageSync('community');
			if (storedCommunity) {
				this.nowSelectedCity = storedCommunity;
			} else {
				this.nowSelectedCity = this.$store.state.user.locationCom;
			}
			this.form.area = this.nowSelectedCity;
			console.log(this.nowSelectedCity, this.form.area, 'this.nowSelectedCity');

		}

	};
</script>

<style lang="scss" scoped>
	.shop-settled {
		background: #f3f5f7;
		padding: 30rpx;

		.form {

			.form-block {
				background: #fff;
				border-radius: 10rpx;
				margin: 0 auto;
				padding: 0 30rpx;
				margin-bottom: 30rpx;

				.upload {
					display: grid;
					grid-template-columns: 1fr 1fr;
				}
			}
		}
	}

	.btn {
		padding: 60rpx 0rpx;

		.button {
			background: #ffba4d;

			text {
				font-weight: 800;
				color: #000;
				font-size: 40rpx
			}

			&:hover {
				background: #ffba4d;
			}
		}
	}

	.agreement {
		text-align: center;

		& ::v-deep .u-checkbox__icon-wrap {
			border: 1px solid #ffba4d;
		}
	}

	.picker-block {
		flex: 1;
		display: flex;
		flex-direction: column;
		align-items: center;
	}

	.picker-label {
		font-size: 24rpx;
		color: #333;
		margin-bottom: 10rpx;
	}

	.picker {
		// width: 300rpx;
		// height: 300rpx;
		border: 1rpx dashed #e0e0e0;
		border-radius: 10rpx;
		overflow: hidden;
	}

	/* 可选：当已经选择图片时给 picker 加个边框高亮 */
	.picker.has-image {
		border-color: #ffba4d;
	}

	// .form-block {
	//   margin-top: 20rpx;
	//   padding: 20rpx;
	//   background-color: #fff;
	//   border-radius: 10rpx;
	// }

	// .upload {
	//   display: flex;
	//   justify-content: space-between;
	//   flex-direction: row;
	//   gap: 15rpx;
	// }

	// .upload img {
	//   width: 100%;
	//   height: 100%;
	//   object-fit: cover;
	//   border-radius: 10rpx;
	// }

	// .upload .u-upload {
	//   flex: 1;
	//   padding: 10rpx;
	//   border: 1px solid #e0e0e0;
	//   border-radius: 10rpx;
	//   background-color: #f9f9f9;
	//   display: flex;
	//   justify-content: center;
	//   align-items: center;
	// }

	// .upload .u-upload .u-upload-content {
	//   display: flex;
	//   justify-content: center;
	//   align-items: center;
	//   color: #999;
	//   font-size: 28rpx;
	// }

	// .upload .u-upload:hover {
	//   background-color: #fff;
	//   border-color: #ffba4d;
	// }

	// .upload .u-upload .u-upload-button {
	//   color: #ffba4d;
	// }

	// .u-form-item {
	//   padding: 20rpx 0;
	// }

	// .u-form-item label {
	//   font-size: 28rpx;
	//   color: #333;
	//   font-weight: 500;
	// }

	// .u-form-item .u-upload {
	//   display: inline-block;
	//   width: 45%;
	//   background-color: #fff;
	//   border-radius: 8rpx;
	//   border: 1px solid #e0e0e0;
	//   padding: 20rpx;
	//   box-sizing: border-box;
	// }

	// .u-form-item .u-upload:hover {
	//   border-color: #ffba4d;
	// }
</style>