<template>
	<view class="page">
		<!-- 带背景的透明导航栏 -->
		<NavbarRoundImg title="我的"></NavbarRoundImg>
		<!-- 用户 -->
		<UserInfo :data="userInfo"></UserInfo>
		<!-- 用户数据 -->
		<view class="user-count">
			<LabelCount :ops="mineCountOps"></LabelCount>
		</view>
		<!-- 我的订单 -->
		<view class="order-nav">
			<DialNav :mode="3" shadow :list="orderNavOps" nameSize="24rpx" imgSize="72rpx">
				<TitleOperate padding="30rpx 30rpx 0 30rpx" titleWeight='800' title="我的订单" moreLabel="全部订单"
					@clickMore="$u.route({ url: '/pages-mall/pages/order/list' })"></TitleOperate>
			</DialNav>
		</view>
		<!-- 我的社区 -->
		<!-- <view class="community-nav">
			<DialNav :mode="6" shadow :list="communityNavOps" nameSize="24rpx" imgSize="72rpx">
				<TitleOperate padding="30rpx 30rpx 0 30rpx" titleWeight='800' title="我的社区"></TitleOperate>
			</DialNav>
		</view> -->
		<!-- 其他功能 -->
		<view class="other-nav">
			<DialNav marginTopLine="28rpx" :mode="8" shadow :list="otherNavOps" nameSize="24rpx" imgSize="40rpx">
				<TitleOperate padding="30rpx 30rpx 0 30rpx" titleWeight='800' title="其他服务"></TitleOperate>
			</DialNav>
		</view>
	</view>
</template>

<script>
	import NavbarRoundImg from '@/components/navbar/navbar-round-img.vue';
	import UserInfo from '@/pages/mine/components/user-info.vue';
	import LabelCount from '@/components/nav/label-count.vue';
	import DialNav from '@/components/nav/dial-nav.vue';
	import TitleOperate from '@/components/title-operate.vue';
	import {
		HTTP_URL_PROD
	} from '@/api/config';
	export default {
		components: {
			NavbarRoundImg,
			UserInfo,
			LabelCount,
			DialNav,
			TitleOperate
		},
		data() {
			return {
				isLogin: false,
				// 用户信息
				// 用户数据
				mineCountOps: [{
						label: '优惠券数量',
						count: 0,
						url: "/pages-mine/pages/discounts"
					},
					// {
					// 	label: '粉丝',
					// 	count: 15,
					// 	url: '/pages-mine/pages/fans'
					// },
					// {
					// 	label: '消息',
					// 	count: 10,
					// 	url: '/pages-mine/pages/message'
					// }
				],
				// 订单导航配置
				orderNavOps: [
					// { name: '退款售后', img: require('../../static/nav/mine-order-4.png'), url: '/pages-mall/pages/order/list?tabIndex=5' },
					{

						name: '服务订单',
						img: HTTP_URL_PROD +
							"/profile/upload/2025/05/22/tdJJSU9aX1zo0e3f23e0b661f83e3de067dd9677881c_20250522172333A086.png",
						url: '/pages-mall/pages/order/list?Tid=1'
					},
					{
						name: '跑腿订单',
						img: HTTP_URL_PROD +
							"/profile/upload/2025/05/22/tdJJSU9aX1zo0e3f23e0b661f83e3de067dd9677881c_20250522172333A086.png",
						url: '/pages-points/lingong/paotui'
					},
					{
						name: '家集市订单',
						img: HTTP_URL_PROD +
							"/profile/upload/2025/05/22/tdJJSU9aX1zo0e3f23e0b661f83e3de067dd9677881c_20250522172333A086.png",
						url: '/pages-zone/pages/store/merchant/merchant?navbarIndex=2'
					},
					// { name: '接单订单', img: require('../../static/nav/mine-order-4.png'), url: '/pages-points/lingong/jiedandd' },
				],
				//社区导航配置
				communityNavOps: [{
						name: '我的帖子',
						img: HTTP_URL_PROD +
							"/profile/upload/2025/05/22/pmixLQSU6FUq4a7e04a5f973954f5fe03df231e3827d_20250522172109A083.png",
						url: '/pages-mine/pages/community'
					},
					{
						name: '我的关注',
						img: HTTP_URL_PROD +
							"/profile/upload/2025/05/22/ER1nbJShgtn1518b8eead3883badf251476af2292110_20250522172151A084.png",
						url: '/pages-mine/pages/follow'
					},
					{
						name: '我的点赞',
						img: HTTP_URL_PROD +
							"/profile/upload/2025/05/22/ePrnuPhmO3u2e14dc3a24c5cdc7a8406766bc3045d66_20250522172226A085.png",
						url: '/pages-mine/pages/thumbsUp'
					},
					{
						name: '参与活动',
						img: HTTP_URL_PROD +
							"/profile/upload/2025/05/22/hKSfHG6AkSLY5711e481700f4c3ae6013a983ffabe35_20250522165443A082.png",
						url: '/pages-mine/pages/activity'
					},
					{
						name: '参与话题',
						img: HTTP_URL_PROD +
							"/profile/upload/2025/05/22/CiYsQeB14eC9e04f7154f6025a3355f68757c8364cea_20250522165355A081.png",
						url: '/pages-mine/pages/topic'
					}
				],
				// 其他导航配置
				otherNavOps: [
					// {
					// 	name: '使用帮助',
					// 	img: require('../../static/nav/mine-setting-1.png'),
					// 	url: ''
					// },
					// {
					// 	name: '客服电话',
					// 	img: require('../../static/nav/mine-setting-2.png'),
					// 	url: ''
					// },
					{
						name: '地址管理',
						img: require('../../static/nav/mine-setting-4.png'),
						url: '/pages-mine/pages/address/list'
					},
					{
						name: '我的优惠券',
						img: require('../../static/nav/mine-setting-5.png'),
						url: '/pages-mine/pages/discounts'
					},
					{
						name: '领取优惠券',
						img: require('../../static/nav/mine-setting-5.png'),
						url: '/pages-mine/pages/getDiscounts'
					},
					// { name: '我的收藏', img: require('../../static/nav/mine-setting-6.png'), url: '/pages-mine/pages/collection' },
					// { name: '设置', img: require('../../static/nav/mine-setting-7.png'), url: '' }
				]
			};
		},
		computed: {
			// isLogin() {
			// 	return uni.getStorageSync("App-Token") ? true : false;
			// 	// return this.$store.state.user.token ? true : false;
			// },
			userInfo: {
				get() {
					return this.$store.state.user.userInfo;
				},
				set(newValue) {
					// 如果需要响应式更新store，可以在这里提交mutation
					// this.$store.commit('updateUserInfo', newValue);
				}
			},

			roles() {
				// return this.$store.state.user.userInfo.roles.map(item => (item.roleKey))
				return (this.$store.state.user.userInfo?.roles || []).map(item => item.roleKey)
			},
			isTechnician() {
				return this.roles.includes('technician')
			},
			// 技师
			isjsTechnician() {
				return this.roles.includes('jsTechnician')
			}
		},
		mounted() {
			this.isLogin = uni.getStorageSync("App-Token") ? true : false;
			this.$eventBus.$on('order-status-changed', this.handleOrderChange);
			// 监听updateInfo事件
			uni.$on('updateInfo', (data) => {
				this.$nextTick(() => {
					this.userInfo = this.$store.state.user.userInfo;
				});
			});
			if (this.isTechnician) {
				this.orderNavOps.push({
					name: '产品订单',
					img: HTTP_URL_PROD +
						"/profile/upload/2025/05/22/tdJJSU9aX1zo0e3f23e0b661f83e3de067dd9677881c_20250522172333A086.png",
					url: '/pages-points/lingong/jiedandd'
				}, {
					name: '师傅订单',
					img: HTTP_URL_PROD +
						"/profile/upload/2025/05/22/tdJJSU9aX1zo0e3f23e0b661f83e3de067dd9677881c_20250522172333A086.png",
					url: '/pages-mall/pages/order/list?Tid=2'
				}, )
			}
			// if (this.isjsTechnician) {
			// 	console.log('角色包括技师')
			// 	// Tid 2 技师
			// 	this.orderNavOps.push({
			// 		name: '师傅接单',
			// 		img: require('../../static/nav/mine-order-4.png'),
			// 		url: '/pages-mall/pages/order/list?Tid=2'
			// 	}, )
			// }
			uni.$on('onShow', this.getCoupon);
		},
		beforeDestroy() {
			uni.$off('updateInfo');
			uni.$off('onShow', this.getCoupon);
			this.$eventBus.$off('order-status-changed', this.handleOrderChange);
		},
		methods: {
			handleOrderChange(payload) {
				this.mineCountOps[0].count = 0
				this.isLogin = false
			},
			getCoupon() {
				if (this.$store.state.user.token) {
					this.$u.api.getCouponList().then(res => {
						this.mineCountOps[0].count = res.data.length
					})
				}
			},
		}
	};
</script>

<style lang="scss" scoped>
	.page {}

	.user-count,
	.order-nav,
	.community-nav,
	.other-nav {
		position: relative;
		z-index: $app-zIndex-normal;
		padding: 0 30rpx 30rpx 30rpx;
	}

	.other-nav {
		padding-bottom: 124rpx;
	}
</style>