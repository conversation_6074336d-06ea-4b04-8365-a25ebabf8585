<template>
	<view class="slot" :class="{ shadow }">
		<view class="nav-navigation">
			<!-- 此处插槽用于扩展是否显示标题等 -->
			<slot></slot>
			<view class="nav-list" v-if="mode == 3">
				<view class="nav-item" style="width: 33.33%;" :style="[{ marginTop: index > 3 ? marginTopLine : '' }]"
					v-for="(item, index) in list" :key="index" @click="$u.route({ url: item.url, params: item.params })">
					<view class="list-img"><u-image :width="imgSize" :height="imgSize" mode="widthFix"
							:src="item[imgName]"></u-image></view>
					<view class="list-text" :style="{ fontSize: nameSize }">{{ item[labelName] }}</view>
				</view>
			</view>
			<view class="nav-list" v-if="mode == 8 || mode == 4">
				<view class="nav-item" style="width: 25%;" :style="[{ marginTop: index > 3 ? marginTopLine : '' }]"
					v-for="(item, index) in list" :key="index" @click="$u.route({ url: item.url, params: item.params })">
					<view class="list-img"><u-image :width="imgSize" :height="imgSize" mode="widthFix"
							:src="item[imgName]"></u-image></view>
					<view class="list-text" :style="{ fontSize: nameSize }">{{ item[labelName] }}</view>
				</view>
			</view>
			<view class="nav-list" v-if="mode == 6">
				<view class="nav-item" style="width: 20%;" v-for="(item, index) in list" :key="index"
					:style="[{ marginTop: index >= 5 ? marginTopLine : '' }]" @click="entanc(item)">
					<view class="list-img">
						<u-image :width="imgSize" :height="imgSize" mode="widthFix" :src="item[imgName]"></u-image>
					</view>
					<view class="list-text" :style="{ fontSize: nameSize }">{{ item[labelName] }}</view>
				</view>
			</view>
			<swiper class="nav-swiper" v-if="mode == 10" :autoplay="false" :indicator-dots="paginatedList.length > 1"
				:style="{ height: swiperHeight }" @change="onSwiperChange">
				<swiper-item v-for="(page, pageIndex) in paginatedList" :key="pageIndex">
					<view class="nav-list">
						<view class="nav-item" style="width: 20%;" v-for="(item, index) in page" :key="index"
							:style="[{ marginTop: index >= 5 ? marginTopLine : '' }]" @click="entanc(item)">
							<view class="list-img">
								<u-image :width="imgSize" :height="imgSize" mode="widthFix"
									:src="https + item.imgUrl"></u-image>
							</view>
							<view class="list-text" :style="{ fontSize: nameSize }">{{ item[labelName] }}</view>
						</view>
					</view>
				</swiper-item>
			</swiper>
		</view>
	</view>
</template>

<script>
import {
	HTTP_URL_PROD
} from '@/api/config';
export default {
	name: 'dial-nav',
	props: {
		// 显示模式
		mode: {
			type: Number,
			default: 10
		},
		// 图片的别名
		imgName: {
			type: String,
			default: 'img'
		},
		// 名称的别名
		labelName: {
			type: String,
			default: 'name'
		},
		// 跳转路径的别名
		urlName: {
			type: String,
			default: 'url'
		},
		// 配置项
		list: {
			type: Array,
			default: () => {
				return [];
			}
		},
		// 是否显示阴影
		shadow: {
			type: Boolean,
			default: false
		},
		// 图标大小
		imgSize: {
			type: String,
			default: '84rpx'
		},
		// 名称的大小
		nameSize: {
			type: String,
			default: '28rpx'
		},
		// 两行之间的距离（仅在8，10中）
		marginTopLine: {
			type: String,
			default: '30rpx'
		}
	},
	computed: {
		// 分页处理（每页最多15个，分两行）
		paginatedList() {
			const pageSize = 10;
			const pages = [];
			for (let i = 0; i < this.list.length; i += pageSize) {
				const pageData = this.list.slice(i, i + pageSize);
				pages.push(pageData);
			}
			return pages;
		},
		// 动态高度计算
		swiperHeight() {
			const currentData = this.paginatedList[this.currentPage] || [];
			const itemsPerRow = 5; // 每行 5 个（width: 20%）
			const rows = Math.ceil(currentData.length / itemsPerRow);
			const padding = 30 + 32; // 上下内边距（30rpx + 32rpx）
			return `${rows * this.rowHeight + padding}rpx`;
		},
	},
	data() {
		return {
			https: HTTP_URL_PROD,
			currentPage: 0, // 当前 swiper 页索引
			rowHeight: 160, // 单行高度（根据实际布局调整，单位 rpx）
		};
	},
	methods: {
		entanc(val) {
			// $u.route({url:item.url, params:item.params,name:item.name})
			console.log(val, 'val')
			uni.navigateTo({
				url: val.url + '?name=' + val.name + '&id=' + val.id
			})
		},
		// swiper 切换时更新当前页索引
		onSwiperChange(e) {
			this.currentPage = e.detail.current;
		},
	}
};
</script>

<style lang="scss" scoped>
.nav-swiper {
	height: 540rpx;
}

.slot {
	border-radius: 16rpx;
	// background-color: $app-theme-bg-color;
	overflow: hidden;
	// background-color: #fff;

	&.shadow {
		box-shadow: $app-theme-shadow;
	}

	.nav-navigation {
		width: 100%;

		.nav-list {
			display: flex;
			flex-wrap: wrap;
			justify-content: flex-start;
			padding-top: 30rpx;
			padding-bottom: 32rpx;

			.nav-item {
				display: flex;
				flex-wrap: wrap;
				justify-content: center;
				align-items: center;

				.list-img {
					width: 100%;
					display: flex;
					justify-content: center;
					align-items: center;
					margin-bottom: 16rpx;
				}

				.list-text {
					text-align: center;
					font-weight: 400;
					color: $app-theme-text-black-color;
				}
			}
		}
	}
}
</style>