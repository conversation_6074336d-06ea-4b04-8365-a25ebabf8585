<template>
	<view class="page">
		<Navbar title="优惠卷"></Navbar>
		<view class="listquan">
			<view class="item" v-for="(item, index) in couponList" :key="index">
				<view class="left">
					<view class="discount">
						<text>￥</text>
						<text>{{ item.amount }}</text>
					</view>
					<view class="standard">满{{ item.minUseAmount }}可用</view>
				</view>
				<view class="right">
						<view class="title">{{ item.title||'满减优惠券' }}</view>
						<view class="date">使用时间 {{ item.startTime.split(" ")[0] }} - {{ item.endTime.split(" ")[0] }}
						</view>
				</view>
				<view class="get" @click='handleGetCoupon(item.id)'>领取</view>
				<view class="select">待领取</view>
			</view>
			<view v-if="couponList.length==0" style="text-align: center;margin-top: 100rpx;">
				暂无可领取优惠券
			</view>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			couponList: [],
			selectIndex: 0,
			discountsList: [{
				title: '全场所有品类',
				endDate: '2022.10.12',
				standard: 199,
				discounts: 25
			},
			{
				title: '新人30元代金券',
				endDate: '2022.10.12',
				standard: 199,
				discounts: 30
			}
			]
		};
	},
	mounted() {
		this.getCoupon()
	},
	methods: {
		changeDiscount(item, index) {
			this.selectIndex = index;
		},
		getCoupon() {
			this.$u.api.getCouponNotList().then(res => {
				this.couponList = res.data
			})
		},
		handleGetCoupon(id, index) {
			this.selectIndex = index;
			this.$u.api.receiveCoupon(id).then(res => {
				uni.$u.toast(res.msg)
				this.getCoupon()
			})
		},
	}
};
</script>

<style lang="scss" scoped>
.listquan {

	margin: 20rpx;

	// height: calc(100vh - 40rpx - 48px);
	.item {
		width: 100%;
		height: 174rpx;
		background-image: url('@/pages-mine/static/discounts-bg.png');
		background-size: cover;
		margin-bottom: 30rpx;
		display: flex;
		justify-content: flex-start;
		align-items: flex-end;
		padding-bottom: 34rpx;
		position: relative;
		overflow: hidden;

		.left {
			width: 25%;
			display: flex;
			flex-direction: column;
			align-items: center;
			// margin-right: 60rpx;
			// margin-left: 20rpx;

			.discount {
				text:nth-child(1) {
					font-size: 22rpx;
					color: $app-theme-text-money-color;
				}

				text:nth-child(2) {
					font-size: 58rpx;
					color: $app-theme-text-money-color;
				}
			}

			.standard {
				font-size: 20rpx;
				color: $app-theme-card-gray-color;
			}
		}

		.right {
			margin-left: 15rpx;
			.title {
				font-size: 32rpx;
				color: $app-theme-text-black-color;
				margin-bottom: 32rpx;
			}

			.date {
				font-size: 20rpx;
				color: $app-theme-card-gray-color;
			}
		}

		.get {
			width: 100rpx;
			height: 40rpx;
			background-color: #f70;
			color: #fff;
			border-radius: 20rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			font-size: 20rpx;
			font-weight: bold;
			box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.08);
			position: absolute;
			right: 40rpx;
			bottom: 30rpx;
			transform: none;
		}

		.select {
			height: 40rpx;
			width: 150rpx;
			background-color: #f70;
			color: #fff;
			position: absolute;
			top: 15rpx;
			right: -30rpx;
			z-index: 10;
			display: flex;
			align-items: center;
			justify-content: center;
			font-size: 20rpx;
			font-weight: bold;
			transform: rotate(45deg);
			letter-spacing: 2rpx;
			box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.08);
		}
	}
}
</style>