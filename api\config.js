// api请求地址
export const HTTP_URL_PROD = "https://yl.shaoxing.link:1000/syjlssw_app/prod-api";
export const HTTP_URL_DEV = "https://yl.shaoxing.link:1000/syjlssw_app/prod-api";

// export const HTTP_URL_PROD = "http://115.220.3.130:1000/syjlssw_app/prod-api";
// export const HTTP_URL_DEV = "http://115.220.3.130:1000/syjlssw_app/prod-api";

// export const HTTP_URL_DEV = "http://172.26.66.37:8446";
// export const HTTP_URL_PROD = "http://172.26.66.37:8446";

// export const HTTP_URL_DEV = "http://172.26.66.48:8446";
// export const HTTP_URL_PROD = "http://172.26.66.48:8446";
import store from "../store/index.js";
// api前缀
export const HTTP_URL_DEV_PREFIX = ''
// export const HTTP_URL_DEV_PREFIX = '/prod-api'
export const HTTP_URL_PROD_PREFIX = ''
let confirmShown = false;
export const baseUrl = process.env.NODE_ENV === 'development' ? HTTP_URL_DEV : HTTP_URL_PROD

// 基于uview-ui的http配置
export const UVIEWUI_HTTP_CONFIG = {
	// 地址
	baseUrl: baseUrl,
	// 请求方式
	method: "GET",
	// 参数类型
	dataType: "json",
	// 是否显示请求中的loading
	showLoading: true,
	// 请求loading中的文字提示
	loadingText: "请求中...",
	// 在此时间内，请求还没回来的话，就显示加载中动画，单位ms
	loadingTime: 800,
	// 是否在拦截器中返回服务端的原始数据
	originalData: false,
	// 展示loading的时候，是否给一个透明的蒙层，防止触摸穿透
	loadingMask: true,
};

// 此处配置请求拦截器
export const httpRequest = (config) => {
	const token = uni.getStorageSync("App-Token");
	config.header = {
		Authorization: token,
	};
	return config;
};

// 此处配置响应拦截器
export const httpResponse = (res) => {
	const token = uni.getStorageSync("App-Token");

	if (res.code == 401) {
		if (!confirmShown) {
			confirmShown = true;

			let message = token ? "登录信息已过期，是否重新登录" : "您还未登录，前往登录";

			store.dispatch("user/LogOut").then(() => {
				uni.showModal({
					title: "提示",
					content: message,
					cancelText: "取消",
					confirmText: "确定",
					success: function(res) {
						if (res.confirm) {
							uni.reLaunch({
								url: "/pages/login/index",
							});
						}
						confirmShown = false;
					},
				});
			});
		}
		return res;
	} else if (res.code == 500) {
		return res;
	} else if (res.code == 0 || res.code == 200) {
		return res;
	}
};

// http安装方法
export const installHttpConfig = (Vue, vm) => {
	Vue.prototype.$u.http.setConfig(UVIEWUI_HTTP_CONFIG);
	Vue.prototype.$u.http.interceptor.request = httpRequest;
	Vue.prototype.$u.http.interceptor.response = httpResponse;
};