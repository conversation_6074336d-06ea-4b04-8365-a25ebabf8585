export const useAddressApi = (Vue, vm) => {
	return {
		// 获取收货地址
		getAddressList: () => vm.$u.get('common/deliveryAddress/list',{
			pageSize: 99999,
			pageNum: 1,
			userId: vm.$store.state.user.userInfo.userId
		}),
		// 新增收货地址
		addAddress: (data) => vm.$u.post('common/deliveryAddress',data),
		// 修改收货地址
		updateAddress: (data) => vm.$u.put('common/deliveryAddress',data),
		// 删除收货地址
		deleteAddress: (id) => vm.$u.delete('common/deliveryAddress/' + id),
		// 查询收货地址
		detailAddress: (id) => vm.$u.get('common/deliveryAddress/' + id),
		// 获取地址选项信息
		getAddressOption: () => vm.$u.get('administrative/tree',{
			pageSize: 99999,
			pageNum: 1
		})
	}
}