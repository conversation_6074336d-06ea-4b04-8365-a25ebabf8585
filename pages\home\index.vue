<template>
	<view class="page">
		<!-- 背景 -->
		<!-- 带城市、搜索框的导航栏 -->
		<navbar-city-search placeholder="搜索商品、品牌" :cityListSelected="nowSelectedCity"></navbar-city-search>
		<view v-if="phoneType=='h5'" style="height: 55px;"></view>
		<view class="swiper">
			<u-swiper :list="list"></u-swiper>
		</view>
<!-- 		<view class="example-body">
			<u-upload ref="uUpload" :header="header" :action="action" :file-list="avatarImages" :max-count="1"
				@on-change="upChange1" upload-text="点击上传" />
		</view> -->
		<!-- 金刚区 -->
		<view class="dial-nav"><dial-nav marginTopLine='22rpx' imgSize='84rpx' :list="dialNavList"></dial-nav></view>
		<!-- 专区 -->
		<ZoneGrid :list="zoneList" />
		<!-- 横向滚动列表 -->
		<!-- <horizontal-scroll-nav :list="horizontalScrollNavList"></horizontal-scroll-nav> -->
		<!-- 推荐商品 -->
		<!-- 		<view class="recommendGoods">
			<TitleOperate title="推荐商品" showMore></TitleOperate>
			<view class="list">
				<u-waterfall ref="topicWaterFall" v-model="recommendGoodsList" marginLeft="7rpx" marginRight="7rpx">
					<template v-slot:left="{ leftList }">
						<CardGoods v-for="(item, index) in leftList" :key="index" :data="item"></CardGoods>
					</template>
					<template v-slot:right="{ rightList }">
						<CardGoods v-for="(item, index) in rightList" :key="index" :data="item"></CardGoods>
					</template>
				</u-waterfall>
			</view>
		</view> -->
		<HotList :data="HotList" :hid="hid" :hnage="hnage" :hurl="hurl"></HotList>
		<HotList :data="HotTList" :hid="hidT" :hnage="hnageT" :hurl="hurlT"></HotList>
		<Store />
		<!-- 精选晒单 -->
		<!-- <view class="recommendTopic ">

			<TitleOperate title="精选晒单"></TitleOperate>
			<view class="list">

				<u-waterfall ref="topicWaterFall" v-model="recommendTopicList" marginLeft="7rpx" marginRight="7rpx">
					<template v-slot:left="{ leftList }">
						<PostCardSimple v-for="(item, index) in leftList" :key="index" :data="item"></PostCardSimple>
					</template>
					<template v-slot:right="{ rightList }">
						<PostCardSimple v-for="(item, index) in rightList" :key="index" :data="item"></PostCardSimple>
					</template>
				</u-waterfall>
			</view>
		</view> -->
		<!-- 店铺消息 -->
		<!-- 精选服务 -->
		<view class="selectedServices">
			<!-- 带操作的标题组件 -->
			<TitleOperate title="精选服务"></TitleOperate>
			<!-- <view class="list"> -->
			<!-- 瀑布流组件 -->
			<!-- 				<u-waterfall ref="topicWaterFall" v-model="recommendGoodsList" marginLeft="7rpx" marginRight="7rpx">
					<template v-slot:left="{ leftList }">
						<CardGoods v-for="(item, index) in leftList" :key="index" :data="item"></CardGoods>
					</template>
					<template v-slot:right="{ rightList }">
						<CardGoods v-for="(item, index) in rightList" :key="index" :data="item"></CardGoods>
					</template>
				</u-waterfall> -->
			<SelectedServices :data="selectedList"></SelectedServices>
			<!-- </view> -->
		</view>
	</view>
</template>

<script>
	const token = uni.getStorageSync("App-Token");
	import {
		HTTP_URL_PROD
	} from '@/api/config';
	// 导入组件1
	import NavbarCitySearch from '@/components/navbar/navbar-city-search.vue';
	import DialNav from '@/components/nav/dial-nav.vue';
	// import HorizontalScrollNav from '@/components/nav/horizontal-scroll-nav.vue';
	import CardGoods from '@/pages/mall/components/card.vue';
	import PostCardSimple from '@/pages/community/components/post-card-simple.vue';
	import TitleOperate from '@/components/title-operate.vue';
	import ZoneGrid from './components/shop/zone-grid.vue'
	import Store from './components/store-index.vue'
	import SelectedServices from '@/components/selected-services.vue'
	import HotList from '@/components/hot-list.vue'
	// 导入假数据1
	// import {
	// 	goodsList,
	// 	topicList,
	// 	dialNavListHome,
	// 	horizontalScrollNavList
	// } from '@/static/test-data.js';
	export default {
		components: {
			NavbarCitySearch,
			DialNav,
			// HorizontalScrollNav,
			CardGoods,
			SelectedServices,
			PostCardSimple,
			TitleOperate,
			Store,
			ZoneGrid,
			HotList
		},
		data() {
			return {
				header: {
					Authorization: this.$store.state.user.token
				},
				action: HTTP_URL_PROD + '/common/upload',
				phoneType: process.env.UNI_PLATFORM,
				avatarImages: [], // 简历头像
				list: [],
				hurl: '',
				hnage: '',
				hid: '',
				hurlT: '',
				hnageT: '',
				hidT: '',
				// 热门商品列表
				HotList: [],
				HotTList: [],
				// 精选服务列表
				selectedList: [],
				count: 4,
				value: 2,
				// 金刚区
				// dialNavList: dialNavListHome,
				dialNavList: null,
				// 横向滚动列表
				// horizontalScrollNavList: horizontalScrollNavList,
				horizontalScrollNavList: null,
				// 由城市选择页面返回的选择城市数据
				nowSelectedCity: '',
				// 推荐商品列表
				recommendGoodsList: [],
				// 精选晒单列表
				recommendTopicList: [],
				// 默认第一个
				selectedIndex: 0,
				zoneList: [
					// {
					// 	img: HTTP_URL_PROD +
					// 		"/profile/upload/2025/05/22/DEjF3WtCb6IBc80588bba9f89e019b3962fc019677eb_20250522150200A056.png",
					// 	title: '商城专区',
					// 	name: '身边人都在用的商品',
					// 	url: ''
					// },
					// {
					// 	img: HTTP_URL_PROD +
					// 		"/profile/upload/2025/05/22/AcWjtmZJ5RStfddaed83ff2eb39f0411ef5b1e5010ad_20250522151856A062.png",
					// 	title: '邻工入住',
					// 	name: '邻工入住申请',
					// 	url: '/pages-zone/pages/store/settled/shop-settled2'
					// },
					// {
					// 	img: HTTP_URL_PROD +
					// 		"/profile/upload/2025/05/22/fl2ySo4dSwcocb7de51f1cd50e72991c70c3c6cce461_20250522152136A063.png",
					// 	title: '商家入驻',
					// 	name: '消息享积分，0元购',
					// 	url: '/pages-zone/pages/store/settled/shop-settled'
					// },
					// {
					// 	img: HTTP_URL_PROD +
					// 		"/profile/upload/2025/05/22/BHvrUI4lvSkic378ea43827d119d5b35a9517191a223_20250522152151A064.png",
					// 	title: '师傅入住',
					// 	name: '师傅入住申请',
					// 	url: '/pages-zone/pages/Techniciancheckin/index'
					// },
					{
						img: HTTP_URL_PROD +
							"/profile/upload/2025/05/22/tGhoSZuN9pu94bb9b8309ec1c13e58a80a01086b3054_20250522152211A065.png",
						title: '优惠专区',
						name: '千万好物，轻松下单',
						url: '/pages-mine/pages/getDiscounts'
					},
					{
						img: HTTP_URL_PROD +
							"/profile/upload/2025/05/22/VwlgHf86xnQxac916cfd74b230953a1f04e5bb0797c6_20250522152224A066.png",
						title: '本地商家',
						name: '商家入住申请',
						url: '/pages-zone/pages/store/merchant/merchant'
					},
				]
			};
		},
		onShow() {

		},
		mounted() {
			let that = this;
			uni.$on('onShow', function(data) {
				that.nowSelectedCity = that.$store.state.user.locationCom || uni.getStorageSync('community')
				that.loadPageData(false);
			});
		},
		methods: {
			async upChange1(res, index, lists, name) {
				//   try {
				// 	const res = await this.$u.api.uploadImage(file.url); // 注意这里传的是本地路径
				// 	if (res.code === 200) {
				// 	  const imageUrl = res.data.url; // 服务器返回的图片线上地址

				// 	  console.log('上传成功，图片地址：', imageUrl);
				// 	  this.form.orderImageUrls.push({ url: imageUrl }); 

				// 	} else {
				// 	  uni.showToast({ title: res.message || '上传失败', icon: 'none' });
				// 	}
				//   } catch (err) {
				// 	console.error('上传异常', err);
				// 	uni.showToast({ title: '上传失败', icon: 'none' });
				//   }
			},
			getJGQlistData() {
				let data = {
					pageNum: 1,
					pageSize: 999,
					homePageShow: 2,
				}
				this.$u.api.getJGQlistData(data).then(res => {
					const newData = res.data.list.map(item => {
						if (item.type == 1) {
							return {
								...item,
								url: '/pages-zone/pages/store/merchant/merchant-detail?id=' + item.id  + '&title=' + item.name,
							}
						} else {
							return {
								...item,
								url: '/pages-mall/pages/indexdetails/index'
							}
						}
					});
					this.dialNavList = newData
					this.funPopular(newData[2])
					this.funPopularT(newData[7])
				})
			},
			funPopular(val) {
				let data = {
					pageNum: 1,
					pageSize: 10,
					parentId: val.id
				}
				this.$u.api.getJGQlistData(data).then(res => {
					this.getPopularlist(res.data.list[0].id)
					this.hid = val.id
					this.hnage = val.name
					this.hurl = val.url

				})
			},
			funPopularT(val) {
				let data = {
					pageNum: 1,
					pageSize: 10,
					parentId: val.id
				}
				this.hidT = val.id
				this.hnageT = val.name
				this.hurlT = val.url
				this.$u.api.getJGQlistData(data).then(res => {
					this.getPopularTlist(res.data.list[1].id)
				})
			},
			getPopularlist(id) {
				let data = {
					type: id
				}
				this.$u.api.getlistgoods(data).then(res => {
					this.HotList = res.rows
				})
			},
			getPopularTlist(id) {
				let data = {
					type: id
				}
				this.$u.api.getlistgoods(data).then(res => {
					this.HotTList = res.rows
				})
			},
			funlistgoods() {
				let data = {
					type: 22
				}
				this.$u.api.getlistgoods(data).then(res => {
					this.selectedList = res.rows
				})
			},
			// tab单选
			selectItem(index) {
				this.selectedIndex = index;
			},
			getBannerList() {
				this.$u.api.listBanner({
					isVisible: true
				}).then((res) => {
					if (res.code == 200) {
						this.list = res.data;
						this.list = this.list.map((item) => {
							item.image = this.$imgUrl + item.imageInput;
							return item;
						});
					}
				});
			},
			// 首页需要渲染的数据
			loadPageData(isAll = true) {
				setTimeout(() => {
					this.getBannerList()
					this.getRecommendGoods();
					this.getRecommendTopic();
					this.getJGQlistData()
					this.funlistgoods()
				}, 0);
			},

			// 加载推荐商品
			getRecommendGoods() {
				this.$nextTick(() => {
					this.recommendGoodsList = null
				});
			},

			// 加载精选帖子
			getRecommendTopic() {
				this.$nextTick(() => {
					this.recommendTopicList = null
				});
			}
		}
	};
</script>

<style lang="scss" scoped>
	.page {
		background: linear-gradient(0deg, #ffd9a400 70%, #ffd69c);
	}

	.swiper {
		padding: 0 24rpx;
	}

	.home_bj {
		position: absolute;
		width: 100vh;

		image {
			width: 100%;
		}
	}

	.list {
		display: flex;
		flex-wrap: wrap;
		justify-content: space-between;
		padding: 0 30rpx;
	}

	.dial-nav {
		padding: 24rpx;
	}

	.selectedServices {
		padding-bottom: 124rpx;
		background-color: #fff;

		.tabs {
			background: #fff;
			display: flex;
			padding: 28rpx;
			justify-content: space-between;
			color: #999;

			.button-item {}

			.selected {
				font-weight: bold;
				color: #000;
			}
		}
	}
</style>