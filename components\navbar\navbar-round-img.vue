<template>
	<view class="slot">
		<u-navbar :back-icon-name="backIconName" :border-bottom="false" :is-back="isBack"
			:title-color="titleColor || appThemeTextWhiteColor" :back-icon-size="32"
			:back-icon-color="backIconColor || appThemeTextWhiteColor" :title="title" :background="background"
			title-color="#000"></u-navbar>

		<view class="bg-img" :style="{ height }">

			<!-- <u-image width="100%" :src="bgImg" mode="widthFix"></u-image> -->

		</view>
	</view>
</template>

<script>
	import {
		HTTP_URL_PROD
	} from '@/api/config';
	export default {
		name: 'navbar-round-img',
		props: {
			// 标题
			title: {
				type: String,
				default: '标题'
			},
			// 背景图
			bgImg: {
				type: String,
				default: HTTP_URL_PROD +
					"/profile/upload/2025/05/22/HYm8cDWotlfC8e0fd53f28429c278513305d3c56dac1_20250522162849A080.png"
			},
			// 是否带返回按钮
			isBack: {
				type: Boolean,
				default: false
			},
			// 返回图标名称
			backIconName: {
				type: String,
				default: 'arrow-leftward'
			},
			// 背景色
			backgroundColor: {
				type: String,
				default: null
			},
			// 高度
			height: {
				type: String,
				default: '36vh'
			},
			// 标题颜色
			titleColor: {
				type: String,
				default: null
			},
			// 返回按钮颜色
			backIconColor: {
				type: String,
				default: null
			}
		},
		data() {
			return {
				appThemeTextWhiteColor: this.$appTheme.appThemeTextWhiteColor,
				appThemeColor: this.$appTheme.appThemeColor,
				background: {
					backgroundColor: "#ffd69c",
				}
			};
		}
	};
</script>

<style lang="scss" scoped>
	.bg-img {
		position: absolute;
		top: 0;
		left: -1rpx;
		right: 0;
		bottom: 0;
		z-index: $app-zIndex-deep;
		width: 101%;
		overflow: hidden;
		background: linear-gradient(0deg, #ffd9a414 10%, #ffc572);
	}
</style>