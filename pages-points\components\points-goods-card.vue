<template>
	<view class="slot" @click="goGoods()">
		<view class=""><u-image width="100%" height="316rpx" :src="data.img"></u-image></view>
		<view class="title">{{ data.title }}</view>
		<view class="price">
			<view class="points">
				<u-image style="margin-right: 4rpx;" width="32rpx" height="32rpx" :src="points"></u-image>
				<text>{{ data.points }}</text>
			</view>
			<view class="money">+{{ data.extraMoney }}元</view>
		</view>
	</view>
</template>

<script>
	import {
		HTTP_URL_PROD
	} from '@/api/config';
export default {
	name: 'points-goods-card',
	props: {
		data: {
			type: Object,
			default: () => {
				return {};
			}
		}
	},
	data() {
		return {
			points: HTTP_URL_PROD +
				"/profile/upload/2025/05/22/zaBim4GeKeV947dceb430e90c31b6a482218cb595d08_20250522181054A094.png"
	
		}
	},
	methods: {
		// 去商品页
		goGoods() {
			uni.navigateTo({
				url: '/pages-mall/pages/goods/detail?goodsType=points'
			});
		}
	}
};
</script>

<style lang="scss" scoped>
.slot {
	.title {
		font-size: 28rpx;
		color: $app-theme-text-black-color;
		padding: 12rpx 22rpx;
		overflow: hidden;
		white-space: nowrap;
		text-overflow: ellipsis;
	}
	.price {
		padding: 0rpx 22rpx 24rpx 22rpx;
		display: flex;
		align-items: flex-end;
		.points {
			display: flex;
			align-items: flex-end;
			margin-right: 8rpx;
			text {
				font-size: 32rpx;
				color: $app-theme-text-money-color;
				line-height: 1;
			}
		}
		.money {
			font-size: 24rpx;
			color: $app-theme-card-gray-deep-color;
			line-height: 1;
		}
	}
}
</style>
