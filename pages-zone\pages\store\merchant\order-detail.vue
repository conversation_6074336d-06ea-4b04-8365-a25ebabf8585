<template>
	<view class="order-detail">
		<navbar title="订单详情"></navbar>
		<view class="status">
			{{formatOrderStatus(order.orderStatus)}}
		</view>
		<view class="cart-list">
			<view class="title">
				{{shopName}}
			</view>
			<view class="cart" v-for="item in storeList" :key="item.id">
				<view class="left">
					<view class="img">
						<img style="width: 160rpx;height: 160rpx;" :src="baseUrl + item.img" alt="" />
					</view>
					<view class="xq">
						<view class="name">
							{{item.name}}
						</view>
						<view class="count">
							X{{item.count}}
						</view>
					</view>
				</view>
				<view class="right">
					￥{{item.price * item.count}}
				</view>
			</view>
		</view>
		<view class="pay">
			<view class="detail-item">
				<view class="title">店铺优惠券</view>
				<view class="data">-￥{{order.amount?order.amount:0}}</view>
			</view>
			<view class="detail-item">
				<view class="title">共{{count}}件商品</view>
				<view class="data">￥{{sell}}</view>
			</view>
			<!-- <view class="detail-item">
				<view class="title">打包费</view>
				<view class="data">￥0</view>
			</view> -->
			<view class="detail-item" v-if="order.addressId">
				<view class="title">配送服务费</view>
				<view class="data">￥{{storeList[0].peiSong}}</view>
			</view>
			<view class="detail-item">
				<view class="title">实付</view>
				<view class="data">￥{{order.totalAmount}}</view>
			</view>
		</view>
		<view class="song" v-if="order.addressId">
			<view class="detail-item">
				<view class="title">期望时间</view>
				<view class="data">{{order.reservationTime ? order.reservationTime : '立刻配送'}}</view>
			</view>
			<view class="detail-item">
				<view class="title">配送地址</view>
				<view class="data">{{order.address.substr(4) }}（{{storeList[0].phone}}）</view>
			</view>
			<view class="detail-item">
				<view class="title">配送方式</view>
				<view class="data">{{order.deliveryFlag ? '自提' : '家事快送'}}</view>
			</view>
			<view class="detail-item">
				<view class="title">商家电话</view>
				<view class="data">{{storeList[0].phone}}<text style="color: #0da1fd;margin-left: 10rpx;">联系他</text></view>
			</view>
		</view>
		<view class="song" v-else>
			<view class="detail-item">
				<view class="title">自提时间</view>
				<view class="data">{{order.reservationTime ? order.reservationTime : '立刻自提'}}</view>
			</view>
			<view class="detail-item">
				<view class="title">配送方式</view>
				<view class="data">门店自提</view>
			</view>
			<view class="detail-item">
				<view class="title">商家电话</view>
				<view class="data">{{storeList[0].phone}}<text style="color: #0da1fd;margin-left: 10rpx;">联系他</text></view>
			</view>
		</view>
		<view class="order">
			<view class="detail-item">
				<view class="title">订单号码</view>
				<view class="data" style="width: 360rpx;">{{order.orderNo}}</view>
			</view>
			<view class="detail-item">
				<view class="title">下单时间</view>
				<view class="data">{{order.createTime}}</view>
			</view>
			<view class="detail-item">
				<view class="title">收货时间</view>
				<view class="data">{{order.confirmReceiptTime ? order.confirmReceiptTime : '未收货'}}</view>
			</view>
			<view class="detail-item">
				<view class="title">备注</view>
				<view class="data">{{order.remark ? order.remark : '无'}}</view>
			</view>
		</view>
	</view>
</template>

<script>
	import { baseUrl } from '@/api/config'
	export default {
		data() {
			return {
				order: {},
				count: 0,
				sell: 0,
				shopName:'',
				storeList:[],
				baseUrl,
			}
		},
		onLoad({orderNo}) {
			this.$u.api.getOrderDetail(orderNo).then(res=>{
				this.order = res.data[0]
				this.shopName = Object.keys(this.order?.orderDetailList)[0]
				this.storeList = this.order?.orderDetailList[this.shopName]
				let sum = 0
				let count = 0
				this.storeList.forEach(item=>{
					sum += item.price * item.count
					count += item.count
				})
				this.count = count
				this.sell = sum
			})
		},
		methods: {
			formatOrderStatus(status){
				if(status == 0){
					return '待支付'
				}else if (status == 1){
					return '已预约'
					// return '已支付'
				}else if (status == 2){
					return '已取消'
				}else if (status == 3){
					return '已退款'
				}
			}
		}
	}
</script>

<style scoped lang="scss">
.status{
	height: 300rpx;
	background: #f78708;
	width: 100%;
	color: #fff;
	font-weight: 800;
	font-size: 50rpx;
	display: flex;
	align-items: center;
	padding-left: 30rpx;
	padding-bottom: 50rpx;
}

.cart-list{
	padding: 20rpx;
	.title{
		font-weight: 800;
		margin-bottom: 10rpx;
		font-size: 34rpx;
	}
	
	.cart{
		display: flex;
		justify-content: space-between;
		align-items: flex-end;
		.left{
			display: flex;
			.img{
				margin-right: 20rpx;
			}
			
			.xq{
				display: flex;
				justify-content: space-around;
				flex-direction: column;
			}
		}
		.right{
			color: red;
		}
	}
}

.order, .cart-list, .pay, .song{
	background-color: #fff;
	border-radius: 10rpx;
	width: 90%;
	margin: 0 auto;
	margin-bottom: 20rpx;
	transform: translateY(-100rpx);
	
	.detail-item{
		display: flex;
		justify-content: space-between;
		align-items: center;
		height: 80rpx;
		font-size: 34rpx;
		padding: 0 30rpx;
		border-bottom: 1px solid #f8f8f8;
		
		.data{
			font-size: 24rpx;
			font-weight: 800;
		}
		
		&:last-child{
			border-bottom:none;
		}
	}
}
</style>
