<template>
  <view class="container">
	  <!-- 顶部导航 -->
	  <view class="navbar">
	   <Navbar title="抢单成功" />
	  </view>
	  
    <view class="card">
      <!-- 标题区 -->
      <view class="header">
        <text class="tag">{{ order.tag }}</text>
        <!-- <text class="time">{{ formattedTime }}</text> -->
      </view>

      <view class="divider" />

      <!-- 描述区 -->
      <view class="section">
        <text class="section-title">订单描述 : </text>
        <text class="desc">{{ order.desc }}</text>
      </view>

      <!-- 金额区 -->
      <view class="section">
        <text class="section-title">赏金金额 : </text>
        <text class="price">¥ {{ order.price }}</text>
      </view>

      <!-- 返回按钮 -->
      <u-button class="btn" type="primary" @click="goBack">
        返回列表
      </u-button>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      order: {
        tag: '',
        time: '',
        desc: '',
        price: 0
      },
      formattedTime: ''
    };
  },
  // Uni-app 页面生命周期：接收跳转参数
  onLoad(options) {
    // 解码并赋值
    const desc = decodeURIComponent(options.desc || '');
    const tag = decodeURIComponent(options.tag || '');
    const price = options.price || 0;
    const time = options.time || '';

    this.order = { desc, tag, price, time };
    // 格式化一下时间显示
    this.formattedTime = time
      ? new Date(time).toLocaleString()
      : '';
  },
	methods: {
	  goBack() {
		uni.navigateTo({
		  url: '/pages-points/lingong/jiedandd'  // 注意：不要加 .vue 后缀
		});
	  }
	}

};
</script>

<style scoped>
	/* 头部 */
	.navbar {
	  position: fixed;
	  top: 0;
	  left: 0;
	  right: 0;
	  height: 44px;
	  background: #fff;
	  z-index: 10;
	}
	
.container {
  padding: 32rpx;
  background: #f5f5f5;
  min-height: 100vh;
}
.card {
  margin-top: 100px;
  background: #ffffff;
  border-radius: 12rpx;
  padding: 32rpx;
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.05);
}
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}
.tag {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
}
.time {
  font-size: 24rpx;
  color: #999999;
}
.divider {
  height: 1rpx;
  background: #eeeeee;
  margin: 0 -32rpx 24rpx;
}
.section {
  margin-bottom: 24rpx;
}
.section-title {
  font-size: 28rpx;
  color: #666666;
  margin-bottom: 12rpx;
}
.desc {
  padding-left: 15px;
  font-size: 30rpx;
  color: #333333;
  line-height: 44rpx;
}
.price {
  padding-left: 15px;
  font-size: 36rpx;
  color: #ff4a4a;
  font-weight: bold;
}
.btn {
  margin-top: 32rpx;
  width: 100%;
  border-radius: 8rpx;
}
</style>
