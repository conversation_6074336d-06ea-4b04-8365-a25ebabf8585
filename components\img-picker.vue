<template>
	<view>
		<u-upload :action="baseUrl" @on-remove="deleteImg"  :header="header" @on-success="getImg" :max-count="limit" ></u-upload>
	</view>
</template>
<script>
	import {
		getToken
	} from '@/utils/auth';
	export default {
		name:"img-picker",
		data() {
			return {
				baseUrl: this.$imgUrl + '/common/upload',
				imgs: [],
				imgPaths: {},
				header:{
					'Authorization':getToken()
				}
			};
		},
		props: {
			url: {
				type: String,
				default: '/common/upload'
			},
			types: {
				type: Number,
				default: 0
			},
			mediatype: {
				type: String,
				default: 'image'
			},
			limit: {
				type: Number,
				default: 9
			},
		},
		methods: {
			uploadImg(file) {},
			uploadInfo(file) {},
			getImg(e){
				this.imgs.push(e.fileName)
				this.$emit('imglist', this.imgs)
			},
			deleteImg(e) {
				console.log(e)
				this.imgs.splice(e, 1);
				this.$emit('imglist', this.imgs)				
			},
		},

	}
</script>

<style scoped lang="scss">

</style>